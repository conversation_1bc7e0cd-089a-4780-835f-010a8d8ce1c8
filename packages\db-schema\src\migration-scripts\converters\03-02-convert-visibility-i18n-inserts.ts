import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class VisibilityI18nMigrationConverter extends BaseConverter {
  private visibilityI18nMappings: Mapping[] = [];

  private convertToPostgres(
    record: MySQLI18NDescription,
    visibilityIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    const newVisibilityId = visibilityIdMappings[record.data_id.toString()];
    if (!newVisibilityId) {
      throw new Error(`No mapping found for visibility_id: ${record.data_id}`);
    }

    this.visibilityI18nMappings.push({
      mysqlId: record.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newVisibilityId,
      locale: record.locale,
      name: record.nom,
      description: record.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for visibilities_i18n table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'visibilite_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No visibilities_i18n INSERT statements found.');
        return;
      }

      // Load visibility ID mappings
      const visibilityIdMappings =
        await this.loadEntityIdMappings('visibilite');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, visibilityIdMappings);
        });
        allPostgresRecords.push(...postgresRecords);
      }

      // Generate output
      const postgresInserts = this.generatePostgresI18NInsert(
        allPostgresRecords,
        'visibilities_i18n',
        'Visibility I18n Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.visibilityI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'visibilite_trad', postgres: 'visibility_i18n' },
        idMappings,
      );

      console.log('Visibility I18n migration completed successfully.');
    } catch (error) {
      console.error('Error during visibility I18n migration:', error);
      throw error;
    }
  }
}
