import * as Schema from 'effect/Schema';
import { PermissionActionSchema } from './permissions.schema';
import { ResourceTypeSchema } from './user-permissions.schema';

export const SessionDataSchema = Schema.Struct({
  id: Schema.String,
  token: Schema.String,
  userId: Schema.String,
  expiresAt: Schema.Date,
  createdAt: Schema.Date,
  updatedAt: Schema.Date,
  ipAddress: Schema.NullishOr(Schema.String),
  userAgent: Schema.NullishOr(Schema.String),
});

export const UserSessionSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  emailVerified: Schema.Boolean,
  email: Schema.String,
  createdAt: Schema.Date,
  updatedAt: Schema.Date,
  image: Schema.NullishOr(Schema.String),
});

export const SessionPermissionsSchema = Schema.Struct({
  globalPermissions: Schema.Array(
    Schema.Struct({
      domain: ResourceTypeSchema,
      action: PermissionActionSchema,
    }),
  ),
  address: Schema.Array(Schema.String),
  applicationSector: Schema.Array(Schema.String),
  building: Schema.Array(Schema.String),
  campus: Schema.Array(Schema.String),
  equipment: Schema.Array(Schema.String),
  excellenceHub: Schema.Array(Schema.String),
  fundingProject: Schema.Array(Schema.String),
  infrastructure: Schema.Array(Schema.String),
  innovationLab: Schema.Array(Schema.String),
  institution: Schema.Array(Schema.String),
  media: Schema.Array(Schema.String),
  people: Schema.Array(Schema.String),
  researchField: Schema.Array(Schema.String),
  room: Schema.Array(Schema.String),
  serviceContract: Schema.Array(Schema.String),
  serviceOffer: Schema.Array(Schema.String),
  technique: Schema.Array(Schema.String),
  unit: Schema.Array(Schema.String),
  user: Schema.Array(Schema.String),
  userRole: Schema.Array(Schema.String),
  vendor: Schema.Array(Schema.String),
  visibility: Schema.Array(Schema.String),
  documentationCategory: Schema.Array(Schema.String),
  equipmentCategory: Schema.Array(Schema.String),
  equipmentStatus: Schema.Array(Schema.String),
  equipmentType: Schema.Array(Schema.String),
  fundingProjectType: Schema.Array(Schema.String),
  fundingProjectIdentifierType: Schema.Array(Schema.String),
  infrastructureStatus: Schema.Array(Schema.String),
  infrastructureType: Schema.Array(Schema.String),
  institutionType: Schema.Array(Schema.String),
  mediaType: Schema.Array(Schema.String),
  peopleRoleType: Schema.Array(Schema.String),
  roomCategory: Schema.Array(Schema.String),
  unitType: Schema.Array(Schema.String),
});

export const SessionSchema = Schema.Struct({
  roles: Schema.Array(Schema.String),
  permissions: SessionPermissionsSchema,
  session: SessionDataSchema,
  user: UserSessionSchema,
});
