import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { buildingFormSections } from '@/constants/bottin/building';
import { getQueryClientOptions } from '@/constants/query-client';
import { getGenericByIdOptions } from '@/hooks/bottin/generic-list.options';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import { BuildingEditToFormSchema } from '@rie/domain/serializers';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import * as Schema from 'effect/Schema';
import { notFound } from 'next/navigation';
import { EditionBuildingPage } from './edit-building-page';

type EditBuildingPageParams = PageDetailsParams;
export default async function EditBuildingPage(props: EditBuildingPageParams) {
  const params = await props.params;

  const { id, locale } = params;

  const formSections = await getFormSections({
    resourceName: 'buildings',
    sections: buildingFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const t0 = performance.now();

  const controlledLists: ControlledListKey[] = ['campus', 'organisation'];

  const building = await Promise.all([
    queryClient.fetchQuery(
      getGenericByIdOptions({
        controlledListKey: 'building',
        id,
        view: 'edit',
      }),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "organization", "campus" and "building" took ${t1 - t0} milliseconds.`,
  );

  if (!building) {
    return notFound();
  }

  // Transform the building edit data to form schema
  const formData = Schema.decodeSync(BuildingEditToFormSchema)(building[0]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionBuildingPage
        formSections={formSections}
        id={id}
        initialData={formData}
      />
    </HydrationBoundary>
  );
}
