'use client';
import { PersonForm } from '@/app/[locale]/bottin/personnes/form/person-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useGetGenericById } from '@/hooks/bottin/generic-list.hook';
import { useUpdatePerson } from '@/hooks/bottin/people.hook';
import type { PersonFormSchema } from '@/schemas/bottin/person-form-schema';
import type { PersonFormSectionKey } from '@/types/bottin/person';
import type { SupportedLocale } from '@/types/locale';
import { PersonEditToFormSchema } from '@rie/domain/serializers';
import * as Schema from 'effect/Schema';

type EditionPersonPageParams = {
  formSections: Record<PersonFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionPersonPage({
  formSections,
  id,
}: EditionPersonPageParams) {
  const {
    data: person,
    error,
    isPending,
  } = useGetGenericById<'person', 'edit'>({
    controlledListKey: 'person',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Erreur : {error.message}</div>;
  }

  const updatePerson = useUpdatePerson();
  const onSubmit = async (data: PersonFormSchema) => {
    await updatePerson.mutateAsync({ id, payload: data });
  };

  // Transform person edit data to form schema using serializer
  const formData = Schema.decodeSync(PersonEditToFormSchema)(
    person,
  ) as PersonFormSchema;

  return (
    <PersonForm
      defaultValues={formData}
      formSections={formSections}
      onSubmit={onSubmit}
    />
  );
}
