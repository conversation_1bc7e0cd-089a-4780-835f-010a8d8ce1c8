'use client';
import { Description } from '@/app/[locale]/contrats-service/[id]/sections/description';
import Header from '@/app/[locale]/contrats-service/[id]/sections/header';
import { EquipmentGrid } from '@/app/[locale]/equipements/equipment-grid';
import { hasElementsToDisplay } from '@/components/details-section-body/helpers';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGetServiceContractById } from '@/hooks/contract-service/useGetContractServiceById';
import type { IdType } from '@/types/bottin/project';
import { EquipmentStructureToEquipmentListSchema } from '@rie/domain/serializers';
import type { EquipmentList } from '@rie/domain/types';
import * as Schema from 'effect/Schema';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';

export const Details = () => {
  const t = useTranslations('contractService.details.sections');
  const { id } = useParams<IdType>();
  const {
    data: serviceContractDetails,
    error,
    status,
  } = useGetServiceContractById(id);
  if (status === 'pending') {
    return null; // or render a loading state
  }

  if (status === 'error') {
    // Need to handle Errors properly
    return <div>{error.message}</div>;
  }

  const { coveredEquipments, description, headerDescription } =
    serviceContractDetails;

  // Transform EquipmentStructure[] to EquipmentList[] using serializer
  const transformedEquipments: EquipmentList[] = coveredEquipments.map(
    (equipment) =>
      Schema.decodeSync(EquipmentStructureToEquipmentListSchema)(
        equipment,
      ) as EquipmentList,
  );

  return (
    <div className="w-full">
      <Card>
        <CardHeader>
          {hasElementsToDisplay(headerDescription) ? (
            <Header {...headerDescription} />
          ) : null}
        </CardHeader>
        <CardContent>
          <Tabs className="w-full" defaultValue="description">
            <TabsList>
              {hasElementsToDisplay(description) ? (
                <TabsTrigger value="description">
                  {t('description.title')}
                </TabsTrigger>
              ) : null}
              {hasElementsToDisplay(transformedEquipments) ? (
                <TabsTrigger value="coveredEquipments">
                  {t('coveredEquipments.title')}
                </TabsTrigger>
              ) : null}
            </TabsList>
            <TabsContent
              className="border-t-4 border-primary"
              value="description"
            >
              {hasElementsToDisplay(description) ? (
                <Description {...description} />
              ) : null}
            </TabsContent>
            <TabsContent
              className="border-t-4 border-primary"
              value="coveredEquipments"
            >
              {hasElementsToDisplay(transformedEquipments) ? (
                <EquipmentGrid
                  className="mt-6"
                  equipments={transformedEquipments}
                />
              ) : null}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default Details;
