import type {
  RoomDetailSchema,
  RoomEditSchema,
  RoomInputSchema,
  RoomListSchema,
  RoomSchema,
  RoomSelectSchema,
} from '../schemas/rooms.schema';

import type * as Schema from 'effect/Schema';

export type Room = Schema.Schema.Type<typeof RoomSchema>;
export type RoomInput = Schema.Schema.Type<typeof RoomInputSchema>;
export type RoomList = Schema.Schema.Type<typeof RoomListSchema>;
export type RoomSelect = Schema.Schema.Type<typeof RoomSelectSchema>;
export type RoomEdit = Schema.Schema.Type<typeof RoomEditSchema>;
export type RoomDetail = Schema.Schema.Type<typeof RoomDetailSchema>;
