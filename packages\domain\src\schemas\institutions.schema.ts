import {
  DbInstitutionI18NSelectSchema,
  DbInstitutionInputSchema,
  DbInstitutionSelectSchema,
  DbInstitutionTypeI18NSelectSchema,
  DbInstitutionTypeSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth150MaxLengthSchema,
  optionalFieldWth1500MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';

// — Full Institution shape
export const InstitutionSchema = Schema.Struct({
  ...DbInstitutionSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    DbInstitutionI18NSelectSchema.omit('id', 'dataId'),
  ),
  type: Schema.NullishOr(
    Schema.Struct({
      ...DbInstitutionTypeSelectSchema.fields,
      translations: Schema.Array(
        DbInstitutionTypeI18NSelectSchema.omit('id', 'dataId'),
      ),
    }),
  ),
});

// — Translation input schema
export const InstitutionI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: optionalFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  otherNames: optionalFieldWth150MaxLengthSchema('Other Names'),
  acronyms: optionalFieldWth150MaxLengthSchema('Acronyms'),
});

// — Institution List view schema (for directory table)
export const InstitutionListSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String, // nom par défaut depuis traductions
  acronym: Schema.NullishOr(Schema.String), // acronyme depuis traductions
  establishmentType: Schema.NullishOr(Schema.String), // type d'établissement
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  guidId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
});

// — Institution Select view schema
export const InstitutionSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Institution Edit view schema
export const InstitutionEditSchema = Schema.Struct({
  id: Schema.String,
  guidId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
  translations: Schema.Array(InstitutionI18NInputSchema),
});

// — Institution Detail view schema (same as list for now)
export const InstitutionDetailSchema = InstitutionListSchema;

// — Input (create/update) shape
export const InstitutionInputSchema = Schema.Struct({
  ...DbInstitutionInputSchema.omit('id').fields,
  translations: Schema.Array(InstitutionI18NInputSchema),
});

// — Database schema (for serializers)
export const DbInstitutionSchema = InstitutionSchema;
