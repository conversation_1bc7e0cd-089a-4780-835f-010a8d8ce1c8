import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';

interface MySQLGuid {
  guid_id: number;
  uuid: string;
  table_ref: string;
  created_at: string;
  last_updated_at: string | null;
}

interface PostgresGuid {
  id: string; // cuid2
  uuid: string;
  createdAt: string;
  updatedAt: string;
}

interface GuidMapping {
  mysqlId: number;
  postgresId: string;
  uuid: string;
}

export class GuidMigrationConverter extends BaseConverter {
  private guidMappings: GuidMapping[] = [];

  protected parseInsertStatement(statement: string): MySQLGuid[] {
    const guidData = this.extractValuesFromInsertStatement(statement);
    return [
      {
        guid_id: Number.parseInt(guidData.guid_id),
        uuid: guidData.uuid,
        table_ref: guidData.table_ref,
        created_at: guidData.created_at,
        last_updated_at: guidData.last_updated_at,
      },
    ];
  }

  private convertToPostgres(mysqlGuid: MySQLGuid): PostgresGuid {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.guidMappings.push({
      mysqlId: mysqlGuid.guid_id,
      postgresId: postgresId,
      uuid: mysqlGuid.uuid,
    });

    return {
      id: postgresId,
      uuid: mysqlGuid.uuid,
      createdAt: mysqlGuid.created_at,
      updatedAt: mysqlGuid.last_updated_at || mysqlGuid.created_at,
    };
  }

  private generatePostgresInsertWithMappings(guids: PostgresGuid[]): string {
    let output = '';

    const values = guids
      .map(
        (guid) =>
          `('${guid.id}', ${guid.uuid ? `'${guid.uuid}'` : 'NULL'}, ${
            guid.createdAt ? `'${guid.createdAt}'` : 'NULL'
          }, ${guid.updatedAt ? `'${guid.updatedAt}'` : 'NULL'})`,
      )
      .join(',\n');

    output += `INSERT INTO "guids" ("id", "uuid", "created_at", "updated_at") VALUES\n${values};\n\n`;

    return output;
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for guid table using the new method
      const insertStatements = this.extractInsertStatements(sqlContent, 'guid');

      if (insertStatements.length === 0) {
        return;
      }

      const allPostgresGuids: PostgresGuid[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlGuids = this.parseInsertStatement(statement);
        const postgresGuids = mysqlGuids.map((g) => this.convertToPostgres(g));
        allPostgresGuids.push(...postgresGuids);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresInsertWithMappings(allPostgresGuids);

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.guidMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'guid', postgres: 'guids' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresGuids.length} guid records`);
      console.log(
        `- PostgreSQL inserts and mappings appended to: ${outputPath}`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
