import type { ControlledListsQuery } from '@rie/domain/schemas';
import { transformControlledListData } from '@rie/domain/serializers';
import {
  ControlledListsRepositoryLive,
  LocaleRepositoryLive,
} from '@rie/repositories';
import * as Effect from 'effect/Effect';

export class ControlledListsServiceLive extends Effect.Service<ControlledListsServiceLive>()(
  'ControlledListsServiceLive',
  {
    dependencies: [
      ControlledListsRepositoryLive.Default,
      LocaleRepositoryLive.Default,
    ],
    effect: Effect.gen(function* () {
      /**
       * Get controlled lists for multiple entities
       */
      const getControlledLists = ({
        entities,
        limit,
        locale,
      }: ControlledListsQuery) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const controlledListsRepository =
            yield* ControlledListsRepositoryLive;
          const localeRepository = yield* LocaleRepositoryLive;
          const fallbackLocale =
            yield* localeRepository.getFallbackLocale(locale);

          const rawResults =
            yield* controlledListsRepository.getControlledLists({
              entityKeys: entities,
              limit,
              locale,
            });

          const transformedResults = entities.map((entity, index) => {
            const rawResult = rawResults[index];

            return {
              entity,
              data: rawResult
                ? transformControlledListData(
                    rawResult.filter(
                      (item): item is typeof item & { locale: string } =>
                        item.locale !== null,
                    ),
                    locale,
                    fallbackLocale,
                  )
                : [],
            };
          });

          const t1 = performance.now();
          console.log(
            `Call to getControlledLists took ${t1 - t0} milliseconds.`,
          );

          return transformedResults;
        });
      };

      return {
        getControlledLists,
      } as const;
    }),
  },
) {}
