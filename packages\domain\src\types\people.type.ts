import type {
  PersonDetailSchema,
  PersonEditSchema,
  PersonInputSchema,
  PersonListSchema,
  PersonSchema,
  PersonSelectSchema,
} from '../schemas/people.schema';

import type * as Schema from 'effect/Schema';

export type Person = Schema.Schema.Type<typeof PersonSchema>;
export type PersonInput = Schema.Schema.Type<typeof PersonInputSchema>;
export type PersonList = Schema.Schema.Type<typeof PersonListSchema>;
export type PersonSelect = Schema.Schema.Type<typeof PersonSelectSchema>;
export type PersonEdit = Schema.Schema.Type<typeof PersonEditSchema>;
export type PersonDetail = Schema.Schema.Type<typeof PersonDetailSchema>;
