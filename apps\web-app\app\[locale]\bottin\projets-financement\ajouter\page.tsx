import { AddFinancingProject } from '@/app/[locale]/bottin/projets-financement/ajouter/add-financing-project';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { projectFormSections } from '@/constants/bottin/financing-project';
import { getQueryClientOptions } from '@/constants/query-client';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import { allInfrastructuresOptions } from '@/hooks/infrastructure/useGetAllInfrastructures';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function NewFinancingProjectPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  const formSections = await getFormSections({
    resourceName: 'directory',
    sections: projectFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = [
    'person',
    'financingProjectType',
    'numberType',
    'purchasedEquipment',
  ];

  const t0 = performance.now();

  await Promise.all([
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
    queryClient.prefetchQuery(
      allInfrastructuresOptions({
        params: defaultRieServiceParams(locale),
        queryParams: '',
      }),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "person", "financingProjectType", "numberType", "purchasedEquipment" and "infrastructures" took ${t1 - t0} milliseconds.`,
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AddFinancingProject locale={locale} formSections={formSections} />
    </HydrationBoundary>
  );
}
