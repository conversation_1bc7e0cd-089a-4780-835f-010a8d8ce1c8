import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class DocumentationCategoryMigrationConverter extends BaseConverter {
  private documentationCategoryMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlDocumentationCategory: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.documentationCategoryMappings.push({
      mysqlId: mysqlDocumentationCategory.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for categorie_documentation table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'categorie_documentation',
      );

      if (insertStatements.length === 0) {
        console.log('No categorie_documentation INSERT statements found.');
        return;
      }

      const allPostgresDocumentationCategories: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlDocumentationCategories =
          this.parseInsertStatement(statement);
        const postgresDocumentationCategories =
          mysqlDocumentationCategories.map((dc) => this.convertToPostgres(dc));
        allPostgresDocumentationCategories.push(
          ...postgresDocumentationCategories,
        );
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresDocumentationCategories,
          'documentation_categories',
          'Documentation Category Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.documentationCategoryMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'categorie_documentation',
          postgres: 'documentation_categories',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresDocumentationCategories.length} documentation_categories records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
