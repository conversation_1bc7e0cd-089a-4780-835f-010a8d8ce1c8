import { FinancingProjectIdentificationId } from '@/app/[locale]/bottin/projets-financement/form/sections/description/financing-project-identification-id';
import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { DatePickerField } from '@/components/form-fields/datepicker-field';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useGetInfiniteInfrastructures } from '@/hooks/infrastructure/useGetInfiniteInfrastructures';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import { mapListDataToSelectOption } from '@/services/mappers/controlled-list-select-data';
import { Button } from '@/ui/button';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Heading } from '@/ui/heading';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FiPlus, FiTrash2 } from 'react-icons/fi';

export const GeneralInfo = () => {
  const tFinancingProject = useTranslations(
    'financingProjects.form.sections.description.generalInfo',
  );
  const tFinancingProjectFields = useTranslations(
    'financingProjects.form.sections.description.generalInfo.fields',
  );
  const tCommon = useTranslations('common');
  const locale = useAvailableLocale();
  const [searchTerm] = useState('');

  const { control, formState } = useFormContext<FinancingProjectFormSchema>();
  const nameFields = useTranslatedField(control, 'name');
  const nameError = getFieldErrorMessage(formState.errors, 'name');
  const nameErrorMessage = nameError
    ? tFinancingProjectFields(nameError)
    : undefined;
  const descriptionFields = useTranslatedField(control, 'description');
  const { append, fields, remove } = useFieldArray({
    control,
    name: 'identificationIds',
  });

  const infrastructures = useGetInfiniteInfrastructures({
    params: defaultRieServiceParams(locale),
    queryParams: searchTerm ? `q=${searchTerm}` : '',
    select: (response) =>
      response.pages.flatMap((page) =>
        page.data.map(mapListDataToSelectOption),
      ),
  });

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData([
      'person',
      'financingProjectType',
      'purchasedEquipment',
    ]);

  return (
    <FormSubsection title={tFinancingProject('title')}>
      <FieldWithTranslations
        control={control}
        errorMessage={nameErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="name"
        fields={nameFields.fields}
        label={(locale) =>
          tFinancingProjectFields('projectName.label', {
            locale: tCommon(locale),
          })
        }
        maxLength={150}
        onAddTranslation={nameFields.handleAddTranslation}
        onRemoveTranslation={nameFields.handleRemoveTranslation}
        required={true}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="person"
        fetchNextPage={selectsData.person?.fetchNextPage}
        fieldLabel={tFinancingProjectFields('principalResearcher.label')}
        fieldName="principalResearcher"
        hasNextPage={Boolean(selectsData.person?.hasNextPage)}
        isFetching={Boolean(selectsData.person?.isFetching)}
        isFetchingNextPage={Boolean(selectsData.person?.isFetchingNextPage)}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />

      <MultiselectField
        fieldName="associateResearchers"
        label={tFinancingProjectFields('associateResearchers.label')}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
      />

      <MultiselectField
        fieldName="financedInfrastructures"
        label={tFinancingProjectFields('financedInfrastructures.label')}
        options={infrastructures.data ?? []}
        placeholder={tCommon('select')}
      />

      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="description"
        fields={descriptionFields.fields}
        label={(locale) =>
          tFinancingProjectFields('description.label', {
            locale: tCommon(locale),
          })
        }
        onAddTranslation={descriptionFields.handleAddTranslation}
        onRemoveTranslation={descriptionFields.handleRemoveTranslation}
      />
      <FormField
        control={control}
        name="obtainingYear"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor="obtainingYear"
              label={tFinancingProjectFields('obtainingYear.label')}
            />
            <FormControl>
              <Input
                {...field}
                inputMode="numeric"
                min={0}
                pattern="[0-9]*"
                step={1}
                type="number"
              />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <DatePickerField
        className="w-full"
        fieldLabel={tFinancingProjectFields('closingDate.label')}
        fieldName="closingDate"
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="financingProjectType"
        fetchNextPage={selectsData.financingProjectType?.fetchNextPage}
        fieldLabel={tFinancingProjectFields('financingProjectType.label')}
        fieldName="financingProjectType"
        hasNextPage={Boolean(selectsData.financingProjectType?.hasNextPage)}
        isFetching={Boolean(selectsData.financingProjectType?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.financingProjectType?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.financingProjectType?.data ?? []}
        placeholder={tCommon('select')}
      />
      <FormField
        control={control}
        name="synchroId"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor="synchroId"
              label={tFinancingProjectFields('synchroId.label')}
              required={true}
            />
            <FormControl>
              <Input {...field} />
            </FormControl>
          </FormItem>
        )}
      />
      <Heading className="flex gap-x-2" level={5}>
        <LabelTooltip
          label={tFinancingProjectFields('otherIdentificationNumbers.label')}
        />
      </Heading>
      <div className="flex flex-col gap-y-4">
        <div className="flex flex-col gap-y-3">
          {fields.map((field, index) => (
            <div className="flex flex-col" key={field.id}>
              <FinancingProjectIdentificationId index={index} />
              <Button
                className="self-end"
                onClick={() => remove(index)}
                type="button"
                variant="destructive"
              >
                <FiTrash2 className="mr-2 h-4 w-4" /> {tCommon('delete')}
              </Button>
            </div>
          ))}
        </div>
        <Button
          className="self-end"
          onClick={() =>
            append({
              identificationId: '',
              type: { locale: '', value: '' },
            })
          }
          type="button"
        >
          <FiPlus className="mr-2 h-4 w-4" />{' '}
          {tFinancingProjectFields('addIdentificationId.label')}
        </Button>
      </div>
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="purchasedEquipment"
        fetchNextPage={selectsData.purchasedEquipment?.fetchNextPage}
        fieldLabel={tFinancingProjectFields('purchasedEquipment.label')}
        fieldName="purchasedEquipment"
        hasNextPage={Boolean(selectsData.purchasedEquipment?.hasNextPage)}
        isFetching={Boolean(selectsData.purchasedEquipment?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.purchasedEquipment?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.purchasedEquipment?.data ?? []}
        placeholder={tCommon('select')}
      />
    </FormSubsection>
  );
};
