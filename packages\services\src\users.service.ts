import { UserNotFoundError } from '@rie/domain/errors';
import { transformDbUserDetailWithRolesToUserDetailWithRoles } from '@rie/domain/serializers';
import { UsersRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';

export class UsersServiceLive extends Effect.Service<UsersServiceLive>()(
  'UsersServiceLive',
  {
    dependencies: [UsersRepositoryLive.Default],
    effect: Effect.gen(function* () {
      /**
       * Get a user by ID with all their roles and associated person information
       */
      const getUserById = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const usersRepository = yield* UsersRepositoryLive;

          // Get user with roles
          const user = yield* usersRepository.findUserById(id);

          if (!user) {
            return yield* Effect.fail(new UserNotFoundError({ id }));
          }

          const t1 = performance.now();
          console.log(`Call to getUserById took ${t1 - t0} milliseconds.`);

          return transformDbUserDetailWithRolesToUserDetailWithRoles(user);
        });
      };

      return {
        getUserById,
      } as const;
    }),
  },
) {}
