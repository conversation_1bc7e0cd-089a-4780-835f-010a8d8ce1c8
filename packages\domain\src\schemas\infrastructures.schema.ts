import {
  DbInfrastructureI18NSelectSchema,
  DbInfrastructureInputSchema,
  DbInfrastructureSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth150MaxLengthSchema,
  optionalFieldWth1500MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';

// — Full Infrastructure shape
export const InfrastructureSchema = Schema.Struct({
  ...DbInfrastructureSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    DbInfrastructureI18NSelectSchema.omit('id', 'dataId'),
  ),
});

// — DB Infrastructure schema (from repository data with relations)
export const DbInfrastructureSchema = Schema.Struct({
  id: Schema.String,
  guidId: Schema.String,
  typeId: Schema.String,
  addressId: Schema.NullishOr(Schema.String),
  statusId: Schema.String,
  unitId: Schema.NullishOr(Schema.String),
  website: Schema.NullishOr(Schema.String),
  is_featured: Schema.NullishOr(Schema.Boolean),
  visibilityId: Schema.String,
  createdAt: Schema.String,
  updatedAt: Schema.String,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    Schema.Struct({
      id: Schema.String,
      locale: Schema.String,
      name: Schema.NullishOr(Schema.String),
      description: Schema.NullishOr(Schema.String),
      otherNames: Schema.NullishOr(Schema.String),
      acronyms: Schema.NullishOr(Schema.String),
    }),
  ),
  type: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
  status: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
  unit: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
          description: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
  address: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      addressType: Schema.NullishOr(Schema.String),
      campusAddressId: Schema.NullishOr(Schema.String),
      civicAddressId: Schema.NullishOr(Schema.String),
      civicAddress: Schema.NullishOr(
        Schema.Struct({
          street1: Schema.NullishOr(Schema.String),
          city: Schema.NullishOr(Schema.String),
          state: Schema.NullishOr(Schema.String),
          postalCode: Schema.NullishOr(Schema.String),
          countryCode: Schema.NullishOr(Schema.String),
        }),
      ),
      campusAddress: Schema.NullishOr(
        Schema.Struct({
          id: Schema.String,
          room_id: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
  equipments: Schema.Array(Schema.Struct({ id: Schema.String })),
  associatedScientificManagers: Schema.Array(
    Schema.Struct({
      id: Schema.String,
      person: Schema.Struct({
        id: Schema.String,
        firstName: Schema.NullishOr(Schema.String),
        lastName: Schema.NullishOr(Schema.String),
      }),
    }),
  ),
  associatedOperationalManagers: Schema.Array(
    Schema.Struct({
      person: Schema.Struct({
        id: Schema.String,
        firstName: Schema.NullishOr(Schema.String),
        lastName: Schema.NullishOr(Schema.String),
      }),
    }),
  ),
});

// — Translation input schema
export const InfrastructureI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: optionalFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  otherNames: optionalFieldWth150MaxLengthSchema('Other Names'),
  acronyms: optionalFieldWth150MaxLengthSchema('Acronyms'),
});

// — Input (create/update) shape
export const InfrastructureInputSchema = Schema.Struct({
  ...DbInfrastructureInputSchema.omit('id').fields,
  translations: Schema.Array(InfrastructureI18NInputSchema),
});

// — List view schema for infrastructures (used by table and card views)
export const InfrastructureListSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  type: Schema.NullishOr(Schema.String),
  status: Schema.NullishOr(Schema.String),
  statusText: Schema.NullishOr(Schema.String),
  equipmentCount: Schema.Number,
  jurisdiction: Schema.NullishOr(Schema.String),
  parentUnit: Schema.NullishOr(Schema.String),
  scientificDirector: Schema.NullishOr(Schema.String),
  technicalDirector: Schema.NullishOr(Schema.String),
  address: Schema.NullishOr(Schema.String),
  updatedAt: Schema.String,
});

// — Detail view schema (for now, same as list)
export const InfrastructureDetailSchema = InfrastructureListSchema;

// — Select view schema for infrastructures
export const InfrastructureSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});
