import type { RieServiceParams } from '@/constants/rie-client';
import { getInfiniteControlledList } from '@/services/controled-lists';
import { mapControlledListsToSelectOptions } from '@/services/mappers/controlled-list-select-data';
import type {
  ControlledListKey,
  ControlledListReturnType,
  SelectOption,
} from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import type { UseInfiniteQueryOptions } from '@tanstack/react-query';
import type { AxiosError } from 'axios';

type InfiniteControlledListArgs<TData> = {
  controlledListKey: ControlledListKey;
  locale: SupportedLocale;
  params?: RieServiceParams;
  searchTerm: string;
} & Pick<
  UseInfiniteQueryOptions<
    ControlledListReturnType<TData>,
    AxiosError,
    SelectOption[]
  >,
  'select'
>;

export const infiniteControlledListsOptions = <
  TData extends { id: number | string; text: string },
>({
  controlledListKey,
  locale,
  params,
  searchTerm,
}: InfiniteControlledListArgs<TData>): UseInfiniteQueryOptions<
  ControlledListReturnType<TData[]>,
  AxiosError,
  SelectOption[]
> => {
  return {
    getNextPageParam: (lastPage) => {
      if (lastPage.offset + lastPage.data.length < lastPage.count) {
        return lastPage.offset + lastPage.data.length;
      }
      return undefined;
    },
    getPreviousPageParam: (firstPage) => {
      if (firstPage.offset > 0) {
        return firstPage.offset - firstPage.data.length;
      }
      return undefined;
    },
    initialPageParam: 0,
    queryFn: ({ pageParam = 0 }) =>
      getInfiniteControlledList({
        controlledListKey,
        pageParam: pageParam as number,
        params: {
          ...params,
          lang: locale,
          limit: '12',
        },
        searchTerm,
      }),
    queryKey: [
      'infiniteControlledLists',
      controlledListKey,
      locale,
      params,
      searchTerm,
    ],

    select: (data) =>
      mapControlledListsToSelectOptions(
        data.pages.flatMap((page) => page.data),
      ),
  };
};
