import * as Brand from 'effect/Brand';
import * as Schema from 'effect/Schema';
import type { MessageAnnotation } from 'effect/SchemaAST';
import { LocaleSchema } from './query.schema';

export type Integer = number & Brand.Brand<'Integer'>;
export const Integer = Brand.refined<Integer>(
  (number) => Number.isInteger(number),
  (invalidNumber) => Brand.error(`${invalidNumber} is not an integer`),
);

export type PositiveInteger = number & Brand.Brand<'PositiveInteger'>;
export const PositiveInteger = Brand.refined<PositiveInteger>(
  (number) => number > 0,
  (invalidNumber) => Brand.error(`${invalidNumber} is not a positive integer`),
);

export const TrimmedString = Schema.transform(Schema.String, Schema.String, {
  decode: (s) => s.trim(),
  encode: (s) => s,
}).annotations({
  message: () => 'You must provide a string',
});

interface RequiredStringOfMaxLengthArgs {
  fieldMaxLength: number;
  errorMessages: { required: MessageAnnotation; maxLength: MessageAnnotation };
}

export const createRequiredStringOfMaxLengthSchema = ({
  fieldMaxLength,
  errorMessages,
}: RequiredStringOfMaxLengthArgs) => {
  return TrimmedString.pipe(
    Schema.nonEmptyString({ message: errorMessages.required }),
    Schema.maxLength(fieldMaxLength, {
      message: errorMessages.maxLength,
    }),
  );
};

interface OptionalStringOfMaxLengthArgs {
  fieldMaxLength: number;
  maxLengthErrorMessage: MessageAnnotation;
}

export const createOptionalStringOfMaxLengthSchema = ({
  fieldMaxLength,
  maxLengthErrorMessage,
}: OptionalStringOfMaxLengthArgs) =>
  Schema.optional(
    TrimmedString.pipe(
      Schema.maxLength(fieldMaxLength, {
        message: maxLengthErrorMessage,
      }),
    ),
  );

type RequiredTranslation = {
  required: true;
  missingTranslationMessage: string;
};

type OptionalTranslation = {
  required: false;
  missingTranslationMessage?: never;
};

type Field = {
  maxLengthErrorMessage: string;
  maxLength: number;
};

type CreateTranslatableFieldArgs = (
  | RequiredTranslation
  | OptionalTranslation
) & {
  field: Field;
};

/**
 * Creates a translatable field schema
 * If message is provided, then at least one translation must be provided
 * @param field - Object containing maxLength and maxLengthErrorMessage
 * @param required - Boolean indicating if at least one translation is required
 * @param missingTranslationMessage - Error message to display when required is true and no translations are provided
 *
 * @example
 * createTranslatableFieldMaxLengthSchema({
 *   field: {
 *     maxLengthErrorMessage: "Name can't contain more than 150 characters",
 *     maxLength: 150
 *   },
 *   required: true,
 *   missingTranslationMessage: "At least one translation must be provided"
 * })
 **/
export const createTranslatableFieldMaxLengthSchema = ({
  field,
  missingTranslationMessage,
  required,
}: CreateTranslatableFieldArgs) => {
  const baseSchema = Schema.Array(
    Schema.Struct({
      locale: LocaleSchema,
      value: TrimmedString.pipe(
        Schema.maxLength(field.maxLength, {
          message: () => field.maxLengthErrorMessage,
        }),
      ),
    }),
  );

  return required
    ? baseSchema.pipe(
        Schema.filter((translations) =>
          translations.some((t) => t.value.trim().length > 0, {
            message: missingTranslationMessage,
          }),
        ),
      )
    : baseSchema;
};

export const descriptionSchema = createOptionalStringOfMaxLengthSchema({
  fieldMaxLength: 1500,
  maxLengthErrorMessage: (issue) =>
    `Description must be ${issue._tag.length} characters or less`,
});

export const requiredFieldWth150MaxLengthSchema = (fieldName: string) =>
  createRequiredStringOfMaxLengthSchema({
    fieldMaxLength: 150,
    errorMessages: {
      required: () => 'Name is required',
      maxLength: (issue) =>
        `${fieldName} must be ${issue._tag.length} characters or less`,
    },
  });

export const optionalFieldWth150MaxLengthSchema = (fieldName: string) =>
  createOptionalStringOfMaxLengthSchema({
    fieldMaxLength: 150,
    maxLengthErrorMessage: (issue) =>
      `${fieldName} must be ${issue._tag.length} characters or less`,
  });

export const optionalFieldWth1500MaxLengthSchema = (fieldName: string) =>
  createOptionalStringOfMaxLengthSchema({
    fieldMaxLength: 1500,
    maxLengthErrorMessage: (issue) =>
      `${fieldName} must be ${issue._tag.length} characters or less`,
  });

export const SelectOptionSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});
