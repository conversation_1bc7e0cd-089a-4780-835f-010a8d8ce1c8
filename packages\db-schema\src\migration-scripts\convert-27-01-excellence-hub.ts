import * as path from 'node:path';
import { SQL_FILE_NAME } from '@/migration-scripts/constants';
import { ExcellenceHubMigrationConverter } from './converters/27-01-convert-excellence-hub-inserts';

async function convertExcellenceHubData() {
  const scriptDir = __dirname;
  // Input file is in the data directory
  const inputFile = path.join(
    scriptDir,
    'data',
    'mysql_riedb-content_24-04-2025.sql',
  );
  // Output will go to migration-scripts/output
  const outputDir = path.join(scriptDir, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new ExcellenceHubMigrationConverter();

    // Convert the file
    await converter.convertFile(inputFile, outputFile);
  } catch (error) {
    console.error('Error converting excellence hub data:', error);
    process.exit(1);
  }
}

// Run the conversion
convertExcellenceHubData();
