'use client';
import { CampusForm } from '@/app/[locale]/bottin/campus/form/campus-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useUpdateCampus } from '@/hooks/bottin/campuses.hook';
import { useGetGenericById } from '@/hooks/bottin/generic-list.hook';
import type { CampusFormSchema } from '@/schemas/bottin/campus-form-schema';
import type { CampusFormSectionKey } from '@/types/building';
import type { SupportedLocale } from '@/types/locale';
import { CampusEditToFormSchema } from '@rie/domain/serializers';
import * as Schema from 'effect/Schema';

type EditCampusPageParams = {
  formSections: Record<CampusFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionCampusPage({
  formSections,
  id,
}: EditCampusPageParams) {
  const {
    data: campus,
    error,
    isPending,
  } = useGetGenericById<'campus', 'edit'>({
    controlledListKey: 'campus',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Erreur : {error.message}</div>;
  }

  const updateCampus = useUpdateCampus();
  const onSubmit = async (data: CampusFormSchema) => {
    await updateCampus.mutateAsync({ id, payload: data });
  };

  // Transform campus edit data to form schema using serializer
  const formData = Schema.decodeSync(CampusEditToFormSchema)(
    campus,
  ) as CampusFormSchema;

  return (
    <CampusForm
      defaultValues={formData}
      formSections={formSections}
      onSubmit={onSubmit}
    />
  );
}
