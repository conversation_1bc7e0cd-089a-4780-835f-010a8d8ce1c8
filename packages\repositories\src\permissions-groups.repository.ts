import type { DbPermissionGroupPermissionInput } from '@rie/db-schema/entity-types';
import {
  permissionGroupPermissions,
  permissionGroups,
} from '@rie/db-schema/schemas';
import type { DBPermissionsGroupInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

/**
 * Update a permission group
 */
interface UpdatePermissionsGroupParams extends DBPermissionsGroupInput {
  id: string;
}

export class PermissionsGroupsRepositoryLive extends Effect.Service<PermissionsGroupsRepositoryLive>()(
  'PermissionsGroupsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      /**
       * Find all permission groups
       */
      const findAllPermissionsGroups = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.permissionGroups.findMany({
            columns: {
              id: true,
              name: true,
              description: true,
              createdAt: true,
              updatedAt: true,
            },
            with: {
              permissions: {
                with: {
                  permission: {
                    columns: {
                      id: true,
                      domain: true,
                      action: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      /**
       * Find a permission group by ID
       */
      const findPermissionsGroupById = dbClient.makeQuery(
        (execute, id: string) => {
          return execute((client) =>
            client.query.permissionGroups.findFirst({
              where: eq(permissionGroups.id, id),
              columns: {
                id: true,
                name: true,
                description: true,
                createdAt: true,
                updatedAt: true,
              },
              with: {
                permissions: {
                  columns: {
                    permissionId: false,
                    groupId: false,
                  },
                  with: {
                    permission: {
                      columns: {
                        id: true,
                        domain: true,
                        action: true,
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      /**
       * Check if a permission group with the given name already exists
       */
      const checkPermissionsGroupExists = dbClient.makeQuery(
        (execute, name: string) => {
          return execute((client) =>
            client.query.permissionGroups.findFirst({
              where: eq(permissionGroups.name, name),
              columns: {
                id: true,
              },
            }),
          );
        },
      );

      /**
       * Create a new permission group
       * This uses a transaction to ensure all related data is created atomically
       */
      const createPermissionsGroup = ({
        name,
        description,
        permissions,
      }: Omit<DBPermissionsGroupInput, 'createdAt' | 'updatedAt'>) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // 1. Create the permission group
            const [group] = yield* tx((client) =>
              client
                .insert(permissionGroups)
                .values({ name, description })
                .returning({
                  id: permissionGroups.id,
                  name: permissionGroups.name,
                  description: permissionGroups.description,
                }),
            );

            if (!group) {
              return yield* Effect.fail(
                new Error('Failed to create permission group'),
              );
            }

            // 2. Add permissions if provided
            if (permissions.length > 0) {
              const groupPermissions: DbPermissionGroupPermissionInput[] =
                permissions.map((permissionId) => ({
                  groupId: group.id,
                  permissionId,
                }));

              yield* tx((client) =>
                client
                  .insert(permissionGroupPermissions)
                  .values(groupPermissions),
              );
            }

            return group;
          });
        });
      };

      /**
       * Update a permission group
       */
      const updatePermissionsGroup = ({
        id,
        name,
        description,
        permissions,
      }: UpdatePermissionsGroupParams) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // 1. Update the permission group
            const [group] = yield* tx((client) =>
              client
                .update(permissionGroups)
                .set({
                  name,
                  description,
                })
                .where(eq(permissionGroups.id, id))
                .returning({
                  id: permissionGroups.id,
                  name: permissionGroups.name,
                  description: permissionGroups.description,
                  createdAt: permissionGroups.createdAt,
                  updatedAt: permissionGroups.updatedAt,
                }),
            );

            if (!group) {
              return yield* Effect.fail(
                new Error('Failed to update permission group'),
              );
            }

            // 3. Add permissions if provided
            if (permissions.length > 0) {
              yield* tx((client) =>
                client
                  .delete(permissionGroupPermissions)
                  .where(eq(permissionGroupPermissions.groupId, id)),
              );

              const groupPermissions: DbPermissionGroupPermissionInput[] =
                permissions.map((permissionId) => ({
                  groupId: group.id,
                  permissionId,
                }));

              yield* tx((client) =>
                client
                  .insert(permissionGroupPermissions)
                  .values(groupPermissions),
              );
            }

            return group;
          });
        });
      };

      /**
       * Delete a permission group
       * This will cascade delete all related data due to foreign key constraints
       */
      const deletePermissionsGroup = dbClient.makeQuery(
        (execute, id: string) => {
          return execute((client) =>
            client
              .delete(permissionGroups)
              .where(eq(permissionGroups.id, id))
              .returning({ id: permissionGroups.id }),
          );
        },
      );

      return {
        findAllPermissionsGroups,
        findPermissionsGroupById,
        checkPermissionsGroupExists,
        createPermissionsGroup,
        updatePermissionsGroup,
        deletePermissionsGroup,
      } as const;
    }),
  },
) {}
