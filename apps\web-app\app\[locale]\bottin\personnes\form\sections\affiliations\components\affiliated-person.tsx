import { AffiliationTypeField } from '@/app/[locale]/bottin/personnes/form/sections/affiliations/components//affiliation-type-field';
import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { PersonFormSchema } from '@/schemas/bottin/person-form-schema';
import { Input } from '@/ui/input';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

type AffiliatedPersonProps = {
  index: number;
};
export const AffiliatedPerson = ({ index }: AffiliatedPersonProps) => {
  const tInfrastructures = useTranslations(
    'infrastructures.form.sections.affiliations.affiliatedPersons',
  );
  const tCommon = useTranslations('common');
  const { control, formState } = useFormContext<PersonFormSchema>();

  const jobTitleFields = useTranslatedField(
    control,
    'affiliatedField.affiliationSection',
  );
  const jobTitleError = getFieldErrorMessage(
    formState.errors,
    `affiliatedField.affiliationSection.${index}.jobTitle`,
  );
  const jobTitleErrorMessage = jobTitleError
    ? tInfrastructures(jobTitleError)
    : undefined;

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['jobType']);

  return (
    <>
      <AffiliationTypeField index={index} />
      <FieldWithTranslations
        control={control}
        errorMessage={jobTitleErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName={`affiliatedField.affiliationSection.${index}.jobTitle`}
        fields={jobTitleFields.fields}
        label={(locale) =>
          tInfrastructures('fields.jobTitle.label', { locale: tCommon(locale) })
        }
        maxLength={150}
        onAddTranslation={jobTitleFields.handleAddTranslation}
        onRemoveTranslation={jobTitleFields.handleRemoveTranslation}
        required={true}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="jobType"
        fetchNextPage={selectsData.jobType?.fetchNextPage}
        fieldLabel={tInfrastructures('fields.jobType.label')}
        fieldName={`affiliatedField.${index}.jobType`}
        hasNextPage={Boolean(selectsData.jobType?.hasNextPage)}
        isFetching={Boolean(selectsData.jobType?.isFetching)}
        isFetchingNextPage={Boolean(selectsData.jobType?.isFetchingNextPage)}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.jobType?.data ?? []}
        placeholder={tCommon('select')}
        required
      />
    </>
  );
};
