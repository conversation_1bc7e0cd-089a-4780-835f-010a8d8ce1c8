import {
  DbEquipmentI18NSelectSchema,
  DbEquipmentInputSchema,
  DbEquipmentSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth150MaxLengthSchema,
  optionalFieldWth1500MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';

// — Full Equipment shape
export const EquipmentSchema = Schema.Struct({
  ...DbEquipmentSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(DbEquipmentI18NSelectSchema.omit('id', 'dataId')),
});

// — Translation input schema
export const EquipmentI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: optionalFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  specification: optionalFieldWth1500MaxLengthSchema('Specification'),
  usageContext: optionalFieldWth1500MaxLengthSchema('Usage Context'),
  risk: optionalFieldWth1500MaxLengthSchema('Risk'),
  comment: optionalFieldWth1500MaxLengthSchema('Comment'),
});

// — Input (create/update) shape
export const EquipmentInputSchema = Schema.Struct({
  ...DbEquipmentInputSchema.omit('id').fields,
  translations: Schema.Array(EquipmentI18NInputSchema),
});

// — List view schema for equipments (used by table and card views)
export const EquipmentListSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  type: Schema.NullishOr(Schema.String),
  status: Schema.NullishOr(Schema.String),
  statusText: Schema.NullishOr(Schema.String),
  infrastructureId: Schema.NullishOr(Schema.String),
  parentInfrastructure: Schema.NullishOr(Schema.String),
  location: Schema.NullishOr(Schema.String),
  manufacturer: Schema.NullishOr(Schema.String),
  model: Schema.NullishOr(Schema.String),
  equipmentCategories: Schema.NullishOr(Schema.Array(Schema.String)),
  updatedAt: Schema.NullishOr(Schema.String),
});

// — DB Equipment schema (from repository data with relations)
export const DbEquipmentSchema = Schema.Struct({
  id: Schema.String,
  guidId: Schema.NullishOr(Schema.String),
  campusAddressId: Schema.NullishOr(Schema.String),
  isCampusAddressConfidential: Schema.NullishOr(Schema.Boolean),
  model: Schema.NullishOr(Schema.String),
  serialNumber: Schema.NullishOr(Schema.String),
  homologationNumber: Schema.NullishOr(Schema.String),
  inventoryNumber: Schema.NullishOr(Schema.String),
  doi: Schema.NullishOr(Schema.String),
  useInClinicalTrial: Schema.NullishOr(Schema.Boolean),
  isHidden: Schema.NullishOr(Schema.Boolean),
  typeId: Schema.NullishOr(Schema.String),
  statusId: Schema.NullishOr(Schema.String),
  workingPercentage: Schema.NullishOr(Schema.Number),
  monetaryCost: Schema.NullishOr(Schema.Number),
  inKindCost: Schema.NullishOr(Schema.Number),
  manufactureYear: Schema.NullishOr(Schema.Number),
  acquisitionDate: Schema.NullishOr(Schema.String),
  installationDate: Schema.NullishOr(Schema.String),
  decommissioningDate: Schema.NullishOr(Schema.String),
  scientificManagerId: Schema.NullishOr(Schema.String),
  manufacturerId: Schema.NullishOr(Schema.String),
  supplierId: Schema.NullishOr(Schema.String),
  infrastructureId: Schema.NullishOr(Schema.String),
  isFeatured: Schema.NullishOr(Schema.Boolean),
  institutionId: Schema.NullishOr(Schema.String),
  createdAt: Schema.String,
  updatedAt: Schema.String,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    Schema.Struct({
      id: Schema.String,
      locale: Schema.String,
      name: Schema.NullishOr(Schema.String),
      description: Schema.NullishOr(Schema.String),
      specification: Schema.NullishOr(Schema.String),
      usageContext: Schema.NullishOr(Schema.String),
      risk: Schema.NullishOr(Schema.String),
      comment: Schema.NullishOr(Schema.String),
    }),
  ),
  type: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
          description: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
  status: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
          description: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
  manufacturer: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
          description: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
  supplier: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
          description: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
  infrastructure: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
          description: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
});
