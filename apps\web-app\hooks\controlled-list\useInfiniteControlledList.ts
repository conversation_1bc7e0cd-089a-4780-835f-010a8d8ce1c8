import { infiniteControlledListsOptions } from '@/hooks/controlled-list/useInfiniteControlledListOptions';
import { useAppStore } from '@/providers/app-store-provider';
import type { ControlledListKey } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import { useInfiniteQuery } from '@tanstack/react-query';

export const useGetSingleInfiniteControlledList = ({
  controlledListKey,
  locale,
}: {
  controlledListKey: ControlledListKey;
  locale: SupportedLocale;
}) => {
  const controlledListFilters = useAppStore(
    (state) => state.controlledListFilters,
  );

  return useInfiniteQuery(
    infiniteControlledListsOptions({
      controlledListKey,
      locale,
      searchTerm: controlledListFilters[controlledListKey] ?? '',
    }),
  );
};
