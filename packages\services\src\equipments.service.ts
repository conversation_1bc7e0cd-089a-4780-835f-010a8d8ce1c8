import { EquipmentNotFoundError } from '@rie/domain/errors';
import type { EquipmentInputSchema } from '@rie/domain/schemas';
import { EquipmentsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type EquipmentInput = Schema.Schema.Type<typeof EquipmentInputSchema>;

export class EquipmentsServiceLive extends Effect.Service<EquipmentsServiceLive>()(
  'EquipmentsServiceLive',
  {
    dependencies: [EquipmentsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllEquipments = (includes?: {
        type?: boolean;
        status?: boolean;
        unit?: boolean;
        address?: boolean;
      }) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          // Use full method when any includes are requested, otherwise use collection method for performance
          const hasIncludes = includes && Object.values(includes).some(Boolean);
          const all = hasIncludes
            ? yield* repo.findAllEquipments()
            : yield* repo.findAllEquipmentsForCollection(undefined);
          const t1 = performance.now();
          console.log(`Call to getAllEquipments took ${t1 - t0} ms.`);
          return all;
        });
      };

      const countEquipments = () =>
        Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          return yield* repo.countAllEquipments();
        });

      const getEquipmentById = (id: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          const equipment = yield* repo.findEquipmentById(id);
          if (!equipment) {
            return yield* Effect.fail(new EquipmentNotFoundError({ id }));
          }
          const t1 = performance.now();
          console.log(`Call to getEquipmentById took ${t1 - t0} ms.`);
          return equipment;
        });
      };

      const createEquipment = (equipment: EquipmentInput) =>
        Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          return yield* repo.createEquipment({ equipment });
        });

      const updateEquipment = ({
        id,
        equipment,
      }: { equipment: EquipmentInput; id: string }) => {
        return Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          const existingEquipment = yield* repo.findEquipmentById(id);
          if (!existingEquipment) {
            return yield* Effect.fail(new EquipmentNotFoundError({ id }));
          }
          return yield* repo.updateEquipment({
            equipmentId: id,
            equipment,
          });
        });
      };

      const deleteEquipment = (id: string) => {
        return Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          const existingEquipment = yield* repo.findEquipmentById(id);
          if (!existingEquipment) {
            return yield* Effect.fail(new EquipmentNotFoundError({ id }));
          }
          const result = yield* repo.deleteEquipment(id);
          return result.length > 0;
        });
      };

      return {
        getAllEquipments,
        countEquipments,
        getEquipmentById,
        createEquipment,
        updateEquipment,
        deleteEquipment,
      } as const;
    }),
  },
) {}
