import { DBSchema } from '@rie/db-schema';
import { institutionAssociatedUnits } from '@rie/db-schema/schemas';
import type { UnitInputSchema } from '@rie/domain/schemas';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type UnitInput = Schema.Schema.Type<typeof UnitInputSchema>;

export class UnitsRepositoryLive extends Effect.Service<UnitsRepositoryLive>()(
  'UnitsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      // — Fetch all units with translations
      const findAllUnits = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.units.findMany({
            columns: {
              id: true,
              isActive: true,
              guidId: true,
              typeId: true,
              parentId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
              parent: {
                columns: {
                  id: true,
                  type: true,
                },
                with: {
                  institution: {
                    columns: {
                      id: true,
                    },
                    with: {
                      translations: {
                        columns: {
                          locale: true,
                          name: true,
                        },
                      },
                    },
                  },
                  unit: {
                    columns: {
                      id: true,
                    },
                    with: {
                      translations: {
                        columns: {
                          locale: true,
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          }),
        );
      });

      // — Fetch one unit by ID
      const findUnitById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.units.findFirst({
            where: eq(DBSchema.units.id, id),
            columns: {
              id: true,
              isActive: true,
              guidId: true,
              typeId: true,
              parentId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
              type: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              parent: {
                columns: {
                  id: true,
                  type: true,
                },
                with: {
                  institution: {
                    columns: {
                      id: true,
                    },
                    with: {
                      translations: {
                        columns: {
                          locale: true,
                          name: true,
                        },
                      },
                    },
                  },
                  unit: {
                    columns: {
                      id: true,
                    },
                    with: {
                      translations: {
                        columns: {
                          locale: true,
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          }),
        );
      });

      const createUnit = (params: { unit: UnitInput }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Create the unit
            const [createdUnit] = yield* tx((client) =>
              client.insert(DBSchema.units).values(params.unit).returning({
                id: DBSchema.units.id,
                guidId: DBSchema.units.guidId,
                typeId: DBSchema.units.typeId,
                parentId: DBSchema.units.parentId,
                createdAt: DBSchema.units.createdAt,
                updatedAt: DBSchema.units.updatedAt,
                modifiedBy: DBSchema.units.modifiedBy,
              }),
            );

            if (!createdUnit) {
              return yield* Effect.fail(new Error('Failed to create unit'));
            }

            // Create the translations
            const translationsToInsert = params.unit.translations.map(
              (translation) => ({
                dataId: createdUnit.id,
                ...translation,
              }),
            );
            const createdTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.unitsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.unitsI18N.id,
                  locale: DBSchema.unitsI18N.locale,
                  name: DBSchema.unitsI18N.name,
                  description: DBSchema.unitsI18N.description,
                  otherNames: DBSchema.unitsI18N.otherNames,
                  acronyms: DBSchema.unitsI18N.acronyms,
                }),
            );

            if (!createdUnit) {
              return yield* Effect.fail(new Error('Failed to create unit'));
            }

            // Return the unit with its translations directly
            return {
              ...createdUnit,
              translations: createdTranslations,
            };
          });
        });
      };

      const updateUnit = (params: { unitId: string; unit: UnitInput }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the unit
            const [updatedUnit] = yield* tx((client) =>
              client
                .update(DBSchema.units)
                .set({
                  ...params.unit,
                })
                .where(eq(DBSchema.units.id, params.unitId))
                .returning({
                  id: DBSchema.units.id,
                  guidId: DBSchema.units.guidId,
                  typeId: DBSchema.units.typeId,
                  parentId: DBSchema.units.parentId,
                  createdAt: DBSchema.units.createdAt,
                  updatedAt: DBSchema.units.updatedAt,
                  modifiedBy: DBSchema.units.modifiedBy,
                }),
            );

            // Delete existing translations
            yield* tx((client) =>
              client
                .delete(DBSchema.unitsI18N)
                .where(eq(DBSchema.unitsI18N.dataId, params.unitId)),
            );

            // Insert new translations
            const translationsToInsert = params.unit.translations.map(
              (translation) => ({
                dataId: params.unitId,
                ...translation,
              }),
            );

            const updatedTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.unitsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.unitsI18N.id,
                  locale: DBSchema.unitsI18N.locale,
                  name: DBSchema.unitsI18N.name,
                  description: DBSchema.unitsI18N.description,
                  otherNames: DBSchema.unitsI18N.otherNames,
                  acronyms: DBSchema.unitsI18N.acronyms,
                }),
            );

            // Return the unit with its translations directly
            return {
              ...updatedUnit,
              translations: updatedTranslations,
            };
          });
        });
      };

      // — Delete a unit
      const deleteUnit = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.units)
            .where(eq(DBSchema.units.id, id))
            .returning({ id: DBSchema.units.id }),
        );
      });

      /**
       * Find all related entities (units, infrastructures, equipments) for an institution
       * This optimizes the access tree building by using a single query with joins
       */
      const findUnitsWithRelatedEntitiesByInstitutionId = dbClient.makeQuery(
        (execute, institutionId: string) => {
          return execute((client) =>
            client.query.institutionAssociatedUnits.findMany({
              where: eq(
                institutionAssociatedUnits.institutionId,
                institutionId,
              ),
              with: {
                unit: {
                  columns: {
                    id: true,
                  },
                  with: {
                    infrastructures: {
                      columns: {
                        id: true,
                      },
                      with: {
                        equipments: {
                          columns: {
                            id: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      return {
        findUnitsWithRelatedEntitiesByInstitutionId,
        findAllUnits,
        findUnitById,
        createUnit,
        updateUnit,
        deleteUnit,
      } as const;
    }),
  },
) {}
