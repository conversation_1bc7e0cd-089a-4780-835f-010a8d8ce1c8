import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class TimeUnitI18nMigrationConverter extends BaseConverter {
  private timeUnitI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlTimeUnit: MySQLI18NDescription,
    timeUnitIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    // Get the new PostgreSQL ID for the time_unit
    const newMediaTypeId = timeUnitIdMappings[mysqlTimeUnit.data_id.toString()];
    if (!newMediaTypeId) {
      throw new Error(
        `No mapping found for time_units_id: ${mysqlTimeUnit.data_id}`,
      );
    }
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.timeUnitI18nMappings.push({
      mysqlId: mysqlTimeUnit.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newMediaTypeId,
      locale: mysqlTimeUnit.locale,
      name: mysqlTimeUnit.nom,
      description: mysqlTimeUnit.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for time_unit table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'unite_temps_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No time_units_i18n INSERT statements found.');
        return;
      }

      const timeUnitIdMappings = await this.loadEntityIdMappings('unite_temps');

      const allPostgresTimeUnitI18ns: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlTimeUnitI18ns = this.parseI18NInsertStatement(statement);
        const postgresTimeUnitI18ns = mysqlTimeUnitI18ns.map((record) =>
          this.convertToPostgres(record, timeUnitIdMappings),
        );
        allPostgresTimeUnitI18ns.push(...postgresTimeUnitI18ns);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings = this.generatePostgresI18NInsert(
        allPostgresTimeUnitI18ns,
        'time_units_i18n',
        'Time Unit I18N Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.timeUnitI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'unite_temps_trad', postgres: 'time_units_i18n' },
        idMappings,
      );

      console.log(
        `Successfully converted time unit translations to: ${outputPath}`,
      );
    } catch (error) {
      console.error('Error converting time unit translations:', error);
      throw error;
    }
  }
}
