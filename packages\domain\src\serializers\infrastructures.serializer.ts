import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import type { InfrastructureSelectSchema } from '../schemas';
import {
  DbInfrastructureSchema,
  InfrastructureInputSchema,
  InfrastructureListSchema,
} from '../schemas';
import type {
  CollectionViewType,
  DbInfrastructure,
  InfrastructureList,
  ResourceViewType,
} from '../types';

// Input payload transformer (client form -> DB input)
export const InfrastructureFormToDBInput = Schema.transformOrFail(
  Schema.Unknown,
  InfrastructureInputSchema,
  {
    strict: false,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => raw as Schema.Schema.Type<typeof InfrastructureInputSchema>,
        catch: (error) =>
          new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Invalid infrastructure form payload',
          ),
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// DB -> List item
export const DbInfrastructureToList = Schema.transformOrFail(
  DbInfrastructureSchema,
  InfrastructureListSchema,
  {
    strict: false,
    decode: (raw: DbInfrastructure, _options, ast) =>
      ParseResult.try({
        try: () => {
          const translations = raw.translations ?? [];
          const frName = translations.find((t) => t.locale === 'fr')?.name;
          const enName = translations.find((t) => t.locale === 'en')?.name;
          const defaultName =
            frName || enName || translations?.[0]?.name || raw.id;

          const typeTranslations = raw.type?.translations ?? [];
          const typeName =
            typeTranslations.find((t) => t.locale === 'fr')?.name ||
            typeTranslations.find((t) => t.locale === 'en')?.name ||
            typeTranslations?.[0]?.name ||
            null;

          const statusTranslations = raw.status?.translations ?? [];
          const statusText =
            statusTranslations.find((t) => t.locale === 'fr')?.name ||
            statusTranslations.find((t) => t.locale === 'en')?.name ||
            statusTranslations?.[0]?.name ||
            null;

          const unitTranslations = raw.unit?.translations ?? [];
          const unitName =
            unitTranslations.find((t) => t.locale === 'fr')?.name ||
            unitTranslations.find((t) => t.locale === 'en')?.name ||
            unitTranslations?.[0]?.name ||
            null;

          // Normalize civic address (repository returns address.civicAddress)
          const civic = raw.address?.civicAddress ?? null;
          const addressText = civic
            ? [civic.street1, civic.city, civic.state, civic.postalCode]
                .filter((p) => typeof p === 'string' && p.length > 0)
                .join(', ')
            : null;

          const firstManager =
            raw.associatedScientificManagers?.[0]?.person ?? null;
          const scientificDirector = firstManager
            ? `${firstManager.firstName ?? ''} ${firstManager.lastName ?? ''}`.trim() ||
              null
            : null;
          const opManager =
            raw.associatedOperationalManagers?.[0]?.person ?? null;
          const technicalDirector = opManager
            ? `${opManager.firstName ?? ''} ${opManager.lastName ?? ''}`.trim() ||
              null
            : null;

          const equipments = raw.equipments ?? [];
          const updatedAt = raw.updatedAt;

          return {
            id: raw.id,
            name: String(defaultName ?? raw.id ?? ''),
            type: typeName,
            status: null,
            statusText,
            equipmentCount: Array.isArray(equipments) ? equipments.length : 0,
            jurisdiction: unitName || civic?.city || null,
            parentUnit: unitName,
            scientificDirector,
            technicalDirector,
            address: addressText,
            updatedAt,
          };
        },
        catch: (error) =>
          new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse infrastructure for list view',
          ),
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

export const dbInfrastructuresToList = (rows: Array<DbInfrastructure>) =>
  rows.map((row) => Schema.decodeUnknownSync(DbInfrastructureToList)(row));

export const dbInfrastructureToResource = (
  row: {
    id: string;
    guidId: string;
    typeId: string;
    addressId: string | null;
    statusId: string;
    unitId: string | null;
    website: string | null;
    is_featured: boolean | null;
    visibilityId: string;
    createdAt: string;
    updatedAt: string;
    modifiedBy: string | null;
    translations: Array<{
      id: string;
      locale: string;
      name: string | null;
      description: string | null;
      otherNames: string | null;
      acronyms: string | null;
    }>;
    type: {
      id: string;
      translations: Array<{
        locale: string;
        name: string | null;
        description: string | null;
      }>;
    } | null;
    status: {
      id: string;
      translations: Array<{
        locale: string;
        name: string | null;
        description: string | null;
      }>;
    } | null;
    unit: {
      id: string;
      translations: Array<{
        locale: string;
        name: string | null;
        description: string | null;
      }>;
    } | null;
    address: {
      id: string;
      addressType: string | null;
      campusAddressId: string | null;
      civicAddressId: string | null;
      civicAddress: {
        street1: string | null;
        city: string | null;
        state: string | null;
        postalCode: string | null;
        countryCode: string | null;
      } | null;
      campusAddress: {
        id: string;
        room_id: string | null;
      } | null;
    } | null;
  },
  _view: ResourceViewType,
) => {
  // For now return list shape for detail as well
  const translations = row.translations ?? [];
  const frName = translations.find((t) => t.locale === 'fr')?.name;
  const enName = translations.find((t) => t.locale === 'en')?.name;
  const defaultName = frName || enName || translations?.[0]?.name || row.id;

  const typeTranslations = row.type?.translations ?? [];
  const typeName =
    typeTranslations.find((t) => t.locale === 'fr')?.name ||
    typeTranslations.find((t) => t.locale === 'en')?.name ||
    typeTranslations?.[0]?.name ||
    null;

  const statusTranslations = row.status?.translations ?? [];
  const statusName =
    statusTranslations.find((t) => t.locale === 'fr')?.name ||
    statusTranslations.find((t) => t.locale === 'en')?.name ||
    statusTranslations?.[0]?.name ||
    null;

  const unitTranslations = row.unit?.translations ?? [];
  const unitName =
    unitTranslations.find((t) => t.locale === 'fr')?.name ||
    unitTranslations.find((t) => t.locale === 'en')?.name ||
    unitTranslations?.[0]?.name ||
    null;

  // Build address string
  let addressString = null;
  if (row.address?.civicAddress) {
    const civic = row.address.civicAddress;
    addressString = [civic.street1, civic.city, civic.state, civic.postalCode]
      .filter(Boolean)
      .join(', ');
  }

  return {
    id: row.id,
    name: defaultName,
    type: typeName,
    status: statusName,
    statusText: statusName,
    equipmentCount: 0, // Not available in resource data
    jurisdiction: null, // Not available in resource data
    parentUnit: unitName,
    scientificDirector: null, // Not available in resource data
    technicalDirector: null, // Not available in resource data
    address: addressString,
    updatedAt: row.updatedAt,
  };
};

export const dbInfrastructuresToCollection = (
  rows: Array<DbInfrastructure>,
  view: CollectionViewType,
):
  | InfrastructureList[]
  | Schema.Schema.Type<typeof InfrastructureSelectSchema>[] => {
  if (view === 'select') {
    // Convert to simple value/label pairs
    return rows.map((raw) => {
      const translations = raw.translations ?? [];
      const frName = translations.find((t) => t.locale === 'fr')?.name;
      const enName = translations.find((t) => t.locale === 'en')?.name;
      const label = frName || enName || translations?.[0]?.name || raw.id;
      return { value: raw.id, label: String(label ?? raw.id) };
    });
  }
  return dbInfrastructuresToList(rows);
};
