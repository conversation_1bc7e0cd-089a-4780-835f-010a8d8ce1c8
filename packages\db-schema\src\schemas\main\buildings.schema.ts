import { users } from '@/schemas/auth/auth.schema';
import { civicAddresses } from '@/schemas/main/addresses.schema';
import { campuses } from '@/schemas/main/campuses.schema';
import { locales } from '@/schemas/main/locales.schema';
import { rooms } from '@/schemas/main/rooms.schema';
import { unitParents } from '@/schemas/main/units.schema';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  boolean,
  index,
  pgTable,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const buildings = pgTable(
  'buildings',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    isActive: boolean().default(true),
    campusId: text().references(() => campuses.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    civicAddressId: text()
      // .notNull() // TODO: add back notNull() once we've added the address
      .references(() => civicAddresses.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    jurisdictionId: text().references(() => unitParents.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    sadId: text(),
    diId: text(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      campusIdx: index().on(table.campusId),
    },
  ],
);

export const buildingsRelations = relations(buildings, ({ one, many }) => ({
  campus: one(campuses, {
    fields: [buildings.campusId],
    references: [campuses.id],
  }),
  civicAddress: one(civicAddresses, {
    fields: [buildings.civicAddressId],
    references: [civicAddresses.id],
  }),
  jurisdiction: one(unitParents, {
    fields: [buildings.jurisdictionId],
    references: [unitParents.id],
  }),
  translations: many(buildingsI18N),
  rooms: many(rooms),
}));

export const buildingsI18N = pgTable(
  'buildings_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => buildings.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
    otherNames: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const buildingsI18NRelations = relations(buildingsI18N, ({ one }) => ({
  building: one(buildings, {
    fields: [buildingsI18N.dataId],
    references: [buildings.id],
  }),
  locale: one(locales, {
    fields: [buildingsI18N.locale],
    references: [locales.code],
  }),
}));
