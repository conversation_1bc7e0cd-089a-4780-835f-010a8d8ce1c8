# Permissions System Reference

This document describes the application's permission system architecture. It details the permission groups (technical capabilities) and roles (business functions) that make up the access control system.

**Fundamental Principle**: Any permission group granting write access (`create`, `update`, `delete`) implicitly includes the necessary read permissions (`read`). This ensures that a user can always see what they are acting upon.

---

## 1. Permission Groups Catalog

Permission groups are technical, reusable "keychains". They answer the question: **"WHAT can be done?"**.

### Domain: User (User Management)
| Permission Group | Description                                   | Associated Numeric Permissions |
|:-----------------|:----------------------------------------------|:-------------------------------|
| `CanViewUsers`   | Allows viewing, searching, and sorting users. | `[19]`                         |
| `CanCreateUser`  | Allows creating a new user.                   | `[6, 19]`                      |
| `CanEditUser`    | Allows modifying and deactivating a user.     | `[7, 11, 19]`                  |
| `CanDeleteUser`  | Allows deleting a user.                       | `[10, 19]`                     |
| `UserManagement` | Full management rights over users.            | `[6, 7, 10, 11, 19]`           |

### Domain: PermissionGroup (Group Management)
| Permission Group                  | Description                                      | Associated Numeric Permissions |
|:----------------------------------|:-------------------------------------------------|:-------------------------------|
| `CanViewPermissionGroups`         | Allows viewing the list of groups.               | `[15, 19]`                     |
| `CanManagePermissionGroupMembers` | Allows assigning/removing users from groups.     | `[14]`                         |

### Domain: Infrastructure
| Permission Group           | Description                                   | Associated Numeric Permissions             |
|:---------------------------|:----------------------------------------------|:-------------------------------------------|
| `CanViewInfrastructures`   | Allows viewing infrastructures and exporting. | `[52, 62, 78]`                             |
| `CanCreateInfrastructure`  | Allows creating an infrastructure.            | `[27, 52, 62, 78]`                         |
| `CanEditInfrastructure`    | Allows modifying an infrastructure.           | `[28, 29, 31, 32, 33, 34, 52, 62, 69, 78]` |
| `CanDeleteInfrastructure`  | Allows deleting an infrastructure.            | `[30, 52, 62, 78]`                         |
| `InfrastructureManagement` | Full management rights over infrastructures.  | `[27, 28, 29, 30, 31, 32, 33, 34, 69]`     |

### Domain: Equipment
| Permission Group      | Description                                           | Associated Numeric Permissions                         |
|:----------------------|:------------------------------------------------------|:-------------------------------------------------------|
| `CanViewEquipment`    | Allows viewing equipment, its details, and exporting. | `[52, 54, 61, 64, 65, 66, 79]`                         |
| `CanCreateEquipment`  | Allows creating new equipment.                        | `[35, 52, 54, 61, 64, 65, 66, 79]`                     |
| `CanEditEquipment`    | Allows modifying equipment.                           | `[36, 37, 39, 40, 41, 42, 52, 54, 61, 64, 65, 66, 79]` |
| `CanDeleteEquipment`  | Allows deleting equipment.                            | `[38, 52, 54, 61, 64, 65, 66, 79]`                     |
| `EquipmentManagement` | Full management rights over equipment.                | `[35, 36, 37, 38, 39, 40, 41, 42]`                     |

### Domain: Directory
| Permission Group                      | Description                                           | Associated Numeric Permissions |
|:--------------------------------------|:------------------------------------------------------|:-------------------------------|
| `CanAccessDirectory`                  | Allows having the "Directory" tab in the menu.        | `[17]`                         |
| `CanCreateDirectoryEntity`            | Allows creating a generic directory object.           | `[48]`                         |
| `CanEditDirectoryEntity`              | Allows modifying and deactivating a directory object. | `[49, 51]`                     |
| `CanDeleteDirectoryEntity`            | Allows deleting a directory object.                   | `[50]`                         |
| `CanManageDirectoryEntityPermissions` | Allows modifying the "Editing Permissions" section.   | `[71]`                         |

### Domain: Building
| Permission Group     | Description                                 | Associated Numeric Permissions                                       |
|:---------------------|:--------------------------------------------|:---------------------------------------------------------------------|
| `CanViewBuilding`    | Allows viewing buildings and their details. | `[building-read]`                                                    |
| `CanCreateBuilding`  | Allows creating new buildings.              | `[building-create]`                                                  |
| `CanEditBuilding`    | Allows modifying building information.      | `[building-update]`                                                  |
| `CanDeleteBuilding`  | Allows deleting buildings.                  | `[building-delete]`                                                  |
| `BuildingManagement` | Full management rights over buildings.      | `[building-read, building-create, building-update, building-delete]` |

### Domain: Campus
| Permission Group   | Description                                    | Associated Numeric Permissions                               |
|:-------------------|:-----------------------------------------------|:-------------------------------------------------------------|
| `CanViewCampus`    | Allows viewing campus information and details. | `[campus-read]`                                              |
| `CanCreateCampus`  | Allows creating new campus entries.            | `[campus-create]`                                            |
| `CanEditCampus`    | Allows modifying campus information.           | `[campus-update]`                                            |
| `CanDeleteCampus`  | Allows deleting campus entries.                | `[campus-delete]`                                            |
| `CampusManagement` | Full management rights over campus data.       | `[campus-read, campus-create, campus-update, campus-delete]` |

### Domain: Funding Project
| Permission Group           | Description                                        | Associated Numeric Permissions                                                               |
|:---------------------------|:---------------------------------------------------|:---------------------------------------------------------------------------------------------|
| `CanViewFundingProject`    | Allows viewing funding projects and their details. | `[fundingProject-read]`                                                                      |
| `CanCreateFundingProject`  | Allows creating new funding projects.              | `[fundingProject-create]`                                                                    |
| `CanEditFundingProject`    | Allows modifying funding project information.      | `[fundingProject-update]`                                                                    |
| `CanDeleteFundingProject`  | Allows deleting funding projects.                  | `[fundingProject-delete]`                                                                    |
| `FundingProjectManagement` | Full management rights over funding projects.      | `[fundingProject-read, fundingProject-create, fundingProject-update, fundingProject-delete]` |

### Domain: Institution
| Permission Group        | Description                               | Associated Numeric Permissions                                                   |
|:------------------------|:------------------------------------------|:---------------------------------------------------------------------------------|
| `CanViewInstitution`    | Allows viewing institution information.   | `[institution-read]`                                                             |
| `CanCreateInstitution`  | Allows creating new institutions.         | `[institution-create]`                                                           |
| `CanEditInstitution`    | Allows modifying institution information. | `[institution-update]`                                                           |
| `CanDeleteInstitution`  | Allows deleting institutions.             | `[institution-delete]`                                                           |
| `InstitutionManagement` | Full management rights over institutions. | `[institution-read, institution-create, institution-update, institution-delete]` |

### Domain: People
| Permission Group   | Description                                     | Associated Numeric Permissions                               |
|:-------------------|:------------------------------------------------|:-------------------------------------------------------------|
| `CanViewPeople`    | Allows viewing people profiles and information. | `[people-read]`                                              |
| `CanCreatePeople`  | Allows creating new people profiles.            | `[people-create]`                                            |
| `CanEditPeople`    | Allows modifying people information.            | `[people-update]`                                            |
| `CanDeletePeople`  | Allows deleting people profiles.                | `[people-delete]`                                            |
| `PeopleManagement` | Full management rights over people data.        | `[people-read, people-create, people-update, people-delete]` |

### Domain: Room
| Permission Group | Description                                  | Associated Numeric Permissions                       |
|:-----------------|:---------------------------------------------|:-----------------------------------------------------|
| `CanViewRoom`    | Allows viewing room information and details. | `[room-read]`                                        |
| `CanCreateRoom`  | Allows creating new rooms.                   | `[room-create]`                                      |
| `CanEditRoom`    | Allows modifying room information.           | `[room-update]`                                      |
| `CanDeleteRoom`  | Allows deleting rooms.                       | `[room-delete]`                                      |
| `RoomManagement` | Full management rights over rooms.           | `[room-read, room-create, room-update, room-delete]` |

### Domain: Unit
| Permission Group | Description                                  | Associated Numeric Permissions                       |
|:-----------------|:---------------------------------------------|:-----------------------------------------------------|
| `CanViewUnit`    | Allows viewing unit information and details. | `[unit-read]`                                        |
| `CanCreateUnit`  | Allows creating new units.                   | `[unit-create]`                                      |
| `CanEditUnit`    | Allows modifying unit information.           | `[unit-update]`                                      |
| `CanDeleteUnit`  | Allows deleting units.                       | `[unit-delete]`                                      |
| `UnitManagement` | Full management rights over units.           | `[unit-read, unit-create, unit-update, unit-delete]` |

### Domain: Vendor
| Permission Group   | Description                                    | Associated Numeric Permissions                               |
|:-------------------|:-----------------------------------------------|:-------------------------------------------------------------|
| `CanViewVendor`    | Allows viewing vendor information and details. | `[vendor-read]`                                              |
| `CanCreateVendor`  | Allows creating new vendors.                   | `[vendor-create]`                                            |
| `CanEditVendor`    | Allows modifying vendor information.           | `[vendor-update]`                                            |
| `CanDeleteVendor`  | Allows deleting vendors.                       | `[vendor-delete]`                                            |
| `VendorManagement` | Full management rights over vendors.           | `[vendor-read, vendor-create, vendor-update, vendor-delete]` |

### Domain: Controlled Lists
| Permission Group                | Description                                           | Associated Numeric Permissions |
|:--------------------------------|:------------------------------------------------------|:-------------------------------|
| `CanAccessControlledLists`      | Allows having the "Controlled Lists" tab in the menu. | -                              |
| `CanCreateControlledListEntity` | Allows creating a controlled list entity.             | -                              |
| `CanEditControlledListEntity`   | Allows modifying a controlled list entity.            |                                |
| `CanDeleteControlledListEntity` | Allows deleting a controlled list entity.             | -                              |
| `CanManageControlledList`       | Allows managing a controlled list.                    | -                              |

### Access Levels (Data Visibility)
| Permission Group     | Description                                             | Associated Numeric Permissions |
|:---------------------|:--------------------------------------------------------|:-------------------------------|
| `CanViewUdeMData`    | Unlocks reading data with "UdeM" visibility.            | `[72]`                         |
| `CanViewPartnerData` | Unlocks reading data with "UdeM & Partners" visibility. | `[67]`                         |
| `CanViewPrivateData` | Unlocks reading "private" data.                         | `[68]`                         |

---

## 2. Roles and Inheritance Catalog

Roles represent a business function. The system uses **base roles** (technical templates) to centralize permissions, and **specific roles** (assignable to users) which inherit from these bases.

### Base Roles (Non-contextual)

| Base Role      | Description                                                             | Inherits from |
|:---------------|:------------------------------------------------------------------------|:--------------|
| `BaseExplorer` | Provides global read permissions for discovery and reference across all domains. | -             |
| `UdeM`         | Allows users to access data with "UdeM" visibility.                     | -             |
| `UdeMPartner`  | Allows users to access data with "UdeMPartner" visibility.              | -             |
| `User`         | Allows users to access data with "Private" visibility.                  | -             |

*These roles will be automatically assigned to users upon account creation.*
- If the user logs in through a UdeM account, they will automatically receive the `UdeM` role.
- If the user is created by an establishment manager, they will receive the `UdeMPartner` role.
- If the user creates an account via signup form, they will receive the `User` role.

### Specific Roles (Assignable to Users)

#### Contextual Roles
*These roles must be assigned with a context (`institution`, `unit`, `infrastructure`).*

**"Explorer" Family (Read-only)**
- **`InstitutionExplorer`** (Inherits from: `BaseExplorer`)
- **`UnitExplorer`** (Inherits from: `BaseExplorer`)
- **`InfrastructureExplorer`** (Inherits from: `BaseExplorer`)
- **`EquipmentExplorer`** (Inherits from: `BaseExplorer`)

**"Editor" Family (Data management)**
- **`InstitutionEditor`** (Inherits from: `BaseExplorer`)
- **`UnitEditor`** (Inherits from: `BaseExplorer`)
- **`InfrastructureEditor`** (Inherits from: `BaseExplorer`)

**"Manager" Family (Management of data AND people/permissions)**
- **`EquipmentManager`**
    - **Inherits from**: `BaseExplorer`
    - **Additional Permissions**: Equipment management permissions
- **`InfrastructureManager`**
    - **Inherits from**: `EquipmentManager`
    - **Additional Permissions**: Infrastructure management permissions, `CanManageDirectoryEntityPermissions`
- **`UnitManager`**
    - **Inherits from**: `InfrastructureManager`, `UnitEditor`
    - **Additional Permissions**: `UserManagement`, `CanManagePermissionGroupMembers`
- **`InstitutionManager`**
    - **Inherits from**: `UnitManager`, `InstitutionEditor`

#### Global Roles (Without Context)
*These roles are assigned without a context and apply to the entire platform.*

- **`SuperExplorer`** (Inherits from: `BaseExplorer`)
- **`SuperEditor`** (Inherits from: `SuperExplorer`)
- **`SystemAdmin`** (Inherits from: `InstitutionManager`, `SuperEditor`)
- **`UdeMUser`** (Contains: `CanViewUdeMData`, `CanViewPartnerData`)
- **`PartnerUser`** (Contains: `CanViewPartnerData`)
- **`AuthenticatedUser`** (Contains no special permissions by default)

---

# Permissions: Role Inheritance vs. Contextual Access

This document clarifies a fundamental concept of our permission system: the coexistence and necessity of two types of inheritance.

1.  **Contextual Inheritance** (managed by `AccessTreeServiceLive`)
2.  **Role Inheritance** (defined in the role structure)

Understanding their distinction is essential for correctly using and maintaining our access control system.

## The Two Pillars of Our System

Think of access as answering two successive questions:
-   **"WHERE?"**: Does the user have the right to be here? (Contextual Access)
-   **"WHAT?"**: Once there, what are they allowed to do? (Role Inheritance)

### 1. Contextual Inheritance: The "WHERE"

This is the system's ability to infer an access right based on the resource hierarchy.

-   **Principle**: If a role is assigned to a user in the context of a `Unit`, the system automatically grants them a **"line of sight"** to all resources below it in the hierarchy tree (`infrastructures`, `equipment`).
-   **What it answers**: "Does this user have a valid access path to infrastructure X?"
-   **Example**: A `UnitManager` assigned to Unit A has contextual access to all infrastructures within that unit. We do not need role inheritance to establish this access path.

### 2. Role Inheritance: The "WHAT"

This is the ability of a role to aggregate the **permissions** of other roles.

-   **Principle**: This inheritance does not define *to which resources* you have access, but *what actions* you are allowed to perform on those resources once access is confirmed. It aggregates **capabilities**.
-   **What it answers**: "Now that I am at infrastructure X, do I have the `infrastructure:update` or `infrastructure:delete` permission?"
-   **Example**: By having `UnitManager` inherit from `InfrastructureManager`, we ensure that it possesses not only its own unit management permissions but also all infrastructure management capabilities.

## How It Works in Practice: A Two-Step Check

Our `UserPermissionsServiceLive` uses these two pillars in a logical order:

1.  **Step 1: Access Check (The "WHERE")**
    The system uses the contextual tree to verify if the user has a line of sight to the resource.
    ```typescript
    userHasAccessToResource('userId', 'infrastructure', 'resourceId')
    ```
2.  **Step 2: Permission Check (The "WHAT")**
    If access is confirmed, the system checks if the user's set of roles (including inherited permissions) contains the requested action.
    ```typescript
    userHasPermission({ userId: 'userId', domain: 'infrastructure', action: 'update' })
    ```
    If either of these steps fails, the operation is denied.

## The Problematic Scenario: What Would Happen Without Role Inheritance?

Let's imagine for a moment that we rely solely on contextual access and that the `UnitManager` role does **not** inherit from `InfrastructureManager`.

**Scenario Setup:**
-   Bob has the `UnitManager` role on Unit A.
-   Infrastructure X is located in Unit A.
-   The `UnitManager` role has permissions to manage users, but not those for managing infrastructures (which are in the `InfrastructureManager` role).

**Operation Flow:**
1.  **Access Check:** Bob wants to modify Infrastructure X. The system checks:
    -   `userHasAccessToResource('Bob', 'infrastructure', 'Infra-X-ID')` -> **SUCCESS!**
    -   `userHasPermission({ userId: 'Bob', domain: 'infrastructure', action: 'update' })` -> **FAILURE!**
    -   *Explanation: Although Bob is "at the door" of the infrastructure, his keychain (his role permissions) does not contain the `infrastructure:update` key. This key is in the `InfrastructureManager` role, from which he does not inherit in this scenario.*

> ### The Analogy: The Key to the Door vs. The Right to Paint the Walls
>
> -   **Contextual Inheritance** gives you the **key to the infrastructure's door**.
> -   **Role Inheritance** gives you the **right to paint the walls** once you are inside.
>
> You need both to be able to perform an action.

## Conclusion

The design where `UnitManager` inherits from `InfrastructureManager` is not a redundancy, but a **fundamental necessity**. It ensures that a manager at a higher hierarchical level (`Unit`) automatically possesses all the **capabilities** of the levels they supervise (`Infrastructure`, `Equipment`).

Our system is robust because it intelligently separates and combines these two logics:
-   The **assignment context** determines the **scope of accessible resources**.
-   **Role inheritance** determines the **range of possible actions** on those resources.

---

## 3. Role Inheritance Diagram

This diagram visualizes the complete hierarchy. An arrow from A to B (`A --> B`) means that **A inherits from B**.

```mermaid
graph TD
    subgraph "Global Roles (Without Context)"
        SystemAdmin(SystemAdmin)
        SuperEditor(SuperEditor)
        SuperExplorer(SuperExplorer)
        UdeMUser(UdeM User)
        PartnerUser(Partner User)
        AuthenticatedUser(Authenticated User)
    end

    subgraph "Contextual Roles (Assigned with Institution, Unit, etc.)"
        %% Management Roles
        InstitutionManager(InstitutionManager)
        UnitManager(UnitManager)
        InfrastructureManager(InfrastructureManager)
        EquipmentManager(EquipmentManager)
        
        %% Editor Roles
        InstitutionEditor(InstitutionEditor)
        UnitEditor(UnitEditor)
        InfrastructureEditor(InfrastructureEditor)

        %% Explorer Roles
        InstitutionExplorer(InstitutionExplorer)
        UnitExplorer(UnitExplorer)
        InfrastructureExplorer(InfrastructureExplorer)
    end

    subgraph "Base Roles (Technical Templates)"
        BaseExplorer(<b>BaseExplorer</b>)
    end
    
    %% ---- Defining Connections ----

    %% Global Roles Inheritance
    SystemAdmin --> InstitutionManager
    SystemAdmin --> SuperEditor
    SuperEditor --> SuperExplorer
    SuperExplorer --> BaseExplorer
    
    %% Manager Roles Inheritance
    InstitutionManager --> UnitManager
    InstitutionManager --> InstitutionEditor
    UnitManager --> InfrastructureManager
    UnitManager --> UnitEditor
    InfrastructureManager --> EquipmentManager
    EquipmentManager --> BaseExplorer
    
    %% Editor Roles Inheritance
    InstitutionEditor --> BaseEditor
    UnitEditor --> BaseEditor
    InfrastructureEditor --> BaseEditor
    
    %% Explorer Roles Inheritance
    InstitutionExplorer --> BaseExplorer
    UnitExplorer --> BaseExplorer
    InfrastructureExplorer --> BaseExplorer
    
    %% Base Roles Inheritance
    BaseEditor --> BaseExplorer

    %% ---- Styling ----
    style SystemAdmin fill:#c9f,stroke:#333,stroke-width:4px
    style BaseEditor fill:#ececff,stroke:#333,stroke-dasharray: 5 5, color:#000
    style BaseExplorer fill:#ececff,stroke:#333,stroke-dasharray: 5 5, color:#000

    style InstitutionManager fill:#d6b3ff, color:#000
    style UnitManager fill:#d6b3ff, color:#000
    style InfrastructureManager fill:#d6b3ff, color:#000
    style EquipmentManager fill:#d6b3ff, color:#000
    
    style InstitutionEditor fill:#a3c4ff, color:#000
    style UnitEditor fill:#a3c4ff, color:#000
    style InfrastructureEditor fill:#a3c4ff, color:#000

    style InstitutionExplorer fill:#ffeca3, color:#000
    style UnitExplorer fill:#ffeca3, color:#000
    style InfrastructureExplorer fill:#ffeca3, color:#000
```