import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useGetInfiniteInfrastructures } from '@/hooks/infrastructure/useGetInfiniteInfrastructures';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import { mapListDataToSelectOption } from '@/services/mappers/controlled-list-select-data';
import { Textarea } from '@/ui/textarea';
import { useTranslations } from 'next-intl';
import { useCallback, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

export const EquipmentDescription = () => {
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations(
    'equipments.form.sections.description.description',
  );
  const locale = useAvailableLocale();
  const { control } = useFormContext<EquipmentFormSchema>();

  const {
    append: appendSpecification,
    fields: specificationFields,
    remove: removeSpecification,
  } = useFieldArray<EquipmentFormSchema>({
    control,
    name: 'specifications',
  });

  const handleAddSpecificationTranslation = useCallback(
    (locale: string) => {
      appendSpecification({
        locale: locale,
        value: '',
      });
    },
    [appendSpecification],
  );

  const handleRemoveSpecificationTranslation = useCallback(
    (index: number) => {
      removeSpecification(index);
    },
    [removeSpecification],
  );

  const {
    append: appendUsageContexts,
    fields: usageContextsFields,
    remove: removeUsageContexts,
  } = useFieldArray<EquipmentFormSchema>({
    control,
    name: 'usageContexts',
  });

  const handleAddUsageContextsTranslation = useCallback(
    (locale: string) => {
      appendUsageContexts({
        locale: locale,
        value: '',
      });
    },
    [appendUsageContexts],
  );

  const handleRemoveUsageContextsTranslation = useCallback(
    (index: number) => {
      removeUsageContexts(index);
    },
    [removeUsageContexts],
  );

  const {
    append: appendSstRisk,
    fields: sstRisksFields,
    remove: removeSstRisk,
  } = useFieldArray<EquipmentFormSchema>({
    control,
    name: 'sstRisks',
  });

  const handleAddSstRiskTranslation = useCallback(
    (locale: string) => {
      appendSstRisk({
        locale: locale,
        value: '',
      });
    },
    [appendSstRisk],
  );

  const handleRemoveSstRiskTranslation = useCallback(
    (index: number) => {
      removeSstRisk(index);
    },
    [removeSstRisk],
  );

  const {
    append: appendComment,
    fields: commentsFields,
    remove: removeComment,
  } = useFieldArray<EquipmentFormSchema>({
    control,
    name: 'comments',
  });

  const handleAddCommentTranslation = useCallback(
    (locale: string) => {
      appendComment({
        locale: locale,
        value: '',
      });
    },
    [appendComment],
  );

  const handleRemoveCommentTranslation = useCallback(
    (index: number) => {
      removeComment(index);
    },
    [removeComment],
  );

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['person', 'fundingProjects']);

  const [searchTerm, setSearchTerm] = useState('');
  const infrastructures = useGetInfiniteInfrastructures({
    params: defaultRieServiceParams(locale),
    queryParams: searchTerm ? `q=${searchTerm}` : '',
    select: (response) =>
      response.pages.flatMap((page) =>
        page.data.map(mapListDataToSelectOption),
      ),
  });

  return (
    <FormSubsection title={tEquipments('title')}>
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        fetchNextPage={infrastructures.fetchNextPage}
        fieldLabel={tEquipments('fields.infrastructure.label')}
        fieldName="infrastructure"
        hasNextPage={infrastructures.hasNextPage ?? false}
        isFetching={infrastructures.isFetching}
        isFetchingNextPage={infrastructures.isFetchingNextPage}
        onSearchChange={setSearchTerm}
        options={infrastructures.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />

      <MultiselectField
        fieldName="associatedFinancingProjects"
        label={tEquipments('fields.associatedFinancingProjects.label')}
        options={selectsData.local?.data ?? []}
        placeholder={tCommon('select')}
      />
      <MultiselectField
        fieldName="technicalManager"
        label={tEquipments('fields.technicalManager.label')}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
      />
      <MultiselectField
        fieldName="sstManager"
        label={tEquipments('fields.sstManager.label')}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="specifications"
        fields={specificationFields}
        label={(locale) =>
          tEquipments('fields.specifications.label', {
            locale: tCommon(locale),
          })
        }
        onAddTranslation={handleAddSpecificationTranslation}
        onRemoveTranslation={handleRemoveSpecificationTranslation}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="usageContexts"
        fields={usageContextsFields}
        label={(locale) =>
          tEquipments('fields.usageContexts.label', { locale: tCommon(locale) })
        }
        onAddTranslation={handleAddUsageContextsTranslation}
        onRemoveTranslation={handleRemoveUsageContextsTranslation}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="sstRisks"
        fields={sstRisksFields}
        label={(locale) =>
          tEquipments('fields.sstRisks.label', { locale: tCommon(locale) })
        }
        onAddTranslation={handleAddSstRiskTranslation}
        onRemoveTranslation={handleRemoveSstRiskTranslation}
      />
      <FormField
        control={control}
        name="acquisitionCostInCash"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              label={tEquipments('fields.acquisitionCostInCash.label')}
            />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="acquisitionCostInNature"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              label={tEquipments('fields.acquisitionCostInNature.label')}
            />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="comments"
        fields={commentsFields}
        label={(locale) =>
          tEquipments('fields.comments.label', { locale: tCommon(locale) })
        }
        onAddTranslation={handleAddCommentTranslation}
        onRemoveTranslation={handleRemoveCommentTranslation}
      />
    </FormSubsection>
  );
};
