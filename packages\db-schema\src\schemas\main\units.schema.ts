import { DbUtils } from '@rie/utils';
import { and, eq, isNotNull, isNull, or, relations } from 'drizzle-orm';
import {
  boolean,
  pgEnum,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';
import { users } from '../auth/auth.schema';
import { buildings } from './buildings.schema';
import { guids } from './guids.schema';
import { infrastructures } from './infrastructures.schema';
import {
  institutionAssociatedUnits,
  institutions,
} from './institutions.schema';
import { locales } from './locales.schema';
import { people, peopleRoleTypes } from './people.schema';

export const units = pgTable(
  'units',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    isActive: boolean().default(true),
    guidId: text()
      .notNull()
      .references(() => guids.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    typeId: text()
      .notNull()
      .references(() => unitTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    parentId: text().references(() => unitParents.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      guidIdUnique: unique().on(table.guidId),
    },
  ],
);

export const unitsI18N = pgTable(
  'units_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => units.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
    otherNames: text(),
    acronyms: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const unitTypes = pgTable('unit_types', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
});

export const unitTypesI18N = pgTable(
  'unit_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => unitTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const unitAssociatedPeople = pgTable(
  'unit_associated_people',
  {
    unitId: text()
      .notNull()
      .references(() => units.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    roleTypeId: text()
      .notNull()
      .references(() => peopleRoleTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      unitAssociatedPersonPk: primaryKey({
        columns: [table.unitId, table.personId],
      }),
    },
  ],
);

export const unitAssociatedBuildings = pgTable(
  'unit_associated_buildings',
  {
    unitId: text()
      .notNull()
      .references(() => units.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    buildingId: text()
      .notNull()
      .references(() => buildings.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      unitBuildingPk: primaryKey({
        columns: [table.unitId, table.buildingId],
      }),
    },
  ],
);

export const unitAssociatedJurisdictions = pgTable(
  'unit_associated_jurisdictions',
  {
    unitId: text()
      .notNull()
      .references(() => units.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    unitParentId: text()
      .notNull()
      .references(() => unitParents.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      unitJurisdictionPk: primaryKey({
        columns: [table.unitId, table.unitParentId],
      }),
    },
  ],
);

export const unitAssociatedJurisdictionsRelations = relations(
  unitAssociatedJurisdictions,
  ({ one }) => ({
    unit: one(units, {
      fields: [unitAssociatedJurisdictions.unitId],
      references: [units.id],
    }),
    jurisdiction: one(unitParents, {
      fields: [unitAssociatedJurisdictions.unitParentId],
      references: [unitParents.id],
    }),
  }),
);

export const unitsRelations = relations(units, ({ one, many }) => ({
  type: one(unitTypes, {
    fields: [units.typeId],
    references: [unitTypes.id],
  }),
  parent: one(unitParents, {
    fields: [units.parentId],
    references: [unitParents.id],
    relationName: 'units_parent',
  }),
  translations: many(unitsI18N),
  associatedPeople: many(unitAssociatedPeople),
  associatedBuildings: many(unitAssociatedBuildings),
  associatedInstitutions: many(institutionAssociatedUnits),
  infrastructures: many(infrastructures),
  guid: one(guids, {
    fields: [units.guidId],
    references: [guids.id],
  }),
}));

export const unitsI18NRelations = relations(unitsI18N, ({ one }) => ({
  units: one(units, {
    fields: [unitsI18N.dataId],
    references: [units.id],
  }),
}));

export const unitTypesI18NRelations = relations(unitTypesI18N, ({ one }) => ({
  unitsType: one(unitTypes, {
    fields: [unitTypesI18N.dataId],
    references: [unitTypes.id],
  }),
}));

export const unitTypesRelations = relations(unitTypes, ({ many }) => ({
  units: many(units),
  translations: many(unitTypesI18N),
}));

export const unitAssociatedPeopleRelations = relations(
  unitAssociatedPeople,
  ({ one }) => ({
    units: one(units, {
      fields: [unitAssociatedPeople.unitId],
      references: [units.id],
    }),
    person: one(people, {
      fields: [unitAssociatedPeople.personId],
      references: [people.id],
    }),
  }),
);

export const unitAssociatedBuildingsRelations = relations(
  unitAssociatedBuildings,
  ({ one }) => ({
    unit: one(units, {
      fields: [unitAssociatedBuildings.unitId],
      references: [units.id],
    }),
    building: one(buildings, {
      fields: [unitAssociatedBuildings.buildingId],
      references: [buildings.id],
    }),
  }),
);

export const unitParentTypes = pgEnum('jurisdiction_types', [
  'institution',
  'unit',
]);

export const unitParents = pgTable(
  'unit_parents',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    type: unitParentTypes().notNull(),
    institutionId: text().references(() => institutions.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    unitId: text(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      checkJurisdiction: or(
        and(
          eq(table.type, 'institution'),
          isNotNull(table.institutionId),
          isNull(table.unitId),
        ),
        and(
          eq(table.type, 'unit'),
          isNotNull(table.unitId),
          isNull(table.institutionId),
        ),
      ),
    },
  ],
);

export const jurisdictionsRelations = relations(
  unitParents,
  ({ one, many }) => ({
    institution: one(institutions, {
      fields: [unitParents.institutionId],
      references: [institutions.id],
    }),
    unit: one(units, {
      fields: [unitParents.unitId],
      references: [units.id],
    }),
    children: many(unitAssociatedJurisdictions),
    parents: many(unitAssociatedJurisdictions),
  }),
);
