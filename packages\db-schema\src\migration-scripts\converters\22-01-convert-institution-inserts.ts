import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlOrganization {
  id: number;
  guid_id: number | null;
  table_ref: string;
}

interface PostgresInstitution {
  id: string;
  guidId: string | null;
  typeId: string | null;
}

export class OrganizationMigrationConverter extends BaseConverter {
  private organizationMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlOrganization[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return values.table_ref === 'etablissement'
      ? [
          {
            id: Number.parseInt(values.organization_id),
            guid_id:
              values.guid_id === 'NULL'
                ? null
                : Number.parseInt(values.guid_id),
            table_ref: this.parseNullableString(values.table_ref) || '',
          },
        ]
      : [];
  }

  private async convertToPostgres(
    mysqlOrganization: MySqlOrganization,
    typeEtablissementMap: Map<number, number>,
    typeEtablissementIdMappings: Record<string, string>,
  ): Promise<PostgresInstitution | null> {
    // Skip if table_ref is not 'etablissement'
    // We've already filtered in parseInsertStatement, but double-check here
    if (mysqlOrganization.table_ref !== 'etablissement') {
      return null;
    }

    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the guid
    let newGuidId: string | null = null;
    if (mysqlOrganization.guid_id !== null) {
      try {
        const guidIdMappings = await this.loadEntityIdMappings('guid');
        newGuidId = guidIdMappings[mysqlOrganization.guid_id.toString()];
        if (!newGuidId) {
          console.warn(
            `No mapping found for guid_id: ${mysqlOrganization.guid_id}`,
          );
        }
      } catch (error) {
        console.warn(`Error loading guid mappings: ${error}`);
      }
    }

    // Get the type_etablissement_id for this organization
    let typeId: string | null = null;
    const typeEtablissementId = typeEtablissementMap.get(mysqlOrganization.id);
    if (typeEtablissementId !== undefined) {
      typeId =
        typeEtablissementIdMappings[typeEtablissementId.toString()] ?? null;
      if (!typeId) {
        console.warn(
          `No mapping found for type_etablissement_id: ${typeEtablissementId}`,
        );
        // Since typeId is required, we can't proceed without it
        return null;
      }
    } else {
      console.warn(
        `No type_etablissement_id found for organization_id: ${mysqlOrganization.id}`,
      );
      // Since typeId is required, we can't proceed without it
      return null;
    }

    // Store mapping for future reference
    this.organizationMappings.push({
      mysqlId: mysqlOrganization.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      guidId: newGuidId,
      typeId: typeId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for organization table (old MySQL table name)
      const organizationInsertStatements = this.extractInsertStatements(
        sqlContent,
        'organization',
      );

      if (organizationInsertStatements.length === 0) {
        console.log('No organization INSERT statements found.');
        return;
      }

      // Extract INSERT statements for etablissement table
      const etablissementInsertStatements = this.extractInsertStatements(
        sqlContent,
        'etablissement',
      );

      if (etablissementInsertStatements.length === 0) {
        console.log('No etablissement INSERT statements found.');
        return;
      }

      // Create a map of organization_id to type_etablissement_id
      const typeEtablissementMap = new Map<number, number>();
      for (const statement of etablissementInsertStatements) {
        const values = this.extractValuesFromInsertStatement(statement);
        const organizationId = Number.parseInt(values.organization_id);
        const typeEtablissementId = Number.parseInt(
          values.type_etablissement_id,
        );
        typeEtablissementMap.set(organizationId, typeEtablissementId);
      }

      console.log(`Found ${typeEtablissementMap.size} etablissement records`);

      // Load type_etablissement ID mappings
      const typeEtablissementIdMappings =
        await this.loadEntityIdMappings('type_etablissement');

      const mysqlOrganizations: MySqlOrganization[] = [];
      const allPostgresInstitutions: PostgresInstitution[] = [];

      console.log(
        `Processing ${organizationInsertStatements.length} insitutions INSERT statements`,
      );

      // Process each INSERT statement
      for (const statement of organizationInsertStatements) {
        const orgs = this.parseInsertStatement(statement);
        if (orgs.length === 0) {
          continue; // Skip if not 'etablissement'
        }

        mysqlOrganizations.push(...orgs);

        for (const org of orgs) {
          const postgresInstitution = await this.convertToPostgres(
            org,
            typeEtablissementMap,
            typeEtablissementIdMappings,
          );
          if (postgresInstitution) {
            allPostgresInstitutions.push(postgresInstitution);
          } else {
            console.log(`Failed to convert insitution with id=${org.id}`);
          }
        }
      }

      const columns = ['id', 'guid_id', 'type_id'];

      // Generate output with both inserts and mappings
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresInstitutions,
        'institutions',
        'Institution Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.organizationMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'etablissement', postgres: 'institutions' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresInstitutions.length} institutions records (filtered by table_ref='etablissement')`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
