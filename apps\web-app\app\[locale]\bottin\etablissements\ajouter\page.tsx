import { AddEstablishment } from '@/app/[locale]/bottin/etablissements/ajouter/add-establishment';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { establishmentFormSections } from '@/constants/bottin/establishment';
import { getQueryClientOptions } from '@/constants/query-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function NewEstablishmentPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  const formSections = await getFormSections({
    resourceName: 'establishments',
    sections: establishmentFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = ['establishmentType'];

  const t0 = performance.now();

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  );

  const t1 = performance.now();
  console.log(
    `Call to fetch controlledLists "establishmentType" took ${t1 - t0} milliseconds.`,
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AddEstablishment locale={locale} formSections={formSections} />
    </HydrationBoundary>
  );
}
