import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';

export class UnitParentUpdater extends BaseConverter {
  async updateUnitParents(
    inputPath: string,
    outputPath: string,
  ): Promise<void> {
    try {
      const unitParentIdMappings =
        await this.loadEntityIdMappings('juridiction');

      const postgresUnitUpdates = Object.entries(unitParentIdMappings).map(
        ([unitId, value]) => ({
          unitId,
          uniteParentId: value.unit_parent_id,
        }),
      );

      // Create SQL updates
      let updateStatements = this.generateCommentHeader(
        'Update Unit Parent References',
      );

      for (const update of postgresUnitUpdates) {
        updateStatements += `UPDATE "units" SET "parent_id" = '${update.uniteParentId}' WHERE "id" = '${update.unitId}';\n`;
      }

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the update statements to file
      await fs.appendFile(outputPath, updateStatements);

      console.log('Unit parent references updated successfully!');
      console.log(
        `- Generated ${postgresUnitUpdates.length} unit parent updates`,
      );
    } catch (error) {
      console.error('Error updating unit parent references:', error);
      throw error;
    }
  }
}
