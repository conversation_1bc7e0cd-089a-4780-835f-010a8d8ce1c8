import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { InputField } from '@/components/form-fields/input-field';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { UploadFile } from '@/components/upload-file/upload-file';
import { FILE_MAX_UPLOAD_SIZE_MB } from '@/constants/equipments';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { ToggleGroup, ToggleGroupItem } from '@/ui/toggle-group';
import { useTranslations } from 'next-intl';
import {
  type ArrayPath,
  type Control,
  type FieldValues,
  type Path,
  useFormContext,
} from 'react-hook-form';

type FileSourceProps<TFieldData extends FieldValues> = {
  control: Control<TFieldData>;
  fieldName: Path<TFieldData>;
};

export const FileSourceItem = <TFieldData extends FieldValues>({
  control,
  fieldName,
}: FileSourceProps<TFieldData>) => {
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations('equipments.form.sections.description');
  const locale = useAvailableLocale();
  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['documentationCategory']);
  const { clearErrors, watch } = useFormContext<TFieldData>();
  const fileSource = watch(fieldName);
  const descriptionFieldName = `${fieldName}.description`;
  const descriptionFields = useTranslatedField(
    control,
    descriptionFieldName as ArrayPath<TFieldData>,
  );

  const getDefaultValueForType = (type: 'file' | 'url') => {
    return {
      categorie: '',
      data: undefined,
      description: [{ locale, value: '' }],
      isConfidential: false,
      type,
    };
  };

  return (
    <div className="grid gap-y-4">
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName={descriptionFieldName as Path<TFieldData>}
        fields={descriptionFields.fields}
        label={(locale) =>
          tEquipments('media.fields.files.descriptionWithLocale', {
            locale: tCommon(locale),
          })
        }
        maxLength={2000}
        onAddTranslation={descriptionFields.handleAddTranslation}
        onRemoveTranslation={descriptionFields.handleRemoveTranslation}
      />
      <FormField
        control={control}
        name={fieldName}
        render={({ field }) => (
          <FormItem className="w-fit">
            <LabelTooltip htmlFor={fieldName} label="" />
            <FormControl>
              <ToggleGroup
                onValueChange={(value) => {
                  if (value !== '') {
                    const newValue = getDefaultValueForType(
                      value as 'file' | 'url',
                    );
                    field.onChange(newValue);
                    clearErrors(fieldName);
                  }
                }}
                type="single"
                value={field.value.type}
                variant="outline"
              >
                <ToggleGroupItem value="file">
                  {tCommon('file')}
                </ToggleGroupItem>
                <ToggleGroupItem value="url">{tCommon('url')}</ToggleGroupItem>
              </ToggleGroup>
            </FormControl>
          </FormItem>
        )}
      />
      <div className="grid w-full gap-y-4">
        {fileSource.type === 'file' ? (
          <>
            <UploadFile
              description={tEquipments('media.fields.files.fileSize', {
                size: FILE_MAX_UPLOAD_SIZE_MB.toString(),
              })}
              fieldName={`${fieldName}.data` as Path<TFieldData>}
              label={
                fileSource.data?.name ??
                tEquipments('media.fields.files.upload')
              }
              source="documents"
            />
          </>
        ) : (
          <InputField
            className="w-full pb-0"
            fieldName={`${fieldName}.data` as Path<TFieldData>}
            label={tCommon('url')}
          />
        )}
        <ComboboxFieldInfiniteScroll
          clearErrorsOnChange
          controlledListKey="documentationCategory"
          fetchNextPage={selectsData.documentationCategory?.fetchNextPage}
          fieldLabel={tEquipments('media.fields.files.category')}
          fieldName={`${fieldName}.category`}
          hasNextPage={Boolean(selectsData.documentationCategory?.hasNextPage)}
          isFetching={Boolean(selectsData.documentationCategory?.isFetching)}
          isFetchingNextPage={Boolean(
            selectsData.documentationCategory?.isFetchingNextPage,
          )}
          onSearchChange={handleOnSearchTermChange}
          options={selectsData.documentationCategory?.data || []}
          placeholder={tCommon('select')}
          required={true}
        />
        <FormField
          control={control}
          name={`${fieldName}.isConfidential` as Path<TFieldData>}
          render={() => {
            return (
              <FormItem>
                <FormControl>
                  <div className="flex gap-x-2">
                    <Switch id={`${fieldName}.isConfidential`} />
                    <LabelTooltip
                      htmlFor={`${fieldName}.isConfidential`}
                      label={tEquipments('media.fields.files.isConfidential')}
                    />
                  </div>
                </FormControl>
                <FieldInfo>
                  <FormMessage />
                </FieldInfo>
              </FormItem>
            );
          }}
        />
      </div>
    </div>
  );
};
