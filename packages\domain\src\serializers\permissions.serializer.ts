import { DbPermissionSelectSchema } from '@rie/db-schema/entity-schemas';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import { PermissionDetailSchema, PermissionSelectSchema } from '../schemas';
import type {
  CollectionViewType,
  PermissionDetail,
  PermissionSelect,
  ResourceViewType,
} from '../types';

// Schema transformer for converting database permission to detail view
export const DbPermissionToPermissionDetail = Schema.transformOrFail(
  DbPermissionSelectSchema,
  PermissionDetailSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            domain: raw.domain,
            action: raw.action,
            createdAt: raw.createdAt,
            updatedAt: raw.updatedAt,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse permission for detail view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database permission to select view
export const DbPermissionToPermissionSelect = Schema.transformOrFail(
  DbPermissionSelectSchema,
  PermissionSelectSchema,
  {
    strict: true,
    decode: (dbPermission, _options, ast) => {
      return ParseResult.try({
        try: () => ({
          value: dbPermission.id,
          label: `${dbPermission.domain}:${dbPermission.action}`,
        }),
        catch: (error) =>
          new ParseResult.Type(
            ast,
            dbPermission,
            error instanceof Error
              ? error.message
              : 'Failed to transform permission to select format',
          ),
      });
    },
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of DbPermissions to PermissionDetail[] (individual permissions)
export const dbPermissionsToPermissionList = (
  dbPermissions: Schema.Schema.Type<typeof DbPermissionSelectSchema>[],
): PermissionDetail[] => {
  // Return individual permissions instead of grouping by domain
  return dbPermissions.map((dbPermission) => ({
    id: dbPermission.id,
    domain: dbPermission.domain,
    action: dbPermission.action,
    createdAt: dbPermission.createdAt,
    updatedAt: dbPermission.updatedAt,
  }));
};

// Helper function to transform an array of DbPermissions to PermissionSelect[]
export const dbPermissionsToPermissionSelect = (
  dbPermissions: Schema.Schema.Type<typeof DbPermissionSelectSchema>[],
): PermissionSelect[] => {
  return dbPermissions.map((dbPermission) => {
    const result = Schema.decodeEither(DbPermissionToPermissionSelect)(
      dbPermission,
    );

    if (result._tag === 'Left') {
      console.error(`Transform error: ${result.left.message}`);
      // Return a default select object instead of throwing
      return {
        value: dbPermission.id,
        label: `${dbPermission.domain}:${dbPermission.action}`,
      };
    }

    return result.right;
  });
};

// New serializer function for DbPermission with view parameter (similar to permissions-groups)
export const dbPermissionToPermission = (
  dbPermission: Schema.Schema.Type<typeof DbPermissionSelectSchema>,
  view: ResourceViewType,
) => {
  // For permissions, we only have 'detail' view since there's no 'edit' view like in permissions-groups
  // So we always return the detail transformation
  return Schema.decodeUnknownSync(DbPermissionToPermissionDetail)(dbPermission);
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbPermissionsToPermissions = (
  dbPermissions: Schema.Schema.Type<typeof DbPermissionSelectSchema>[],
  view: CollectionViewType,
): PermissionDetail[] | PermissionSelect[] => {
  return view === 'select'
    ? dbPermissionsToPermissionSelect(dbPermissions)
    : dbPermissionsToPermissionList(dbPermissions);
};
