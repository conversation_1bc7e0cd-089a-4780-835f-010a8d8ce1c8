import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { PERSON_AFFILIATION_DEFAULT_VALUE } from '@/constants/common';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useGetInfiniteInfrastructures } from '@/hooks/infrastructure/useGetInfiniteInfrastructures';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import type { PersonFormSchema } from '@/schemas/bottin/person-form-schema';
import { mapListDataToSelectOption } from '@/services/mappers/controlled-list-select-data';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { ToggleGroup, ToggleGroupItem } from '@/ui/toggle-group';
import { useTranslations } from 'next-intl';
import { useCallback, useState } from 'react';
import { type ControllerRenderProps, useFormContext } from 'react-hook-form';

type AffiliationTypeFieldProps = {
  index: number;
};

export const AffiliationTypeField = ({ index }: AffiliationTypeFieldProps) => {
  const t = useTranslations(
    'directory.form.sections.description.affiliationDetails',
  );
  const locale = useAvailableLocale();
  const tCommon = useTranslations('common');
  const { clearErrors, control, setValue } = useFormContext<PersonFormSchema>();
  const [searchTerm, setSearchTerm] = useState('');
  const infrastructures = useGetInfiniteInfrastructures({
    params: defaultRieServiceParams(locale),
    queryParams: searchTerm ? `q=${searchTerm}` : '',
    select: (response) =>
      response.pages.flatMap((page) =>
        page.data.map(mapListDataToSelectOption),
      ),
  });

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['unit', 'establishment']);

  const handleOnValueChange = useCallback(
    (value: string, field: ControllerRenderProps<PersonFormSchema>) => {
      if (value !== '' && value !== null && value !== undefined) {
        field.onChange(value);
        if (value === 'infrastructure') {
          setValue(
            `affiliatedField.affiliationSection.${index}`,
            PERSON_AFFILIATION_DEFAULT_VALUE.affiliationSection,
          );
        } else if (value === 'unit') {
          setValue(`affiliatedField.affiliationSection.${index}`, {
            affiliation: { label: '', value: '' },
            affiliationType: 'unite',
            jobTitle: [{ locale: locale, value: '' }],
            jobType: { label: '', value: '' },
          });
        } else {
          setValue(`affiliatedField.affiliationSection.${index}`, {
            affiliation: { label: '', value: '' },
            affiliationType: 'etablissement',
            jobTitle: [{ locale: locale, value: '' }],
            jobType: { label: '', value: '' },
          });
        }
        clearErrors(`affiliatedField.affiliationSection.${index}.affiliation`);
      }
    },
    [setValue, index, clearErrors, locale],
  );

  return (
    <FormField
      control={control}
      defaultValue="infrastructure"
      name={`affiliatedField.affiliationSection.${index}.affiliationType`}
      render={({ field }) => (
        <FormItem className="w-full">
          <LabelTooltip
            htmlFor={`affiliatedField.affiliationSection.${index}.affiliationType`}
            label={t('fields.affiliationType.label')}
          />
          <FormControl>
            <>
              <ToggleGroup
                className="flex justify-start"
                onValueChange={(value) => handleOnValueChange(value, field)}
                type="single"
                value={field.value}
                variant="outline"
              >
                <ToggleGroupItem value="infrastructure">
                  {t('fields.affiliationType.options.infrastructure')}
                </ToggleGroupItem>
                <ToggleGroupItem value="unit">
                  {t('fields.affiliationType.options.unit')}
                </ToggleGroupItem>
                <ToggleGroupItem value="establishment">
                  {t('fields.affiliationType.options.establishment')}
                </ToggleGroupItem>
              </ToggleGroup>
              {field.value === 'infrastructure' && (
                <ComboboxFieldInfiniteScroll
                  clearErrorsOnChange={false}
                  fetchNextPage={infrastructures.fetchNextPage}
                  fieldLabel={t('fields.infrastructure.label')}
                  fieldName={`affiliatedField.${index}.affiliation`}
                  hasNextPage={Boolean(infrastructures.hasNextPage)}
                  isFetching={Boolean(infrastructures.isFetching)}
                  isFetchingNextPage={Boolean(
                    infrastructures.isFetchingNextPage,
                  )}
                  onSearchChange={setSearchTerm}
                  options={infrastructures.data ?? []}
                  placeholder={tCommon('select')}
                  required
                />
              )}
              {field.value === 'unite' && (
                <ComboboxFieldInfiniteScroll
                  clearErrorsOnChange={false}
                  fetchNextPage={selectsData.unit?.fetchNextPage}
                  fieldLabel={t('fields.unit.label')}
                  fieldName={`affiliatedField.${index}.affiliation`}
                  hasNextPage={Boolean(selectsData.unit?.hasNextPage)}
                  isFetching={Boolean(selectsData.unit?.isFetching)}
                  isFetchingNextPage={Boolean(
                    selectsData.person?.isFetchingNextPage,
                  )}
                  onSearchChange={handleOnSearchTermChange}
                  options={selectsData.unit?.data ?? []}
                  placeholder={tCommon('select')}
                  required
                />
              )}
              {field.value === 'etablissement' && (
                <ComboboxFieldInfiniteScroll
                  clearErrorsOnChange={false}
                  fetchNextPage={selectsData.establishment?.fetchNextPage}
                  fieldLabel={t('fields.establishment.label')}
                  fieldName={`affiliatedField.${index}.affiliation`}
                  hasNextPage={Boolean(selectsData.establishment?.hasNextPage)}
                  isFetching={Boolean(selectsData.establishment?.isFetching)}
                  isFetchingNextPage={Boolean(
                    selectsData.establishment?.isFetchingNextPage,
                  )}
                  onSearchChange={handleOnSearchTermChange}
                  options={selectsData.establishment?.data ?? []}
                  placeholder={tCommon('select')}
                  required
                />
              )}
            </>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
