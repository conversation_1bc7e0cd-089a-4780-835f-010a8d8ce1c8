import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { useGetMaintenanceUnits } from '@/app/[locale]/equipements/form/sections/maintenances/hooks/useGetMaintenanceUnits';
import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxField } from '@/components/form-fields/combobox-field';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { Textarea } from '@/ui/textarea';
import { useTranslations } from 'next-intl';
import { useCallback } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

export const GeneralInfo = () => {
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations(
    'equipments.form.sections.maintenances.generalInfo',
  );
  const { control } = useFormContext<EquipmentFormSchema>();

  const {
    append: appendMinimalMaintenance,
    fields: MinimalMaintenanceFields,
    remove: removeMinimalMaintenance,
  } = useFieldArray<EquipmentFormSchema>({
    control,
    name: 'minimalMaintenanceRequired',
  });

  const handleAddMinimalMaintenanceTranslation = useCallback(
    (locale: string) => {
      appendMinimalMaintenance({
        locale: locale,
        value: '',
      });
    },
    [appendMinimalMaintenance],
  );

  const handleRemoveMinimalMaintenanceTranslation = useCallback(
    (index: number) => {
      removeMinimalMaintenance(index);
    },
    [removeMinimalMaintenance],
  );

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['supplier']);

  const maintenanceUnits = useGetMaintenanceUnits();

  return (
    <FormSubsection title={tEquipments('title')}>
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="minimalMaintenanceRequired"
        fields={MinimalMaintenanceFields}
        label={(locale) =>
          tEquipments('fields.minimalMaintenanceRequired.labelWithLocale', {
            locale: tCommon(locale),
          })
        }
        onAddTranslation={handleAddMinimalMaintenanceTranslation}
        onRemoveTranslation={handleRemoveMinimalMaintenanceTranslation}
      />
      <div className="flex flex-col items-center gap-x-5 md:flex-row">
        <FormField
          control={control}
          name="maximalMaintenanceLength"
          render={({ field }) => (
            <FormItem className="w-full">
              <LabelTooltip label={tEquipments(`fields.${field.name}.label`)} />
              <FormControl>
                <Input
                  {...field}
                  inputMode="numeric"
                  min={0}
                  pattern="[0-9]*"
                  step={1}
                  type="number"
                />
              </FormControl>
              <FieldInfo>
                <FormMessage />
              </FieldInfo>
            </FormItem>
          )}
        />
        <ComboboxField
          className="w-full"
          clearErrorsOnChange={false}
          fieldLabel={tEquipments('fields.unit.label')}
          fieldName="unit"
          options={maintenanceUnits.map((unit) => ({
            label: unit.label,
            value: unit.id,
          }))}
          placeholder={tCommon('select')}
        />
      </div>
      <MultiselectField
        fieldName="repairers"
        label={tEquipments('fields.repairers.label')}
        options={selectsData.supplier?.data ?? []}
        placeholder={tCommon('select')}
      />
    </FormSubsection>
  );
};
