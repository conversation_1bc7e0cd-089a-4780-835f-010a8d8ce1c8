import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { UnitFormSchema } from '@/schemas/bottin/unit-form-schema';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const GeneralInfo = () => {
  const availableLocale = useAvailableLocale();
  const tUnits = useTranslations(
    'directory.form.sections.description.generalInfo',
  );
  const tUnitBase = useTranslations('directory.form.sections');
  const tCommon = useTranslations('common');
  const { control, formState } = useFormContext<UnitFormSchema>();

  const nameFields = useTranslatedField(control, 'names');
  const pseudonymField = useTranslatedField(control, 'pseudonym');
  const aliasFields = useTranslatedField(control, 'alias');

  const nameError = getFieldErrorMessage(formState.errors, 'names');
  const nameErrorMessage = nameError ? tUnitBase(nameError) : undefined;
  const aliasError = getFieldErrorMessage(formState.errors, 'alias');
  const aliasErrorMessage = aliasError ? tUnitBase(aliasError) : undefined;

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['organisation', 'unitType']);

  return (
    <FormSubsection title={tUnits('title')}>
      <FormField
        control={control}
        name="unitType"
        render={({ field }) => (
          <FormItem className="w-fit">
            <LabelTooltip
              htmlFor="unitType"
              label={tUnits('fields.unitType.label')}
              required
            />
            <FormControl>
              <ToggleGroup
                onValueChange={(value) => {
                  if (value !== '' && value !== null && value !== undefined) {
                    field.onChange(value);
                  }
                }}
                type="single"
                value={field.value as string}
                variant="outline"
              >
                {selectsData.unitType?.data?.map((option) => (
                  <ToggleGroupItem key={option.value} value={option.value}>
                    {tUnits(`fields.unitType.options.${option.label}`)}
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FieldWithTranslations
        control={control}
        errorMessage={nameErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="names"
        fields={nameFields.fields}
        label={(locale) =>
          tUnits('fields.name.label', { locale: tCommon(locale) })
        }
        maxLength={150}
        onAddTranslation={nameFields.handleAddTranslation}
        onRemoveTranslation={nameFields.handleRemoveTranslation}
        required={true}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="pseudonym"
        fields={pseudonymField.fields}
        label={(locale) =>
          tUnits('fields.acronym.label', { locale: tCommon(locale) })
        }
        maxLength={30}
        onAddTranslation={pseudonymField.handleAddTranslation}
        onRemoveTranslation={pseudonymField.handleRemoveTranslation}
      />
      <FieldWithTranslations
        control={control}
        errorMessage={aliasErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="alias"
        fields={aliasFields.fields}
        label={(locale) =>
          tUnits('fields.alias.label', { locale: tCommon(locale) })
        }
        maxLength={1000}
        onAddTranslation={aliasFields.handleAddTranslation}
        onRemoveTranslation={aliasFields.handleRemoveTranslation}
        required={true}
        tooltip={(locale) =>
          tUnits('fields.alias.tooltip', {
            locale: tCommon(locale ?? availableLocale),
          })
        }
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="organisation"
        fetchNextPage={selectsData.organisation?.fetchNextPage}
        fieldLabel={tUnits('fields.parentUnit.label')}
        fieldName="parentUnit"
        hasNextPage={Boolean(selectsData.organisation?.hasNextPage)}
        isFetching={Boolean(selectsData.organisation?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.organisation?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.organisation?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
      <MultiselectField
        fieldName="relatedOrganizations"
        label={tUnits('fields.relatedOrganizations.label')}
        options={selectsData.organisation?.data ?? []}
        placeholder={tCommon('select')}
      />
    </FormSubsection>
  );
};
