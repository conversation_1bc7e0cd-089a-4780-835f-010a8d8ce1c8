import {
  DbCampusI18NSelectSchema,
  DbCampusInputSchema,
  DbCampusSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import { requiredFieldWth150MaxLengthSchema } from './base.schema';
import { LocaleSchema } from './query.schema';

// — Full Campus shape
export const CampusSchema = Schema.Struct({
  ...DbCampusSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(DbCampusI18NSelectSchema.omit('id', 'dataId')),
});

// — Translation input schema
export const CampusI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: requiredFieldWth150MaxLengthSchema('Name'),
});

// — Campus List view schema (for directory table)
export const CampusListSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String, // nom par défaut depuis traductions
  jurisdiction: Schema.NullishOr(Schema.String), // institution associée
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  sadId: Schema.NullishOr(Schema.String),
  institutionId: Schema.NullishOr(Schema.String),
});

// — Campus Select view schema
export const CampusSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Campus Edit view schema
export const CampusEditSchema = Schema.Struct({
  id: Schema.String,
  sadId: Schema.NullishOr(Schema.String),
  institutionId: Schema.NullishOr(Schema.String),
  translations: Schema.Array(CampusI18NInputSchema),
});

// — Campus Detail view schema (same as list for now)
export const CampusDetailSchema = CampusListSchema;

// — Input (create/update) shape
export const CampusInputSchema = Schema.Struct({
  ...DbCampusInputSchema.omit('id').fields,
  translations: Schema.Array(CampusI18NInputSchema),
});
