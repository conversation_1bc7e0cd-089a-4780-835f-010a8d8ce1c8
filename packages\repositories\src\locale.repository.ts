import { locales } from '@rie/db-schema/schemas';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq, not } from 'drizzle-orm';
import { Cache, Duration, Array as EArray, Effect } from 'effect';

export class LocaleRepositoryLive extends Effect.Service<LocaleRepositoryLive>()(
  'LocaleRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;
      const getFallbackLocale = (locale: string) => {
        const cacheKey = `fallback-locale-${locale}`;

        const queryEffect = (locale: string) =>
          dbClient
            .execute((db) =>
              db.query.locales.findMany({
                where: not(eq(locales.code, locale)),
                columns: {
                  code: true,
                },
              }),
            )
            .pipe(
              Effect.flatMap(EArray.head),
              Effect.map(({ code }) => code),
            );

        return Effect.gen(function* (_) {
          const cache = yield* Cache.make({
            capacity: 10,
            timeToLive: Duration.infinity,
            lookup: queryEffect,
          });

          return yield* cache.get(cacheKey);
        });
      };

      return { getFallbackLocale } as const;
    }),
  },
) {}
