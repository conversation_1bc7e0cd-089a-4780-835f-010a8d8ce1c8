import { users } from '@/schemas/auth/auth.schema';
import { equipments } from '@/schemas/main/equipments.schema';
import { locales } from '@/schemas/main/locales.schema';
import { media } from '@/schemas/main/media.schema';
import { vendors } from '@/schemas/main/vendors.schema';
import { visibilities } from '@/schemas/main/visibilities.schema';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  boolean,
  date,
  doublePrecision,
  integer,
  pgTable,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const serviceContracts = pgTable('service_contracts', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  isActive: boolean().default(true),
  equipmentId: text()
    .notNull()
    .references(() => equipments.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  vendorId: text()
    .notNull()
    .references(() => vendors.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  startDate: date().notNull(),
  endDate: date(),
  monetaryCost: doublePrecision(),
  inKindCost: doublePrecision(),
  comment: text(),
  visibilityId: text()
    .notNull()
    .references(() => visibilities.id, {
      onUpdate: 'cascade',
    }),
  number: text(),
  isRenewable: boolean().default(false),
  hasParts: boolean().default(false),
  partsCount: integer(),
  hasWorkforce: boolean().default(false),
  hourBank: integer(),
  hasMaintenance: boolean().default(false),
  maintenanceCount: integer(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const serviceContractsRelations = relations(
  serviceContracts,
  ({ one, many }) => ({
    equipment: one(equipments, {
      fields: [serviceContracts.equipmentId],
      references: [equipments.id],
    }),
    vendor: one(vendors, {
      fields: [serviceContracts.vendorId],
      references: [vendors.id],
    }),
    visibility: one(visibilities, {
      fields: [serviceContracts.visibilityId],
      references: [visibilities.id],
    }),
    translations: many(serviceContractsI18N),
    documents: many(serviceContractsDocument),
  }),
);

export const serviceContractsI18N = pgTable(
  'service_contracts_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => serviceContracts.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const serviceContractsI18NRelations = relations(
  serviceContractsI18N,
  ({ one }) => ({
    serviceContracts: one(serviceContracts, {
      fields: [serviceContractsI18N.dataId],
      references: [serviceContracts.id],
    }),
    locale: one(locales, {
      fields: [serviceContractsI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const serviceContractsDocument = pgTable(
  'service_contracts_document',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    serviceContractId: text()
      .notNull()
      .references(() => serviceContracts.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    documentId: text()
      .notNull()
      .references(() => media.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      uniqueContractDocument: unique().on(
        table.serviceContractId,
        table.documentId,
      ),
    },
  ],
);

export const serviceContractsDocumentRelations = relations(
  serviceContractsDocument,
  ({ one }) => ({
    serviceContracts: one(serviceContracts, {
      fields: [serviceContractsDocument.serviceContractId],
      references: [serviceContracts.id],
    }),
    document: one(media, {
      fields: [serviceContractsDocument.documentId],
      references: [media.id],
    }),
  }),
);
