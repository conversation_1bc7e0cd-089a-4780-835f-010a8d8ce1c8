CREATE OR <PERSON><PERSON>LACE FUNCTION assign_default_role()
    <PERSON><PERSON><PERSON>NS TRIGGER AS $$
DECLARE
    udem_role_id TEXT;
    partner_role_id TEXT;
    assigned_role_id TEXT;
BEGIN
    -- Get the IDs of the roles
    SELECT id INTO udem_role_id FROM roles WHERE name = 'Ude<PERSON>';
    SELECT id INTO partner_role_id FROM roles WHERE name = 'UdeMPartner';

    -- Determine which role to assign based on email domain
    IF NEW.email ILIKE '%@umontreal.ca' THEN
        assigned_role_id := udem_role_id;
    ELSE
        assigned_role_id := partner_role_id;
    END IF;

    -- If the appropriate role exists, assign it to the new user
    IF assigned_role_id IS NOT NULL THEN
        INSERT INTO user_roles (user_id, role_id, resource_type)
        VALUES (NEW.id, assigned_role_id, NULL);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS assign_default_role_trigger ON users;
CREATE TRIGGER assign_default_role_trigger
    AFTER INSERT ON users
    FOR EACH ROW
EXECUTE FUNCTION assign_default_role();
