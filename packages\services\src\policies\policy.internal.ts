// Import the actual types from your domain
import type { PermissionAction } from '@rie/domain/types';

export type PermissionConfig = Record<
  string,
  ReadonlyArray<PermissionAction | string>
>;

export type InferPermissions<T extends PermissionConfig> = {
  [K in keyof T]: T[K] extends ReadonlyArray<infer U>
    ? U extends PermissionAction | string
      ? `${K & string}:${U}`
      : never
    : never;
}[keyof T];
export const makePermissions = <T extends PermissionConfig>(
  config: T,
): Array<InferPermissions<T>> => {
  return Object.entries(config).flatMap(
    ([domain, actions]) =>
      actions?.map((action) => `${domain}:${action}` as InferPermissions<T>) ||
      [],
  );
};
