'use client';
import { UnitForm } from '@/app/[locale]/bottin/unites/form/unit-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useGetGenericById } from '@/hooks/bottin/generic-list.hook';
import { useUpdateUnit } from '@/hooks/bottin/units.hook';
import type { UnitFormSchema } from '@/schemas/bottin/unit-form-schema';
import type { UnitFormSectionKey } from '@/types/bottin/unit';
import type { SupportedLocale } from '@/types/locale';
import { UnitEditToFormSchema } from '@rie/domain/serializers';
import * as Schema from 'effect/Schema';

type EditUnitPageParams = {
  formSections: Record<UnitFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionUnitPage({
  formSections,
  id,
}: EditUnitPageParams) {
  const {
    data: unit,
    error,
    isPending,
  } = useGetGenericById<'unit', 'edit'>({
    controlledListKey: 'unit',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Erreur : {error.message}</div>;
  }

  const updateUnit = useUpdateUnit();
  const onSubmit = async (data: UnitFormSchema) => {
    await updateUnit.mutateAsync({ id, payload: data });
  };

  // Transform unit edit data to form schema using serializer
  const formData = Schema.decodeSync(UnitEditToFormSchema)(
    unit,
  ) as UnitFormSchema;

  return (
    <UnitForm
      defaultValues={formData}
      formSections={formSections}
      onSubmit={onSubmit}
    />
  );
}
