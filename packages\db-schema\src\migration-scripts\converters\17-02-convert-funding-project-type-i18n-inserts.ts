import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import { type I18NColumnMapper, i18nColumnMapper } from '../constants';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class FundingProjectTypeI18nMigrationConverter extends BaseConverter {
  private fundingProjectTypeI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    fundingProjectTypeIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the funding_project_type
    const newFundingProjectTypeId =
      fundingProjectTypeIdMappings[mysqlRecord.data_id.toString()];
    if (!newFundingProjectTypeId) {
      throw new Error(
        `No mapping found for funding_project_type_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.fundingProjectTypeI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newFundingProjectTypeId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for type_projet_financement_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'type_projet_financement_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No type_projet_financement_trad INSERT statements found.');
        return;
      }

      // Load funding_project_type ID mappings
      const fundingProjectTypeIdMappings = await this.loadEntityIdMappings(
        'type_projet_financement',
      );

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, fundingProjectTypeIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      const columns = this.extractColumnNames(insertStatements[0] ?? '').map(
        (column) => i18nColumnMapper[column as I18NColumnMapper] ?? column,
      );

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'funding_project_types_i18n',
        'Funding Project Type I18n Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.fundingProjectTypeI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'type_projet_financement_trad',
          postgres: 'funding_project_types_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} funding_project_types_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
