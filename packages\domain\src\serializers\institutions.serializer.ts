import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  InstitutionEditSchema,
  InstitutionListSchema,
  InstitutionSchema,
  InstitutionSelectSchema,
} from '../schemas';
import type {
  CollectionViewType,
  Institution,
  InstitutionList,
  InstitutionSelect,
  ResourceViewType,
} from '../types';

// Schema transformer for converting database institution to list view
export const DbInstitutionToInstitutionList = Schema.transformOrFail(
  InstitutionSchema,
  InstitutionListSchema,
  {
    strict: false,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          const defaultTranslation =
            raw.translations?.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations?.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations?.[0];

          const typeTranslation =
            raw.type?.translations?.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.type?.translations?.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.type?.translations?.[0];

          return {
            id: raw.id,
            text: defaultTranslation?.name || raw.id,
            acronym: defaultTranslation?.acronyms || null,
            establishmentType: typeTranslation?.name || null,
            lastUpdatedAt: raw.updatedAt,
            guidId: raw.guidId || null,
            typeId: raw.typeId || null,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse institution for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database institution to select view
export const DbInstitutionToInstitutionSelect = Schema.transformOrFail(
  InstitutionSchema,
  InstitutionSelectSchema,
  {
    strict: false,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation for label
          const defaultTranslation =
            raw.translations?.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations?.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations?.[0];

          return {
            value: raw.id,
            label: defaultTranslation?.name || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse institution for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database institution to edit format
export const DbInstitutionToInstitutionEdit = Schema.transformOrFail(
  InstitutionSchema,
  InstitutionEditSchema,
  {
    strict: false,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            guidId: raw.guidId || null,
            typeId: raw.typeId || null,
            translations:
              raw.translations?.map((translation) => ({
                locale: translation.locale as 'fr' | 'en',
                name: translation.name || undefined,
                description: translation.description || undefined,
                otherNames: translation.otherNames || undefined,
                acronyms: translation.acronyms || undefined,
              })) || [],
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse institution for edit format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of Institution to InstitutionSelect[]
export const dbInstitutionsToInstitutionSelect = (
  dbInstitutions: Institution[],
): InstitutionSelect[] => {
  return dbInstitutions.map((dbInstitution) => {
    const defaultTranslation =
      dbInstitution.translations.find(
        (translation) => translation.locale === 'fr',
      ) ||
      dbInstitution.translations.find(
        (translation) => translation.locale === 'en',
      ) ||
      dbInstitution.translations[0];

    return {
      value: dbInstitution.id,
      label: defaultTranslation?.name || dbInstitution.id,
    };
  });
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbInstitutionsToInstitutions = (
  dbInstitutions: Institution[],
  view: CollectionViewType,
): InstitutionList[] | InstitutionSelect[] => {
  return view === 'select'
    ? dbInstitutions.map((institution) =>
      Schema.decodeUnknownSync(DbInstitutionToInstitutionSelect)(institution),
    )
    : dbInstitutions.map((institution) =>
      Schema.decodeUnknownSync(DbInstitutionToInstitutionList)(institution),
    );
};

// New serializer function for Institution with view parameter
export const dbInstitutionToInstitution = (
  dbInstitution: Institution,
  view: ResourceViewType,
) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbInstitutionToInstitutionEdit)(dbInstitution)
    : Schema.decodeUnknownSync(DbInstitutionToInstitutionList)(dbInstitution);
};

// Schema transformer for converting InstitutionEdit to form schema (Zod-compatible)
export const InstitutionEditToFormSchema = Schema.transformOrFail(
  InstitutionEditSchema,
  Schema.Struct({
    id: Schema.optional(Schema.String),
    name: Schema.Array(
      Schema.Struct({
        locale: Schema.String,
        value: Schema.String,
      }),
    ),
    acronym: Schema.Array(
      Schema.Struct({
        locale: Schema.String,
        value: Schema.String,
      }),
    ),
    pseudonym: Schema.Array(
      Schema.Struct({
        locale: Schema.String,
        value: Schema.String,
      }),
    ),
    establishmentType: Schema.Struct({
      label: Schema.String,
      value: Schema.String,
    }),
    affiliatedPersons: Schema.optional(Schema.Array(Schema.Unknown)),
  }),
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            name: raw.translations.map((translation) => ({
              locale: translation.locale,
              value: translation.name || '',
            })),
            acronym: raw.translations
              .filter((t) => t.acronyms)
              .map((translation) => ({
                locale: translation.locale,
                value: translation.acronyms || '',
              })),
            pseudonym: raw.translations
              .filter((t) => t.otherNames)
              .map((translation) => ({
                locale: translation.locale,
                value: translation.otherNames || '',
              })),
            establishmentType: raw.typeId
              ? {
                value: raw.typeId,
                label: raw.typeId, // TODO: Get actual type name
              }
              : { value: '', label: '' },
            affiliatedPersons: [], // TODO: Load affiliated persons if needed
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to convert institution edit to form schema',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);
