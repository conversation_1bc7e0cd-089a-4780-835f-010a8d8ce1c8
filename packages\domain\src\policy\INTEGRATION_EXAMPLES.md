# Policy System Integration Examples

This document provides practical examples of how to use the enhanced policy system integrated with your UserPermissionsService.

## Basic Usage in Route Handlers

### Simple Permission Check

```typescript
import { withPolicy, permission } from '@rie/services/global-policies.service';

const getEquipmentList = Effect.gen(function* () {
  // Your business logic here
  const equipment = yield* equipmentService.getAll();
  return equipment;
}).pipe(
  withPolicy(permission('equipment:read'))
);
```

### Resource-Specific Permission Check

```typescript
import { withPolicy, permissionForResource } from '@rie/services/global-policies.service';

const updateEquipment = (equipmentId: string) => Effect.gen(function* () {
  // Your business logic here
  const updatedEquipment = yield* equipmentService.update(equipmentId, data);
  return updatedEquipment;
}).pipe(
  withPolicy(permissionForResource('equipment', 'update', equipmentId))
);
```

### Complex Permission Logic

```typescript
import { withPolicy, all, any, permission, permissionForResource, resourceAccess } from '@rie/domain/policy/policy.service';

const deleteEquipment = (equipmentId: string, infrastructureId: string) => Effect.gen(function* () {
  // Your business logic here
  yield* equipmentService.delete(equipmentId);
  return { success: true };
}).pipe(
  withPolicy(
    any([
      permissionForResource('equipment', 'delete', equipmentId),
      all([
        permissionForResource('infrastructure', 'update', infrastructureId),
        permission('equipment:update')
      ])
    ])
  )
);
```

## Using Domain-Specific Policy Services

### Equipment Management Example

```typescript
import { EquipmentPoliciesLive } from '@rie/domain';

const equipmentRoutes = Effect.gen(function* () {
  const equipmentPolicies = yield* EquipmentPoliciesLive;

  const getEquipment = (equipmentId: string) => Effect.gen(function* () {
    const equipment = yield* equipmentService.findById(equipmentId);
    return equipment;
  }).pipe(
    withPolicy(equipmentPolicies.canReadEquipment(equipmentId))
  );

  const updateEquipment = (equipmentId: string, data: any) => Effect.gen(function* () {
    const updated = yield* equipmentService.update(equipmentId, data);
    return updated;
  }).pipe(
    withPolicy(equipmentPolicies.canUpdateEquipment(equipmentId))
  );

  const deleteEquipment = (equipmentId: string) => Effect.gen(function* () {
    yield* equipmentService.delete(equipmentId);
    return { success: true };
  }).pipe(
    withPolicy(equipmentPolicies.canDeleteEquipment(equipmentId))
  );

  return {
    getEquipment,
    updateEquipment,
    deleteEquipment,
  };
});
```

### Infrastructure Management Example

```typescript
import { InfrastructurePoliciesLive } from '@rie/domain/policy/infrastructure-policies.service';
import { EquipmentPoliciesLive } from '@rie/domain/policy/equipment-policies.service';

const infrastructureRoutes = Effect.gen(function* () {
  const infrastructurePolicies = yield* InfrastructurePoliciesLive;
  const equipmentPolicies = yield* EquipmentPoliciesLive;

  const addEquipmentToInfrastructure = (infrastructureId: string, equipmentData: any) => Effect.gen(function* () {
    const equipment = yield* equipmentService.create({
      ...equipmentData,
      infrastructureId,
    });
    return equipment;
  }).pipe(
    withPolicy(
      all([
        infrastructurePolicies.canUpdateInfrastructure(infrastructureId),
        equipmentPolicies.canCreateEquipment,
      ])
    )
  );

  const exportInfrastructureData = (infrastructureId: string) => Effect.gen(function* () {
    const data = yield* infrastructureService.exportData(infrastructureId);
    return data;
  }).pipe(
    withPolicy(infrastructurePolicies.canExportInfrastructure)
  );

  return {
    addEquipmentToInfrastructure,
    exportInfrastructureData,
  };
});
```

## Hierarchical Permission Examples

### Unit-Level Management

```typescript
const manageUnitResources = (unitId: string) => Effect.gen(function* () {
  const infrastructurePolicies = yield* InfrastructurePoliciesLive;
  const equipmentPolicies = yield* EquipmentPoliciesLive;

  const getAllUnitInfrastructures = Effect.gen(function* () {
    const infrastructures = yield* infrastructureService.getByUnitId(unitId);
    return infrastructures;
  }).pipe(
    withPolicy(
      any([
        permission('unit:read'),
        permission('infrastructure:read'),
      ])
    )
  );

  const bulkUpdateEquipment = (updates: any[]) => Effect.gen(function* () {
    const results = yield* Effect.all(
      updates.map(update => equipmentService.update(update.id, update.data))
    );
    return results;
  }).pipe(
    withPolicy(
      all([
        resourceAccess('unit', unitId),
        permission('equipment:update'),
      ])
    )
  );

  return {
    getAllUnitInfrastructures,
    bulkUpdateEquipment,
  };
});
```

## Service Layer Integration

### Business Logic with Permission Context

```typescript
class EquipmentServiceLive extends Effect.Service<EquipmentServiceLive>()(
  'EquipmentServiceLive',
  {
    dependencies: [EquipmentPoliciesLive.Default],
    effect: Effect.gen(function* () {
      const equipmentPolicies = yield* EquipmentPoliciesLive;

      const transferEquipment = (
        equipmentId: string,
        fromInfrastructureId: string,
        toInfrastructureId: string
      ) => Effect.gen(function* () {
        // Business logic with embedded permission checks
        const canRemoveFromSource = yield* equipmentPolicies.canManageInfrastructureEquipment(
          fromInfrastructureId
        );
        
        const canAddToTarget = yield* equipmentPolicies.canManageInfrastructureEquipment(
          toInfrastructureId
        );

        // Both checks must pass
        return yield* Effect.gen(function* () {
          yield* equipmentRepository.update(equipmentId, {
            infrastructureId: toInfrastructureId,
          });
          return { success: true };
        }).pipe(
          withPolicy(all([canRemoveFromSource, canAddToTarget]))
        );
      });

      return {
        transferEquipment,
      } as const;
    }),
  },
) {}
```

## Testing Policy Integration

### Unit Testing Individual Policies

```typescript
import { describe, it, expect } from 'vitest';
import { Effect } from 'effect';
import { EquipmentPoliciesLive } from '@rie/domain/policy/equipment-policies.service';
import { CurrentUser } from '@rie/domain/policy/policy.service';

describe('Equipment Policies', () => {
  const mockUser = (userId: string): CurrentUser['Type'] => ({
    sessionId: 'test-session',
    userId,
  });

  it('should allow equipment read access with proper permissions', async () => {
    const program = Effect.gen(function* () {
      const equipmentPolicies = yield* EquipmentPoliciesLive;
      
      return yield* Effect.succeed('allowed').pipe(
        withPolicy(equipmentPolicies.canReadEquipment('equipment-123'))
      );
    }).pipe(
      Effect.provideService(CurrentUser, mockUser('user-with-permissions'))
    );

    const result = await Effect.runPromise(program);
    expect(result).toBe('allowed');
  });
});
```

### Integration Testing with Mock Services

```typescript
import { describe, it, expect } from 'vitest';
import { Effect, Layer } from 'effect';

describe('Equipment Routes Integration', () => {
  const mockUserPermissionsService = {
    userHasPermission: ({ userId, domain, action, resourceId }) => {
      // Mock implementation based on test scenarios
      return Effect.succeed(true);
    },
    userHasAccessToResource: (userId, resourceType, resourceId) => {
      return Effect.succeed(true);
    },
  };

  const TestLayer = Layer.succeed(UserPermissionsServiceLive, mockUserPermissionsService);

  it('should handle equipment creation with proper permissions', async () => {
    const program = Effect.gen(function* () {
      const equipmentPolicies = yield* EquipmentPoliciesLive;
      
      return yield* Effect.succeed({ id: 'new-equipment' }).pipe(
        withPolicy(equipmentPolicies.canCreateEquipment)
      );
    }).pipe(
      Effect.provide(TestLayer),
      Effect.provideService(CurrentUser, mockUser('test-user'))
    );

    const result = await Effect.runPromise(program);
    expect(result.id).toBe('new-equipment');
  });
});
```

## Best Practices

1. **Use Domain-Specific Policies**: Create policy services for each domain (Equipment, Infrastructure, etc.)
2. **Compose Policies**: Use `all()` and `any()` to create complex authorization rules
3. **Resource-Specific Checks**: Always use `permissionForResource()` when checking permissions for specific resources
4. **Hierarchical Access**: Leverage your existing access tree system through the policy layer
5. **Performance**: The policy system automatically benefits from your existing permission caching
6. **Error Handling**: Policies fail fast with `Forbidden` errors, providing clear feedback
7. **Testing**: Write unit tests for individual policies and integration tests for complex scenarios

This integration gives you the best of both worlds: the composable, type-safe policy system from the blog post, plus the power of your sophisticated role-based, hierarchical permission system.