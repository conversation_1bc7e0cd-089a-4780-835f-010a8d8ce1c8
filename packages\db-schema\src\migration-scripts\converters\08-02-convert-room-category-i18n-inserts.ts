import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class RoomCategoryI18nMigrationConverter extends BaseConverter {
  private roomCategoryI18nMappings: Mapping[] = [];

  private parseRoomCategoryI18NInsertStatement(
    sqlStatement: string,
  ): MySQLI18NDescription[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        nom: values.nom,
        description: values.description,
      },
    ];
  }

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    roomCategoryIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the room_category
    const newRoomCategoryId =
      roomCategoryIdMappings[mysqlRecord.data_id.toString()];
    if (!newRoomCategoryId) {
      throw new Error(
        `No mapping found for room_category_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.roomCategoryI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newRoomCategoryId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for categorie_local_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'categorie_local_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No categorie_local_trad INSERT statements found.');
        return;
      }

      // Load room_category ID mappings
      const roomCategoryIdMappings =
        await this.loadEntityIdMappings('categorie_local');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords =
          this.parseRoomCategoryI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, roomCategoryIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      // Generate output
      const postgresInserts = this.generatePostgresI18NInsert(
        allPostgresRecords,
        'room_categories_i18n',
        'Room Category I18n Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.roomCategoryI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'categorie_local_trad', postgres: 'room_categories_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} room_categories_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
