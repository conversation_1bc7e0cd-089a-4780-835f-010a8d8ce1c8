import type { DbRole } from '@rie/db-schema/entity-types';
import type {
  RoleInputSchema,
  RoleListSchema,
  RolePermissionGroupsSchema,
  RolePermissionsSchema,
  RoleSelectSchema,
} from '../schemas/roles.schema';

import type * as Schema from 'effect/Schema';

export type Role = DbRole;
export type RoleInput = Schema.Schema.Type<typeof RoleInputSchema>;

export type RolePermissions = Schema.Schema.Type<typeof RolePermissionsSchema>;
export type RolePermissionGroups = Schema.Schema.Type<
  typeof RolePermissionGroupsSchema
>;
export type RoleList = Schema.Schema.Type<typeof RoleListSchema>;
export type RoleSelect = Schema.Schema.Type<typeof RoleSelectSchema>;
