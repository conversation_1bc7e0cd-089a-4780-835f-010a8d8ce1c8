import type {
  EquipmentInputSchema,
  EquipmentSchema,
} from '../schemas/equipments.schema';

import type * as Schema from 'effect/Schema';
import type { EquipmentListSchema } from '../schemas/equipments.schema';

export type Equipment = Schema.Schema.Type<typeof EquipmentSchema>;
export type EquipmentInput = Schema.Schema.Type<typeof EquipmentInputSchema>;
export type EquipmentList = Schema.Schema.Type<typeof EquipmentListSchema>;
