import type {
  PermissionActionSchema,
  PermissionDetailSchema,
  PermissionInputSchema,
  PermissionListSchema,
  PermissionSelectSchema,
} from '../schemas';

import type * as Schema from 'effect/Schema';
import type { ResourceType } from './user-permissions.type';

export type PermissionList = Schema.Schema.Type<typeof PermissionListSchema>;

export type PermissionDetail = Schema.Schema.Type<
  typeof PermissionDetailSchema
>;

export type PermissionSelect = Schema.Schema.Type<
  typeof PermissionSelectSchema
>;

export type PermissionInput = Schema.Schema.Type<typeof PermissionInputSchema>;

export type PermissionAction = Schema.Schema.Type<
  typeof PermissionActionSchema
>;

export type PermissionString = `${ResourceType}:${PermissionAction}`;
