import { getControlledList } from '@/services/controled-lists';
import { mapControlledListsToSelectOptions } from '@/services/mappers/controlled-list-select-data';
import type { ControlledListKey, SelectOption } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import { queryOptions, useQueries } from '@tanstack/react-query';

type UseGetControlledListInput = {
  controlledLists: ControlledListKey[];
  locale: SupportedLocale;
};

export const controlledListsOptions = (
  controlledListKey: ControlledListKey,
  locale: SupportedLocale,
) =>
  queryOptions({
    queryFn: () => getControlledList({ controlledListKey, locale }),
    queryKey: ['controlledLists', controlledListKey, locale],
    select: (controlledList) =>
      mapControlledListsToSelectOptions(controlledList.data),
  });

export const useGetControlledList = ({
  controlledLists,
  locale,
}: UseGetControlledListInput) => {
  return useQueries({
    combine: (results) => {
      return results.reduce<Partial<Record<ControlledListKey, SelectOption[]>>>(
        (acc, result, index) => {
          const listKey = controlledLists[index];
          acc[listKey] = result.data ?? [];
          return acc;
        },
        {},
      );
    },
    queries: controlledLists.map((controlledListKey) => {
      return controlledListsOptions(controlledListKey, locale);
    }),
  });
};
