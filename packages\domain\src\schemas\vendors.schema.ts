import {
  Db<PERSON>endorI18NSelectSchema,
  DbVendorInputSchema,
  DbVendorSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth150MaxLengthSchema,
  optionalFieldWth1500MaxLengthSchema,
  requiredFieldWth150MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';

// — Full Vendor shape (with translations)
export const VendorSchema = Schema.Struct({
  ...DbVendorSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(DbVendorI18NSelectSchema),
});

// — Translation Input for API
export const VendorI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: requiredFieldWth150MaxLengthSchema('Name'),
  website: optionalFieldWth150MaxLengthSchema('Website'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  otherNames: optionalFieldWth150MaxLengthSchema('Other Names'),
});

// — Vendor List view schema (for directory table)
export const VendorListSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String, // nom par défaut depuis traductions
  dateEnd: Schema.NullishOr(Schema.String), // date de fin
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  startDate: Schema.NullishOr(Schema.String),
  endDate: Schema.NullishOr(Schema.String),
});

// — Vendor Select view schema
export const VendorSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Vendor Edit view schema
export const VendorEditSchema = Schema.Struct({
  id: Schema.String,
  startDate: Schema.NullishOr(Schema.String),
  endDate: Schema.NullishOr(Schema.String),
  translations: Schema.Array(VendorI18NInputSchema),
});

// — Vendor Detail view schema (same as list for now)
export const VendorDetailSchema = VendorListSchema;

// — Input (create/update) shape
export const VendorInputSchema = Schema.Struct({
  ...DbVendorInputSchema.omit('id').fields,
  translations: Schema.Array(VendorI18NInputSchema),
});

// — Database schema (for serializers)
export const DbVendorSchema = VendorSchema;

// — Form Schema (client payload shape)
// Matches apps/web-app manufacturer form structure; server converts it using serializers
export const VendorFormSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  name: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: requiredFieldWth150MaxLengthSchema('Name'),
    }),
  ),
  alias: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: optionalFieldWth1500MaxLengthSchema('Other Names'),
    }),
  ),
  dateEnd: Schema.NullishOr(Schema.String),
  // Extra fields currently present in the client form, ignored by server conversion
  contacts: Schema.optional(Schema.Array(Schema.Unknown)),
  phones: Schema.optional(
    Schema.Array(
      Schema.Struct({
        description: Schema.Array(
          Schema.Struct({ locale: Schema.String, value: Schema.String }),
        ),
        phone: Schema.NullishOr(Schema.String),
      }),
    ),
  ),
});
