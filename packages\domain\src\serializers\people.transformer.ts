import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import { PeopleListViewSchema, PeopleRawSchema } from '../schemas';

/**
 * Decoder that transforms PeopleRawSchema to PeopleListViewSchema
 * Main transformation: "personEmails" -> "emails"
 */
export const PeopleRawToPeopleListViewDecoder = Schema.transformOrFail(
  PeopleRawSchema,
  PeopleListViewSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            familyName: raw.familyName,
            givenName: raw.givenName,
            emails: raw.personEmails,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error ? error.message : 'Failed to parse object',
          );
        },
      }),
    encode: (listView, _options, ast) => {
      return ParseResult.try({
        try: () => {
          return {
            id: listView.id,
            familyName: listView.familyName,
            givenName: listView.givenName,
            personEmails: listView.emails,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            listView,
            error instanceof Error ? error.message : 'Failed to parse object',
          );
        },
      });
    },
  },
);
