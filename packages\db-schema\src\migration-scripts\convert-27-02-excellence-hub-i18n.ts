import * as path from 'node:path';
import { SQL_FILE_NAME } from '@/migration-scripts/constants';
import { ExcellenceHubI18nMigrationConverter } from './converters/27-02-convert-excellence-hub-i18n-inserts';

async function convertExcellenceHubI18nData() {
  const scriptDir = __dirname;
  // Input file is in the data directory
  const inputFile = path.join(
    scriptDir,
    'data',
    'mysql_riedb-content_24-04-2025.sql',
  );
  // Output will go to migration-scripts/output
  const outputDir = path.join(scriptDir, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new ExcellenceHubI18nMigrationConverter();

    // Convert the file
    await converter.convertFile(inputFile, outputFile);
  } catch (error) {
    console.error('Error converting excellence hub i18n data:', error);
    process.exit(1);
  }
}

// Run the conversion
convertExcellenceHubI18nData();
