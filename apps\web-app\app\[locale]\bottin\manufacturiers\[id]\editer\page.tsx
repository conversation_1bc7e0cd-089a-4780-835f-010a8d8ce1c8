import EditionManufacturerPage from '@/app/[locale]/bottin/manufacturiers/[id]/editer/edit-manufacturer-page';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { manufacturerFormSections } from '@/constants/bottin/manufacturer';
import { getQueryClientOptions } from '@/constants/query-client';
import { getGenericByIdOptions } from '@/hooks/bottin/generic-list.options';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';

type EditManufacturerPageParams = PageDetailsParams;
export default async function EditManufacturerPage(
  props: EditManufacturerPageParams,
) {
  const params = await props.params;

  const { id, locale } = params;

  const formSections = await getFormSections({
    resourceName: 'manufacturers',
    sections: manufacturerFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const t0 = performance.now();

  const controlledLists: ControlledListKey[] = ['local', 'building'];

  const manufacturer = await Promise.all([
    await queryClient.fetchQuery(
      getGenericByIdOptions({
        controlledListKey: 'supplier',
        id,
        view: 'edit',
      }),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "local", "building" and "manufacturer" took ${t1 - t0} milliseconds.`,
  );

  if (!manufacturer) {
    return notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionManufacturerPage
        formSections={formSections}
        id={id}
        locale={locale}
      />
    </HydrationBoundary>
  );
}
