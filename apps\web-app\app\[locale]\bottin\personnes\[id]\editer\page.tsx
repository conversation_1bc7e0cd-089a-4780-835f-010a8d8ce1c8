import EditionPersonPage from '@/app/[locale]/bottin/personnes/[id]/editer/edit-person-page';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { personFormSections } from '@/constants/bottin/person';
import { getQueryClientOptions } from '@/constants/query-client';
import { getGenericByIdOptions } from '@/hooks/bottin/generic-list.options';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';

type EditPersonPageParams = PageDetailsParams;
export default async function EditPersonPage(props: EditPersonPageParams) {
  const params = await props.params;

  const { id, locale } = params;

  const formSections = await getFormSections({
    resourceName: 'people',
    sections: personFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = [
    'local',
    'building',
    'person',
    'jobType',
  ];

  const t0 = performance.now();

  const person = await Promise.all([
    queryClient.fetchQuery(
      getGenericByIdOptions({ controlledListKey: 'person', id, view: 'edit' }),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  ]);

  const t1 = performance.now();

  if (!person) {
    return notFound();
  }

  console.log(
    `Call to fetch controlledLists "local", "building", "jobType" and "person" took ${t1 - t0} milliseconds.`,
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionPersonPage formSections={formSections} id={id} locale={locale} />
    </HydrationBoundary>
  );
}
