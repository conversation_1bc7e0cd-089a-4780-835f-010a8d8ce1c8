import { DBSchema } from '@rie/db-schema';
import type { CampusInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class CampusesRepositoryLive extends Effect.Service<CampusesRepositoryLive>()(
  'CampusesRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const db = yield* PgDatabaseLive;

      const findAllCampuses = db.makeQuery((exec) =>
        exec((client) =>
          client.query.campuses.findMany({
            columns: {
              id: true,
              sadId: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: { id: true, locale: true, name: true },
              },
              institution: {
                columns: { id: true },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                    },
                  },
                },
              },
            },
          }),
        ),
      );

      const findCampusById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.campuses.findFirst({
            where: eq(DBSchema.campuses.id, id),
            columns: {
              id: true,
              sadId: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: { id: true, locale: true, name: true },
              },
              institution: {
                columns: { id: true },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                    },
                  },
                },
              },
            },
          }),
        ),
      );

      const createCampus = (params: { campus: CampusInput }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            const [createdCampus] = yield* tx((client) =>
              client
                .insert(DBSchema.campuses)
                .values({
                  sadId: params.campus.sadId,
                  institutionId: params.campus.institutionId,
                })
                .returning({
                  id: DBSchema.campuses.id,
                  sadId: DBSchema.campuses.sadId,
                  institutionId: DBSchema.campuses.institutionId,
                  createdAt: DBSchema.campuses.createdAt,
                  updatedAt: DBSchema.campuses.updatedAt,
                  modifiedBy: DBSchema.campuses.modifiedBy,
                }),
            );

            if (!createdCampus) {
              return yield* Effect.fail(new Error('Failed to create campus'));
            }

            const createdTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.campusesI18N)
                .values(
                  params.campus.translations.map((t) => ({
                    dataId: createdCampus.id,
                    locale: t.locale,
                    name: t.name,
                  })),
                )
                .returning({
                  id: DBSchema.campusesI18N.id,
                  locale: DBSchema.campusesI18N.locale,
                  name: DBSchema.campusesI18N.name,
                }),
            );

            // Return the campus with its translations directly
            return {
              ...createdCampus,
              translations: createdTranslations,
            };
          });
        });
      };

      const updateCampus = (params: {
        campusId: string;
        campus: CampusInput;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the campus
            const [updatedCampus] = yield* tx((client) =>
              client
                .update(DBSchema.campuses)
                .set({
                  ...params.campus,
                })
                .where(eq(DBSchema.campuses.id, params.campusId))
                .returning({
                  id: DBSchema.campuses.id,
                  sadId: DBSchema.campuses.sadId,
                  institutionId: DBSchema.campuses.institutionId,
                  createdAt: DBSchema.campuses.createdAt,
                  updatedAt: DBSchema.campuses.updatedAt,
                  modifiedBy: DBSchema.campuses.modifiedBy,
                }),
            );

            let updatedTranslations: Array<{
              id: string;
              locale: string;
              name: string | null;
            }> = [];

            // Update translations if provided
            if (params.campus.translations) {
              const translations = params.campus.translations;
              // Delete existing translations
              yield* tx((client) =>
                client
                  .delete(DBSchema.campusesI18N)
                  .where(eq(DBSchema.campusesI18N.dataId, params.campusId)),
              );

              // Insert new translations
              updatedTranslations = yield* tx((client) =>
                client
                  .insert(DBSchema.campusesI18N)
                  .values(
                    translations.map((t) => ({
                      dataId: params.campusId,
                      locale: t.locale,
                      name: t.name,
                    })),
                  )
                  .returning({
                    id: DBSchema.campusesI18N.id,
                    locale: DBSchema.campusesI18N.locale,
                    name: DBSchema.campusesI18N.name,
                  }),
              );
            }

            // Return the campus with its translations directly
            return {
              ...updatedCampus,
              translations: updatedTranslations,
            };
          });
        });
      };

      const deleteCampus = (id: string) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // First delete all translations for this campus
            yield* tx((client) =>
              client
                .delete(DBSchema.campusesI18N)
                .where(eq(DBSchema.campusesI18N.dataId, id)),
            );

            // Then delete the campus itself
            return yield* tx((client) =>
              client
                .delete(DBSchema.campuses)
                .where(eq(DBSchema.campuses.id, id))
                .returning({ id: DBSchema.campuses.id }),
            );
          });
        });
      };

      return {
        findAllCampuses,
        findCampusById,
        createCampus,
        updateCampus,
        deleteCampus,
      } as const;
    }),
  },
) {}
