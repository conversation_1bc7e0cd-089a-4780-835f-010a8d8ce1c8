import type {
  DBPermissionsGroupInputSchema,
  DbPermissionsGroupSchema,
  PermissionGroupSelectSchema,
  PermissionsGroupDetailSchema,
  PermissionsGroupEditSchema,
  PermissionsGroupInputSchema,
  PermissionsGroupListSchema,
  PermissionsGroupPermissionSchema,
} from '../schemas';

import type * as Schema from 'effect/Schema';

export type DbPermissionsGroup = Schema.Schema.Type<
  typeof DbPermissionsGroupSchema
>;

export type PermissionGroupEdit = Schema.Schema.Type<
  typeof PermissionsGroupEditSchema
>;

export type PermissionsGroupDetail = Schema.Schema.Type<
  typeof PermissionsGroupDetailSchema
>;

export type PermissionsGroupInput = Schema.Schema.Type<
  typeof PermissionsGroupInputSchema
>;
export type PermissionsGroupList = Schema.Schema.Type<
  typeof PermissionsGroupListSchema
>;
export type PermissionsGroupSelect = Schema.Schema.Type<
  typeof PermissionGroupSelectSchema
>;

export type PermissionsGroupPermission = Schema.Schema.Type<
  typeof PermissionsGroupPermissionSchema
>;

export type DBPermissionsGroupInput = Schema.Schema.Type<
  typeof DBPermissionsGroupInputSchema
>;
