import { useGetSingleInfiniteControlledList } from '@/hooks/controlled-list/useInfiniteControlledList';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useAppStore } from '@/providers/app-store-provider';
import type { ControlledListKey } from '@/types/common';
import { useDebounce } from '@uidotdev/usehooks';
import { useCallback, useEffect, useState } from 'react';

export const useControlledListSelectsData = (
  controlledListKeys: ControlledListKey[],
) => {
  const locale = useAvailableLocale();
  const setControlledListFilter = useAppStore(
    (state) => state.setControlledListFilter,
  );

  const [searchTerm, setSearchTerm] = useState('');
  const [activeControlledListKey, setActiveControlledListKey] =
    useState<ControlledListKey>();
  const debouncedSearchTerm = useDebounce(searchTerm, 250);

  useEffect(() => {
    if (activeControlledListKey) {
      setControlledListFilter(activeControlledListKey, debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, activeControlledListKey, setControlledListFilter]);

  const handleOnSearchTermChange = useCallback(
    (value: string, controlledListKey?: ControlledListKey) => {
      setSearchTerm(value);
      setActiveControlledListKey(controlledListKey);
    },
    [],
  );

  const selectsData = controlledListKeys.reduce<
    Partial<
      Record<
        ControlledListKey,
        ReturnType<typeof useGetSingleInfiniteControlledList>
      >
    >
  >((acc, key) => {
    acc[key] = useGetSingleInfiniteControlledList({
      controlledListKey: key,
      locale,
    });
    return acc;
  }, {});

  return { handleOnSearchTermChange, selectsData };
};
