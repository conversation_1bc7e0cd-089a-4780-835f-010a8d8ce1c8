import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class MediaTypeMigrationConverter extends BaseConverter {
  private mediaTypeMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlMediaType: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.mediaTypeMappings.push({
      mysqlId: mysqlMediaType.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for media_type table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'media_type',
      );

      if (insertStatements.length === 0) {
        console.log('No media_type INSERT statements found.');
        return;
      }

      const allPostgresMediaTypes: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlMediaTypes = this.parseInsertStatement(statement);
        const postgresMediaTypes = mysqlMediaTypes.map((mt) =>
          this.convertToPostgres(mt),
        );
        allPostgresMediaTypes.push(...postgresMediaTypes);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresMediaTypes,
          'media_types',
          'Media Type Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.mediaTypeMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'media_type', postgres: 'media_types' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresMediaTypes.length} media_types records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
