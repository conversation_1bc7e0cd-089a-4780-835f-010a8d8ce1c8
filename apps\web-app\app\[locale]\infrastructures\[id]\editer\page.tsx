import { EditInfrastructureForm } from '@/app/[locale]/infrastructures/[id]/editer/edit-infrastructure-form';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { mapInfrastructureFullToFormSchema } from '@/app/[locale]/infrastructures/helpers/map-infrastructure-to-form';
import { RouteGuard } from '@/components/permissions/route-guard';
import { infrastructureFormSections } from '@/constants/infrastructures';
import { getQueryClientOptions } from '@/constants/query-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import { infiniteControlledListsOptions } from '@/hooks/controlled-list/useInfiniteControlledListOptions';
import { infrastructureByIdOptions } from '@/hooks/infrastructure/useGetInfrastructureById';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';

export default async function EditInfrastructurePage(props: PageDetailsParams) {
  const params = await props.params;
  const { id, locale } = params;

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const infrastructure = await queryClient.fetchQuery(
    infrastructureByIdOptions(id, locale, {
      select: (data) =>
        mapInfrastructureFullToFormSchema(data.infrastructure, locale),
    }),
  );

  if (!infrastructure) {
    return notFound();
  }

  const controlledLists: ControlledListKey[] = [
    'person',
    'organisation',
    'unit',
    'fundingProjects',
    'local',
    'visibility',
    'infrastructureType',
    'infrastructureStatus',
    'innovationLab',
  ];

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  );

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchInfiniteQuery(
        infiniteControlledListsOptions({
          controlledListKey,
          locale,
          searchTerm: '',
        }),
      ),
    ),
  );

  const formSections = await getFormSections({
    resourceName: 'infrastructures',
    sections: infrastructureFormSections,
  });

  return (
    <RouteGuard operation="update" resource="infrastructure">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <EditInfrastructureForm
          formSections={formSections}
          id={id}
          locale={locale}
        />
      </HydrationBoundary>
    </RouteGuard>
  );
}
