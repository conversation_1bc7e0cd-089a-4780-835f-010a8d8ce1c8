import { ConfigLive } from '@rie/config';
import { Database } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

// Create the database layer - exported for reuse
export const PgDatabaseLayer = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      Database.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

// Service that provides direct access to database client
export class PgDatabaseLive extends Effect.Service<PgDatabaseLive>()(
  'PgDatabaseLive',
  {
    effect: Effect.gen(function* () {
      return yield* Database.PgDatabase;
    }),
  },
) {
  static readonly Layer = PgDatabaseLayer.pipe(
    Layer.flatMap(() => PgDatabaseLive.Default),
  );
}
