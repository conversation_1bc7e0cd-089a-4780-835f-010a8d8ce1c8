'use client';
import { FinancingProjectForm } from '@/app/[locale]/bottin/projets-financement/form/financing-project-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useUpdateFundingProject } from '@/hooks/bottin/funding-projects.hook';
import { useGetGenericById } from '@/hooks/bottin/generic-list.hook';
import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import type { ProjectFormSectionKey } from '@/types/bottin/project';
import type { SupportedLocale } from '@/types/locale';
import { FundingProjectEditToFormSchema } from '@rie/domain/serializers';
import * as Schema from 'effect/Schema';

type EditFundingProjectPageParams = {
  formSections: Record<ProjectFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionFundingProjectPage({
  formSections,
  id,
}: EditFundingProjectPageParams) {
  const {
    data: fundingProject,
    error,
    isPending,
  } = useGetGenericById<'fundingProjects', 'edit'>({
    controlledListKey: 'fundingProjects',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500"> Erreur : {error.message}</div>;
  }

  const updateFundingProject = useUpdateFundingProject();
  const onSubmit = async (data: FinancingProjectFormSchema) => {
    await updateFundingProject.mutateAsync({ id, payload: data });
  };

  // Transform funding project edit data to form schema using serializer
  const formData = Schema.decodeSync(FundingProjectEditToFormSchema)(
    fundingProject,
  ) as FinancingProjectFormSchema;

  return (
    <FinancingProjectForm
      defaultValues={formData}
      formSections={formSections}
      onSubmit={onSubmit}
    />
  );
}
