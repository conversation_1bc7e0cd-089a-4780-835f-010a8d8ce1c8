import { getControlledListById } from '@/services/controled-lists';
import type { ApiSingleReturnType, ControlledListKey } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import {
  queryOptions,
  useQuery,
  type UseQueryOptions,
} from '@tanstack/react-query';
import type { AxiosError } from 'axios';

export function controlledListByIdOptions<TData, TTransformed = TData>(
  id: string,
  locale: SupportedLocale,
  controlledList: ControlledListKey,
  options: Pick<
    UseQueryOptions<ApiSingleReturnType<TData>, AxiosError, TTransformed>,
    'select'
  >,
) {
  return queryOptions({
    queryFn: () =>
      getControlledListById<TData>({
        controlledListKey: controlledList,
        id,
        locale,
      }),
    queryKey: ['controlledListById', controlledList, id, locale],
    ...options,
  });
}

export const useGetControlledListById = <TData, TTransformed = TData>(
  controlledList: ControlledListKey,
  id: string,
  locale: SupportedLocale,
  options: Pick<
    UseQueryOptions<ApiSingleReturnType<TData>, AxiosError, TTransformed>,
    'select'
  >,
) => {
  return useQuery(
    controlledListByIdOptions<TData, TTransformed>(
      id,
      locale,
      controlledList,
      options,
    ),
  );
};
