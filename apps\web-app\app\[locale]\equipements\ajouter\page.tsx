import { NewEquipmentForm } from '@/app/[locale]/equipements/ajouter/new-equipment-form';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { RouteGuard } from '@/components/permissions/route-guard';
import {
  equipmentFormDefaultValues,
  equipmentFormSections,
} from '@/constants/equipments';
import { getQueryClientOptions } from '@/constants/query-client';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { allServiceContractOptions } from '@/hooks/contract-service/useGetAllServiceContracts';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import { infiniteControlledListsOptions } from '@/hooks/controlled-list/useInfiniteControlledListOptions';
import { allEquipmentsOptions } from '@/hooks/equipment/useGetAllEquipments';
import { infiniteEquipmentsOptions } from '@/hooks/equipment/useGetInfiniteEquipmentsOptions';
import { infiniteInfrastructuresOptions } from '@/hooks/infrastructure/useGetInfiniteInfrastructuresOptions';
import type { ControlledListKey, SearchPageParams } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function NewEquipmentPage(props: SearchPageParams) {
  const searchParams = await props.searchParams;
  const params = await props.params;

  const { locale } = params;

  const formSections = await getFormSections({
    resourceName: 'equipments',
    sections: equipmentFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = [
    'supplier',
    'equipmentType',
    'organisation',
    'person',
    'building',
    'local',
    'equipmentStatus',
    'equipmentCategory',
    'applicationSector',
    'researchDomain',
    'excellenceCenter',
    'technic',
    'fundingProjects',
    'documentationCategory',
  ];

  const searchParam = new URLSearchParams(searchParams);

  const defaultValues = {
    ...equipmentFormDefaultValues(locale),
    ...(searchParam.get('infrastructureId') &&
      searchParam.get('infrastructureLabel') && {
        infrastructure: {
          label: searchParam.get('infrastructureLabel') as string,
          value: searchParam.get('infrastructureId') as string,
        },
      }),
  };

  await Promise.all([
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchInfiniteQuery(
        infiniteControlledListsOptions({
          controlledListKey,
          locale,
          searchTerm: '',
        }),
      ),
    ),
    queryClient.prefetchQuery(
      allEquipmentsOptions({
        params: defaultRieServiceParams(locale),
        queryParams: '',
      }),
    ),
    queryClient.prefetchQuery(allServiceContractOptions(locale)),
    queryClient.prefetchInfiniteQuery(
      infiniteEquipmentsOptions({
        params: defaultRieServiceParams(locale),
        queryParams: '',
        viewOwnEquipments: false,
      }),
    ),
    queryClient.prefetchInfiniteQuery(
      infiniteInfrastructuresOptions({
        params: defaultRieServiceParams(locale),
        queryParams: '',
        viewOwnInfrastructures: false,
      }),
    ),
    queryClient.prefetchQuery(allServiceContractOptions(locale)),
  ]);
  return (
    <RouteGuard operation="create" resource="equipment">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <NewEquipmentForm
          defaultValues={defaultValues}
          formSections={formSections}
          locale={locale}
        />
      </HydrationBoundary>
    </RouteGuard>
  );
}
