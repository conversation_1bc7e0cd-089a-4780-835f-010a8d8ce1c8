import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  VendorEditSchema,
  VendorFormSchema,
  VendorInputSchema,
  VendorListSchema,
  VendorSchema,
  VendorSelectSchema,
} from '../schemas';
import type {
  CollectionViewType,
  ResourceViewType,
  Vendor,
  VendorList,
  VendorSelect,
} from '../types';

// Schema transformer for converting database vendor to list view
export const DbVendorToVendorList = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.String,
    startDate: Schema.NullishOr(Schema.String),
    endDate: Schema.NullishOr(Schema.String),
    createdAt: Schema.String,
    updatedAt: Schema.String,
    modifiedBy: Schema.NullishOr(Schema.String),
    translations: Schema.Array(
      Schema.Struct({
        id: Schema.String,
        locale: Schema.String,
        name: Schema.NullishOr(Schema.String),
        website: Schema.NullishOr(Schema.String),
        description: Schema.NullishOr(Schema.String),
        otherNames: Schema.NullishOr(Schema.String),
      }),
    ),
  }),
  VendorListSchema,
  {
    strict: false, // Allow missing fields
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation (could be 'fr' or 'en')
          const defaultTranslation =
            raw.translations.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations[0];

          return {
            id: raw.id,
            text: defaultTranslation?.name || raw.id,
            dateEnd: raw.endDate,
            lastUpdatedAt: raw.updatedAt,
            startDate: raw.startDate,
            endDate: raw.endDate,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse vendor for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database vendor to select view
export const DbVendorToVendorSelect = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.String,
    startDate: Schema.NullishOr(Schema.String),
    endDate: Schema.NullishOr(Schema.String),
    createdAt: Schema.String,
    updatedAt: Schema.String,
    modifiedBy: Schema.NullishOr(Schema.String),
    translations: Schema.Array(
      Schema.Struct({
        id: Schema.String,
        locale: Schema.String,
        name: Schema.NullishOr(Schema.String),
        website: Schema.NullishOr(Schema.String),
        description: Schema.NullishOr(Schema.String),
        otherNames: Schema.NullishOr(Schema.String),
      }),
    ),
  }),
  VendorSelectSchema,
  {
    strict: false, // Allow missing fields
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation for label
          const defaultTranslation =
            raw.translations.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations[0];

          return {
            value: raw.id,
            label: defaultTranslation?.name || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse vendor for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database vendor to edit format
export const DbVendorToVendorEdit = Schema.transformOrFail(
  VendorSchema,
  VendorEditSchema,
  {
    strict: false, // Allow missing fields
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            startDate: raw.startDate,
            endDate: raw.endDate,
            translations: raw.translations.map((translation) => ({
              locale: translation.locale as 'fr' | 'en',
              name: translation.name || '',
              website: translation.website || undefined,
              description: translation.description || undefined,
              otherNames: translation.otherNames || undefined,
            })),
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse vendor for edit view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of Vendor to VendorSelect[]
export const dbVendorsToVendorSelect = (
  dbVendors: Vendor[],
): VendorSelect[] => {
  return dbVendors.map((dbVendor) => {
    const defaultTranslation =
      dbVendor.translations.find(
        (translation) => translation.locale === 'fr',
      ) ||
      dbVendor.translations.find(
        (translation) => translation.locale === 'en',
      ) ||
      dbVendor.translations[0];

    return {
      value: dbVendor.id,
      label: defaultTranslation?.name || dbVendor.id,
    };
  });
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbVendorsToVendors = (
  dbVendors: Array<{
    id: string;
    startDate: string | null;
    endDate: string | null;
    createdAt: string;
    updatedAt: string;
    modifiedBy: string | null;
    translations: Array<{
      id: string;
      locale: string;
      name: string | null;
      website: string | null;
      description: string | null;
      otherNames: string | null;
    }>;
  }>,
  view: CollectionViewType,
): VendorList[] | VendorSelect[] => {
  return view === 'select'
    ? dbVendors.map((vendor) =>
        Schema.decodeUnknownSync(DbVendorToVendorSelect)(vendor),
      )
    : dbVendors.map((vendor) =>
        Schema.decodeUnknownSync(DbVendorToVendorList)(vendor),
      );
};

// New serializer function for Vendor with view parameter
export const dbVendorToVendor = (dbVendor: Vendor, view: ResourceViewType) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbVendorToVendorEdit)(dbVendor)
    : Schema.decodeUnknownSync(DbVendorToVendorList)(dbVendor);
};

// Transform client VendorFormSchema payload into VendorInputSchema (DB input)
export const VendorFormToDBInput = Schema.transformOrFail(
  VendorFormSchema,
  VendorInputSchema,
  {
    strict: false,
    decode: (val, _options, ast) =>
      ParseResult.try({
        try: () => {
          const translations = val.name.map((nameItem) => {
            const aliasItem = val.alias.find(
              (a) => a.locale === nameItem.locale,
            );
            return {
              locale: nameItem.locale as 'fr' | 'en',
              name: nameItem.value,
              website: undefined,
              description: undefined,
              otherNames: aliasItem?.value,
            };
          });

          return {
            startDate: null,
            endDate: val.dateEnd ?? null,
            translations,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            val,
            error instanceof Error
              ? error.message
              : 'Failed to convert vendor form to DB input',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);
