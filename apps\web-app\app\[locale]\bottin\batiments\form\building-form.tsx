'use client';

import { Description } from '@/app/[locale]/bottin/batiments/form/sections/description';
import { ResourceForm } from '@/components/resource-form/resource-form';
import type { BuildingFormSectionKey } from '@/types/building';
import { effectTsResolver } from '@hookform/resolvers/effect-ts';
import type { BuildingFormSchemaType } from '@rie/domain/types';
import { useForm } from 'react-hook-form';
import { buildingFormSchema } from '../(form)/building-form.schema';

type BuildingFormProps = {
  defaultValues: BuildingFormSchemaType;
  formSections: Record<BuildingFormSectionKey, string>;
  handleOnDelete?: () => void;
  onSubmit: (data: BuildingFormSchemaType) => Promise<void>;
};

export const BuildingForm = ({
  defaultValues,
  formSections,
  onSubmit,
}: BuildingFormProps) => {
  const form = useForm<BuildingFormSchemaType>({
    defaultValues,
    mode: 'onBlur',
    resolver: effectTsResolver(buildingFormSchema),
    reValidateMode: 'onChange',
  });

  return (
    <ResourceForm form={form} formSections={formSections} onSubmit={onSubmit}>
      <Description />
    </ResourceForm>
  );
};
