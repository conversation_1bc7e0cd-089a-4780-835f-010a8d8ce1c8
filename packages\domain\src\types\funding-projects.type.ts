import type * as Schema from 'effect/Schema';
import type {
  FundingProjectDetailSchema,
  FundingProjectEditSchema,
  FundingProjectInputSchema,
  FundingProjectListSchema,
  FundingProjectSchema,
  FundingProjectSelectSchema,
} from '../schemas/funding-projects.schema';

export type FundingProject = Schema.Schema.Type<typeof FundingProjectSchema>;

export type FundingProjectInput = Schema.Schema.Type<
  typeof FundingProjectInputSchema
>;

export type FundingProjectList = Schema.Schema.Type<
  typeof FundingProjectListSchema
>;
export type FundingProjectSelect = Schema.Schema.Type<
  typeof FundingProjectSelectSchema
>;
export type FundingProjectEdit = Schema.Schema.Type<
  typeof FundingProjectEditSchema
>;
export type FundingProjectDetail = Schema.Schema.Type<
  typeof FundingProjectDetailSchema
>;
