import { DbPermissionSelectSchema } from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  createRequiredStringOfMaxLengthSchema,
  descriptionSchema,
} from './base.schema';

export const DbPermissionsGroupSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  description: Schema.NullOr(Schema.String),
  permissions: Schema.Array(
    Schema.Struct({
      permission: DbPermissionSelectSchema.omit('createdAt', 'updatedAt'),
    }),
  ),
  createdAt: Schema.String,
  updatedAt: Schema.String,
});

const PermissionIdsSchema = Schema.Array(
  Schema.Struct({
    value: Schema.String,
    label: Schema.String,
  }),
).pipe(
  Schema.minItems(1, {
    message: () => 'At least one permission is required',
  }),
);

export const PermissionsGroupEditSchema = Schema.Struct({
  ...DbPermissionsGroupSchema.omit(
    'permissions',
    'description',
    'createdAt',
    'updatedAt',
  ).fields,
  description: Schema.optional(Schema.String),
  permissions: PermissionIdsSchema,
});

export const PermissionsGroupPermissionSchema = Schema.Struct({
  groupId: Schema.String,
  permissionId: Schema.String,
});

const nameSchema = createRequiredStringOfMaxLengthSchema({
  fieldMaxLength: 50,
  errorMessages: {
    required: () => 'Name is required',
    maxLength: (issue) =>
      `Name must be ${issue._tag.length} characters or less`,
  },
});

/**
 * Schema for creating a new permission group
 */
export const PermissionsGroupInputSchema = Schema.Struct({
  name: nameSchema,
  description: descriptionSchema,
  permissions: PermissionIdsSchema,
});

export const DBPermissionsGroupInputSchema = Schema.Struct({
  name: nameSchema,
  description: descriptionSchema,
  permissions: Schema.Array(Schema.String),
});

export const PermissionsGroupListSchema = Schema.Struct({
  ...DbPermissionsGroupSchema.omit('permissions').fields,
  permissions: Schema.Array(
    DbPermissionSelectSchema.omit('createdAt', 'updatedAt'),
  ),
});

export const PermissionsGroupDetailSchema = PermissionsGroupListSchema;

export const PermissionGroupSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});
