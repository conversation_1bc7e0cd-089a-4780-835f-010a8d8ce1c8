import { VendorNotFoundError } from '@rie/domain/errors';
import type { VendorFormSchema, VendorInputSchema } from '@rie/domain/schemas';
import { VendorFormToDBInput } from '@rie/domain/serializers';
import { VendorsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';

type VendorForm = Schema.Schema.Type<typeof VendorFormSchema>;
type VendorInput = Schema.Schema.Type<typeof VendorInputSchema>;

export class VendorsServiceLive extends Effect.Service<VendorsServiceLive>()(
  'VendorsServiceLive',
  {
    dependencies: [VendorsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllVendors = () =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          return yield* repo.findAllVendors();
        });

      const getVendorById = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          const vendor = yield* repo.findVendorById(id);
          if (!vendor) {
            return yield* Effect.fail(new VendorNotFoundError({ id }));
          }
          return vendor;
        });

      // Create from form payload
      const createVendorFromForm = ({
        payload,
        modifiedBy,
      }: {
        payload: VendorForm;
        modifiedBy?: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          const vendor = yield* Schema.decode(VendorFormToDBInput)(payload);
          const vendorWithMetadata = modifiedBy
            ? { ...vendor, modifiedBy }
            : vendor;
          return yield* repo.createVendor({ vendor: vendorWithMetadata });
        });

      // Create with DB-ready input
      const createVendor = (vendor: VendorInput) =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          return yield* repo.createVendor({ vendor });
        });

      // Update from form payload
      const updateVendorFromForm = ({
        id,
        payload,
        modifiedBy,
      }: {
        payload: VendorForm;
        id: string;
        modifiedBy?: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          const existingVendor = yield* repo.findVendorById(id);
          if (!existingVendor) {
            return yield* Effect.fail(new VendorNotFoundError({ id }));
          }
          const vendor = yield* Schema.decode(VendorFormToDBInput)(payload);
          const vendorWithMetadata = modifiedBy
            ? { ...vendor, modifiedBy }
            : vendor;
          return yield* repo.updateVendor({
            vendorId: id,
            vendor: vendorWithMetadata,
          });
        });

      // Update with DB-ready input
      const updateVendor = ({
        id,
        vendor,
      }: {
        vendor: VendorInput;
        id: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          const existingVendor = yield* repo.findVendorById(id);
          if (!existingVendor) {
            return yield* Effect.fail(new VendorNotFoundError({ id }));
          }
          return yield* repo.updateVendor({
            vendorId: id,
            vendor,
          });
        });

      const deleteVendor = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          const existingVendor = yield* repo.findVendorById(id);
          if (!existingVendor) {
            return yield* Effect.fail(new VendorNotFoundError({ id }));
          }
          const result = yield* repo.deleteVendor(id);
          return result.length > 0;
        });

      return {
        getAllVendors,
        getVendorById,
        createVendorFromForm,
        createVendor,
        updateVendorFromForm,
        updateVendor,
        deleteVendor,
      } as const;
    }),
  },
) {}
