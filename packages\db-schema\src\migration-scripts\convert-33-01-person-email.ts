import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { SQL_FILE_NAME } from '@/migration-scripts/constants';
import { PersonEmailMigrationConverter } from './converters/33-01-convert-person-email-inserts';

async function convertPersonEmailData() {
  const scriptDir = __dirname;
  // Input file is in the data directory
  const inputFile = path.join(
    scriptDir,
    'data',
    'mysql_riedb-content_24-04-2025.sql',
  );
  // Output will go to migration-scripts/output
  const outputDir = path.join(scriptDir, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create output directory if it doesn't exist
    await fs.mkdir(outputDir, { recursive: true });

    // Check if input file exists
    try {
      await fs.access(inputFile);
    } catch (error) {
      console.error(`Input file not found: ${inputFile}`);
      console.error(
        'Please make sure the MySQL dump file is in the correct location.',
      );
      process.exit(1);
    }

    // Initialize converter
    const converter = new PersonEmailMigrationConverter();

    // Convert the file
    await converter.convertFile(inputFile, outputFile);

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertPersonEmailData().catch(console.error);
