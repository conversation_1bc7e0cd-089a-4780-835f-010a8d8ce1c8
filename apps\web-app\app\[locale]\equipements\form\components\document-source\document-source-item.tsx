import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { InputField } from '@/components/form-fields/input-field';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { UploadFile } from '@/components/upload-file/upload-file';
import { FILE_MAX_UPLOAD_SIZE_MB } from '@/constants/equipments';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { MediaFileType, MediaSourceType } from '@/types/media';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { ToggleGroup, ToggleGroupItem } from '@/ui/toggle-group';
import { useTranslations } from 'next-intl';
import {
  type ArrayPath,
  type Control,
  type FieldValues,
  type Path,
  useFormContext,
} from 'react-hook-form';

import { getDefaultMediaValue } from '../media-source/media-source.helpers';

type FileSourceProps<TFieldData extends FieldValues> = {
  control: Control<TFieldData>;
  fieldName: Path<TFieldData>;
  source: MediaSourceType;
};

export const DocumentSourceItem = <TFieldData extends FieldValues>({
  control,
  fieldName,
  source,
}: FileSourceProps<TFieldData>) => {
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations('equipments.form.sections.description');
  const locale = useAvailableLocale();

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['documentationCategory']);
  const { clearErrors, setValue, watch } = useFormContext<TFieldData>();
  const fileSource = watch(fieldName);
  const descriptionFieldName =
    `${fieldName}.descriptions` as ArrayPath<TFieldData>;
  const { fields, handleAddTranslation, handleRemoveTranslation } =
    useTranslatedField(control, descriptionFieldName);

  return (
    <div className="grid gap-y-4">
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName={descriptionFieldName as Path<TFieldData>}
        fields={fields}
        label={(locale) =>
          tEquipments('media.fields.files.descriptionWithLocale', {
            locale: tCommon(locale),
          })
        }
        maxLength={2000}
        onAddTranslation={handleAddTranslation}
        onRemoveTranslation={handleRemoveTranslation}
      />
      <FormField
        control={control}
        name={`${fieldName}.type` as Path<TFieldData>}
        render={({ field }) => (
          <FormItem className="w-fit">
            <LabelTooltip htmlFor={`${fieldName}.type`} label="" />
            <FormControl>
              <ToggleGroup
                onValueChange={(value) => {
                  if (value !== '') {
                    const currentValue = fileSource;
                    const newValue = getDefaultMediaValue(
                      source,
                      locale,
                      value as MediaFileType,
                      currentValue,
                    );
                    setValue(fieldName as Path<TFieldData>, {
                      ...currentValue,
                      ...newValue,
                      type: value,
                    });
                    clearErrors(fieldName);
                  }
                }}
                type="single"
                value={field.value}
                variant="outline"
              >
                <ToggleGroupItem value="file">
                  {tCommon('file')}
                </ToggleGroupItem>
                <ToggleGroupItem value="url">{tCommon('url')}</ToggleGroupItem>
              </ToggleGroup>
            </FormControl>
          </FormItem>
        )}
      />
      <div className="grid w-full gap-y-4">
        {fileSource?.type === 'file' ? (
          <UploadFile
            description={tEquipments('media.fields.files.fileSize', {
              size: FILE_MAX_UPLOAD_SIZE_MB.toString(),
            })}
            fieldName={`${fieldName}.data` as Path<TFieldData>}
            label={
              fileSource?.data?.name ?? tEquipments('media.fields.files.upload')
            }
            source="documents"
          />
        ) : (
          <InputField
            className="w-full pb-6"
            fieldName={`${fieldName}.data` as Path<TFieldData>}
            label={tCommon('url')}
          />
        )}
        <ComboboxFieldInfiniteScroll
          clearErrorsOnChange
          controlledListKey="documentationCategory"
          fetchNextPage={selectsData.documentationCategory?.fetchNextPage}
          fieldLabel={tEquipments('media.fields.files.category')}
          fieldName={`${fieldName}.category` as Path<TFieldData>}
          hasNextPage={Boolean(selectsData.documentationCategory?.hasNextPage)}
          isFetching={Boolean(selectsData.documentationCategory?.isFetching)}
          isFetchingNextPage={Boolean(
            selectsData.documentationCategory?.isFetchingNextPage,
          )}
          onSearchChange={handleOnSearchTermChange}
          options={selectsData.documentationCategory?.data || []}
          placeholder={tCommon('select')}
          required={true}
        />
        <FormField
          control={control}
          name={`${fieldName}.isConfidential` as Path<TFieldData>}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex gap-x-2">
                  <Switch
                    checked={field.value}
                    id="isConfidential"
                    onCheckedChange={field.onChange}
                  />
                  <LabelTooltip
                    htmlFor="isConfidential"
                    label={tEquipments('media.fields.files.isConfidential')}
                  />
                </div>
              </FormControl>
              <FieldInfo>
                <FormMessage />
              </FieldInfo>
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
