import type { UserNotFoundError } from '@rie/domain/errors';
import { Forbidden } from '@rie/domain/errors';
import type { UserId } from '@rie/domain/schemas';
import type { PermissionAction, ResourceType } from '@rie/domain/types';
import type { Database } from '@rie/postgres-db';
import type {
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import type { NonEmptyReadonlyArray } from 'effect/Array';
import * as Context from 'effect/Context';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';
import type { AccessTreeServiceLive } from '../access-tree.service';
import { UserPermissionsServiceLive } from '../user-permissions.service';
import * as internal from './policy.internal';

// Type alias for all the dependencies and errors from UserPermissionsService
export type UserPermissionsError = Database.DatabaseError | UserNotFoundError;
export type UserPermissionsRequirements =
  | UserPermissionsServiceLive
  | AccessTreeServiceLive
  | UsersRepositoryLive
  | UnitsRepositoryLive
  | InfrastructuresRepositoryLive
  | EquipmentsRepositoryLive;
// ==========================================
// Permissions - Generated from Database Schema
// ==========================================

// Import the actual enum values from database schema
import { domainEnum, permissionActionEnum } from '@rie/db-schema/schemas';

// Generate permissions dynamically from your database schema
const generatePermissions = () => {
  const domains = domainEnum.enumValues;
  const actions = permissionActionEnum.enumValues;

  const permissionConfig = domains.reduce<
    Record<ResourceType, readonly PermissionAction[]>
  >(
    (acc, domain) => {
      acc[domain] = actions;
      return acc;
    },
    {} as Record<ResourceType, readonly PermissionAction[]>,
  );

  return internal.makePermissions(permissionConfig);
};

const allPermissions = generatePermissions();

export const Permission = Schema.Literal(...allPermissions).annotations({
  identifier: 'Permission',
});
export type Permission = typeof Permission.Type;

export class CurrentUser extends Context.Tag('CurrentUser')<
  CurrentUser,
  {
    readonly sessionId: string;
    readonly userId: UserId;
  }
>() {}

// ==========================================
// Policy
// ==========================================

/**
 * Represents an access policy that can be evaluated against the current user.
 * A policy is a function that returns Effect.void if access is granted,
 * or fails with a CustomHttpApiError.Forbidden if access is denied.
 */
export type Policy<E = never, R = never> = Effect.Effect<
  void,
  Forbidden | E,
  CurrentUser | R
>;

/**
 * Creates a policy from a predicate function that evaluates the current user.
 */
export const policy = <E, R>(
  predicate: (user: CurrentUser['Type']) => Effect.Effect<boolean, E, R>,
): Policy<E, R> =>
  CurrentUser.pipe(
    Effect.flatMap((user) =>
      Effect.flatMap(predicate(user), (result) =>
        result ? Effect.void : Effect.fail(new Forbidden()),
      ),
    ),
  );

/**
 * Applies a predicate as a pre-check to an effect.
 * If the predicate returns false, the effect will fail with Forbidden.
 */
export const withPolicy =
  <E, R>(policy: Policy<E, R>) =>
  <A, E2, R2>(self: Effect.Effect<A, E2, R2>) =>
    Effect.zipRight(policy, self);

/**
 * Composes multiple policies with AND semantics - all policies must pass.
 * Returns a new policy that succeeds only if all the given policies succeed.
 */
export const all = <E, R>(
  policies: NonEmptyReadonlyArray<Policy<E, R>>,
): Policy<E, R> =>
  Effect.all(policies, {
    concurrency: 1,
    discard: true,
  });

/**
 * Composes multiple policies with OR semantics - at least one policy must pass.
 * Returns a new policy that succeeds if any of the given policies succeed.
 */
export const any = <E, R>(
  policies: NonEmptyReadonlyArray<Policy<E, R>>,
): Policy<E, R> => Effect.firstSuccessOf(policies);

// Helper functions to extract domain and action from permission string
const extractDomain = (permission: Permission): ResourceType => {
  return permission.split(':')[0] as ResourceType;
};

const extractAction = (permission: Permission): PermissionAction => {
  return permission.split(':')[1] as PermissionAction;
};

/**
 * Creates a policy that checks if the current user has a specific permission.
 */
export const checkPermission = (
  requiredPermission: Permission,
): Policy<UserPermissionsError, UserPermissionsRequirements> =>
  policy((user) =>
    Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;

      const domain = extractDomain(requiredPermission);
      const action = extractAction(requiredPermission);

      return yield* userPermissionsService.userHasPermission({
        userId: user.userId,
        domain,
        action,
      });
    }),
  );

/**
 * Creates a policy that checks if the current user has a specific permission for a resource.
 * This leverages your hierarchical access tree for contextual permission checking.
 */
export const permissionForResource = (
  domain: ResourceType,
  action: PermissionAction,
  resourceId: string,
): Policy<UserPermissionsError, UserPermissionsRequirements> =>
  policy((user) =>
    Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;

      return yield* userPermissionsService.userHasPermission({
        userId: user.userId,
        domain,
        action,
        resourceId,
      });
    }),
  );

/**
 * Creates a policy that checks if the current user has access to a specific resource.
 * This only checks access (hierarchical) without checking specific permissions.
 */
export const resourceAccess = (
  resourceType: ResourceType,
  resourceId: string,
): Policy<UserPermissionsError, UserPermissionsRequirements> =>
  policy((user) =>
    Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;

      return yield* userPermissionsService.userHasAccessToResource(
        user.userId,
        resourceType,
        resourceId,
      );
    }),
  );
