import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type {
  BuildingEdit,
  BuildingFormSchemaType,
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

// Define result types based on the view parameter
export type CollectionBuildingResultType<
  View extends CollectionViewParamType['view'],
> = View extends 'select'
  ? Array<{ value: string; label: string }>
  : Array<{
      id: string;
      campusId: string | null;
      civicAddressId: string | null;
      sadId: string | null;
      diId: string | null;
      createdAt: string;
      updatedAt: string;
      modifiedBy: string | null;
      name: string | null;
      description: string | null;
    }>;

export type ResourceBuildingResultType<View extends ResourceViewType> =
  View extends 'edit'
    ? BuildingEdit
    : {
        id: string;
        campusId: string | null;
        civicAddressId: string | null;
        sadId: string | null;
        diId: string | null;
        createdAt: string;
        updatedAt: string;
        modifiedBy: string | null;
        name: string | null;
        description: string | null;
      };

export const getAllBuildings = async <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) => {
  try {
    const client = await getApiClient();
    return await client
      .get<CollectionBuildingResultType<View>>('v2/buildings', {
        searchParams: {
          view,
        },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch buildings');
  }
};

export type GetBuildingByIdParams = {
  view: ResourceViewType;
  id: string;
};

export const getBuildingById = async <View extends ResourceViewType>({
  id,
  view,
}: GetBuildingByIdParams) => {
  try {
    const client = await getApiClient();

    return await client
      .get<ResourceBuildingResultType<View>>(`v2/buildings/${id}`, {
        searchParams: {
          view,
        },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch building');
  }
};

export async function createBuilding(
  payload: BuildingFormSchemaType,
): Promise<BuildingEdit> {
  try {
    const client = await getApiClient();

    return await client
      .post<BuildingEdit>('v2/buildings', {
        json: payload,
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }

    // Otherwise, wrap it in a generic error
    throw new Error('Failed to create building');
  }
}

interface UpdateBuildingParams {
  payload: BuildingFormSchemaType;
  id: string;
}

export const updateBuilding = async ({ payload, id }: UpdateBuildingParams) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient
      .put<BuildingEdit>(`v2/buildings/${id}`, {
        json: payload,
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    // Otherwise, wrap it in a generic error
    throw new Error('Failed to update building');
  }
};

export const deleteBuilding = async (id: string) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.delete(`v2/buildings/${id}`);
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }

    throw new Error('Failed to delete building');
  }
};
