import {
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import { AccessTreeServiceLive } from '../access-tree.service';
import {
  type Policy,
  type UserPermissionsError,
  type UserPermissionsRequirements,
  all,
  any,
  checkPermission,
  permissionForResource,
  resourceAccess,
} from './policy.service';

import { UserPermissionsServiceLive } from '../user-permissions.service';

/**
 * Domain-specific policy service for infrastructure management.
 * This service provides composable policies that understand your business rules
 * and leverage your hierarchical permission system.
 */
export class InfrastructurePoliciesLive extends Effect.Service<InfrastructurePoliciesLive>()(
  'InfrastructurePoliciesLive',
  {
    effect: Effect.gen(function* () {
      /**
       * Policy to check if user can read infrastructure information
       */
      const canReadInfrastructure = (
        infrastructureId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        infrastructureId
          ? permissionForResource('infrastructure', 'read', infrastructureId)
          : checkPermission('infrastructure:read');

      /**
       * Policy to check if user can create infrastructure
       */
      const canCreateInfrastructure = checkPermission('infrastructure:create');

      /**
       * Policy to check if user can update specific infrastructure
       */
      const canUpdateInfrastructure = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('infrastructure', 'update', infrastructureId);

      /**
       * Policy to check if user can delete specific infrastructure
       */
      const canDeleteInfrastructure = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('infrastructure', 'delete', infrastructureId);

      /**
       * Policy to check if user can manage infrastructure within a unit
       * This combines unit access with infrastructure management permissions
       */
      const canManageUnitInfrastructure = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          resourceAccess('unit', unitId),
          checkPermission('infrastructure:update'),
        ]);

      /**
       * Policy for infrastructure editors - can read and update, but not delete
       */
      const infrastructureEditor = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('infrastructure', 'read', infrastructureId),
          permissionForResource('infrastructure', 'update', infrastructureId),
        ]);

      /**
       * Policy for infrastructure managers - full infrastructure management within their scope
       */
      const infrastructureManager = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('infrastructure', 'read', infrastructureId),
          permissionForResource('infrastructure', 'update', infrastructureId),
          permissionForResource('infrastructure', 'delete', infrastructureId),
        ]);

      /**
       * Policy that allows infrastructure modification through multiple paths
       */
      const canModifyInfrastructure = (
        infrastructureId: string,
        unitId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> => {
        const directInfrastructurePermission = permissionForResource(
          'infrastructure',
          'update',
          infrastructureId,
        );

        if (unitId) {
          return any([
            directInfrastructurePermission,
            canManageUnitInfrastructure(unitId),
          ] as const);
        }

        return directInfrastructurePermission;
      };

      /**
       * Policy for infrastructure export - requires read permission
       */
      const canExportInfrastructure = any([
        checkPermission('infrastructure:read'),
        checkPermission('unit:read'), // Unit managers can also export
      ]);

      /**
       * Advanced policy that checks for infrastructure access based on hierarchical rules
       */
      const hasInfrastructureAccess = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        resourceAccess('infrastructure', infrastructureId);

      /**
       * Policy for infrastructure discovery - allows read access to infrastructure listings
       */
      const canDiscoverInfrastructure = any([
        checkPermission('infrastructure:read'),
        checkPermission('unit:read'),
        checkPermission('institution:read'),
      ]);

      /**
       * Policy for managing infrastructure permissions - only for infrastructure managers
       */
      const canManageInfrastructurePermissions = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('infrastructure', 'update', infrastructureId),
          // Could add additional checks for role management permissions
        ]);

      return {
        canReadInfrastructure,
        canCreateInfrastructure,
        canUpdateInfrastructure,
        canDeleteInfrastructure,
        canManageUnitInfrastructure,
        infrastructureEditor,
        infrastructureManager,
        canModifyInfrastructure,
        canExportInfrastructure,
        hasInfrastructureAccess,
        canDiscoverInfrastructure,
        canManageInfrastructurePermissions,
      } as const;
    }),
  },
) {}

/**
 * Default layer for InfrastructurePoliciesLive
 */
export const InfrastructurePoliciesLayer = Layer.mergeAll(
  InfrastructurePoliciesLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
);
