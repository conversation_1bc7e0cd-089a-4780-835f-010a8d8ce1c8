import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class MeasurementUnitI18nMigrationConverter extends BaseConverter {
  private measurementUnitI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    measurementUnitIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the measurement_unit
    const newMeasurementUnitId =
      measurementUnitIdMappings[mysqlRecord.data_id.toString()];
    if (!newMeasurementUnitId) {
      throw new Error(
        `No mapping found for measurement_unit_id: ${mysqlRecord.data_id}`,
      );
    }

    // Store mapping for future reference
    this.measurementUnitI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newMeasurementUnitId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for unite_mesure_trad table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'unite_mesure_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No measurement_units_i18n INSERT statements found.');
        return;
      }

      // Load measurement_unit ID mappings
      const measurementUnitIdMappings =
        await this.loadEntityIdMappings('unite_mesure');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) =>
          this.convertToPostgres(record, measurementUnitIdMappings),
        );
        allPostgresRecords.push(...postgresRecords);
      }

      // Generate output
      const postgresInserts = this.generatePostgresI18NInsert(
        allPostgresRecords,
        'equipment_measurement_units_i18n',
        'Measurement Unit I18n Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.measurementUnitI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'unite_mesure_trad',
          postgres: 'equipment_measurement_units_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} equipment_measurement_units_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
