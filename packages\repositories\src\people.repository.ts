import { DBSchema } from '@rie/db-schema';
import type { PersonInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';

import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class PeopleRepositoryLive extends Effect.Service<PeopleRepositoryLive>()(
  'PeopleRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      const findAllPeople = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.people.findMany({
            columns: {
              id: true,
              isActive: true,
              guidId: true,
              uid: true,
              firstName: true,
              lastName: true,
              userId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              emails: {
                columns: {
                  id: true,
                  address: true,
                },
              },
            },
          }),
        );
      });

      const findPersonById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.people.findFirst({
            where: eq(DBSchema.people.id, id),
            columns: {
              id: true,
              isActive: true,
              guidId: true,
              uid: true,
              firstName: true,
              lastName: true,
              userId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              emails: {
                columns: {
                  id: true,
                  address: true,
                },
              },
            },
          }),
        );
      });

      const createPerson = (params: { person: PersonInput }) => {
        return dbClient.transaction((tx) => {
          return tx((client) =>
            client.insert(DBSchema.people).values(params.person).returning({
              id: DBSchema.people.id,
              guidId: DBSchema.people.guidId,
              uid: DBSchema.people.uid,
              firstName: DBSchema.people.firstName,
              lastName: DBSchema.people.lastName,
              userId: DBSchema.people.userId,
              createdAt: DBSchema.people.createdAt,
              updatedAt: DBSchema.people.updatedAt,
              modifiedBy: DBSchema.people.modifiedBy,
              isActive: DBSchema.people.isActive,
              emails: DBSchema.peopleAssociatedEmails.address,
            }),
          );
        });
      };

      const updatePerson = (params: {
        personId: string;
        person: PersonInput;
      }) => {
        return dbClient.transaction((tx) => {
          return tx((client) =>
            client
              .update(DBSchema.people)
              .set({
                ...params.person,
              })
              .where(eq(DBSchema.people.id, params.personId))
              .returning({
                id: DBSchema.people.id,
                guidId: DBSchema.people.guidId,
                uid: DBSchema.people.uid,
                firstName: DBSchema.people.firstName,
                lastName: DBSchema.people.lastName,
                userId: DBSchema.people.userId,
                createdAt: DBSchema.people.createdAt,
                updatedAt: DBSchema.people.updatedAt,
                modifiedBy: DBSchema.people.modifiedBy,
                isActive: DBSchema.people.isActive,
                emails: DBSchema.peopleAssociatedEmails.address,
              }),
          );
        });
      };

      const deletePerson = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.people)
            .where(eq(DBSchema.people.id, id))
            .returning({ id: DBSchema.people.id }),
        );
      });

      return {
        findAllPeople,
        findPersonById,
        createPerson,
        updatePerson,
        deletePerson,
      } as const;
    }),
  },
) {}
