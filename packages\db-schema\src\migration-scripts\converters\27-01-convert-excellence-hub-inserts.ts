import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class ExcellenceHubMigrationConverter extends BaseConverter {
  private excellenceHubMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlExcellenceHub: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.excellenceHubMappings.push({
      mysqlId: mysqlExcellenceHub.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for pole_excellence table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'pole_excellence',
      );

      if (insertStatements.length === 0) {
        console.log('No pole_excellence INSERT statements found.');
        return;
      }

      const allPostgresExcellenceHubs: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlExcellenceHubs = this.parseInsertStatement(statement);
        const postgresExcellenceHubs = mysqlExcellenceHubs.map((eh) =>
          this.convertToPostgres(eh),
        );
        allPostgresExcellenceHubs.push(...postgresExcellenceHubs);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresExcellenceHubs,
          'excellence_hubs',
          'Excellence Hub Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.excellenceHubMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'pole_excellence', postgres: 'excellence_hubs' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresExcellenceHubs.length} excellence_hubs records`,
      );
    } catch (error) {
      console.error('Error converting file:', error);
      throw error;
    }
  }
}
