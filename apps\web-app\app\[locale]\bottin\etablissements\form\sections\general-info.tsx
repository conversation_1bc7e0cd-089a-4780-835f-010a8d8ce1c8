import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { Input } from '@/components/ui/input';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { EstablishmentFormSchema } from '@/schemas/bottin/establishment-form-schema';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const GeneralInfo = () => {
  const tEstablishments = useTranslations(
    'directory.form.sections.description.generalInfo',
  );
  const tCommon = useTranslations('common');
  const { control, formState } = useFormContext<EstablishmentFormSchema>();

  const nameFields = useTranslatedField(control, 'name');
  const acronymField = useTranslatedField(control, 'acronym');
  const pseudonymField = useTranslatedField(control, 'pseudonym');

  const nameErrorMessage = getFieldErrorMessage(formState.errors, 'name')
    ? getFieldErrorMessage(formState.errors, 'name')
    : undefined;

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['establishmentType']);

  return (
    <FormSubsection title={tEstablishments('title')}>
      <FieldWithTranslations
        control={control}
        errorMessage={nameErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="name"
        fields={nameFields.fields}
        label={(locale) =>
          tEstablishments('fields.name.label', { locale: tCommon(locale) })
        }
        maxLength={150}
        onAddTranslation={nameFields.handleAddTranslation}
        onRemoveTranslation={nameFields.handleRemoveTranslation}
        required={true}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="acronym"
        fields={acronymField.fields}
        label={(locale) =>
          tEstablishments('fields.acronym.label', { locale: tCommon(locale) })
        }
        maxLength={30}
        onAddTranslation={acronymField.handleAddTranslation}
        onRemoveTranslation={acronymField.handleRemoveTranslation}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="pseudonym"
        fields={pseudonymField.fields}
        label={(locale) =>
          tEstablishments('fields.pseudonym.label', { locale: tCommon(locale) })
        }
        maxLength={1000}
        onAddTranslation={pseudonymField.handleAddTranslation}
        onRemoveTranslation={pseudonymField.handleRemoveTranslation}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="establishmentType"
        fetchNextPage={selectsData.establishmentType?.fetchNextPage}
        fieldLabel={tEstablishments('fields.establishmentType.label')}
        fieldName="establishmentType"
        hasNextPage={Boolean(selectsData.establishmentType?.hasNextPage)}
        isFetching={Boolean(selectsData.establishmentType?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.establishmentType?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.establishmentType?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
    </FormSubsection>
  );
};
