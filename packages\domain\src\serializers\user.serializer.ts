import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  DbUserDetailWithRolesSchema,
  UserDetailWithRolesSchemas,
} from '../schemas/user-permissions.schema';
import type {
  DbUserDetailWithRoles,
  UserDetailWithRoles,
} from '../types/user-permissions.type';

export const DbUserDetailWithRolesToUserDetailWithRoles =
  Schema.transformOrFail(
    DbUserDetailWithRolesSchema,
    UserDetailWithRolesSchemas,
    {
      strict: false,
      decode: (dbUser, _options, ast) => {
        return ParseResult.try({
          try: () => ({
            id: dbUser.id,
            name: dbUser.name,
            email: dbUser.email,
            emailVerified: dbUser.emailVerified,
            image: dbUser.image,
            roles: dbUser.userRoles.map((userRole) => ({
              id: userRole.role.id,
              name: userRole.role.name,
              description: userRole.role.description,
              // TODO: FIXME - needs to make sure role <PERSON>de<PERSON> has ressourceType
              resourceType: userRole.resourceType ?? 'user',
              resourceId: userRole.resourceId,
              permissions: userRole.role.rolePermissionGroups.flatMap(
                (rolePermissionGroup) =>
                  rolePermissionGroup.group.permissions.map((permission) => ({
                    id: permission.permission.id,
                    domain: permission.permission.domain,
                    action: permission.permission.action,
                  })),
              ),
            })),
          }),
          catch: (error) =>
            new ParseResult.Type(
              ast,
              dbUser,
              error instanceof Error
                ? error.message
                : 'Failed to transform user with roles',
            ),
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

export const transformDbUserDetailWithRolesToUserDetailWithRoles = (
  userWithRoles: DbUserDetailWithRoles,
): UserDetailWithRoles => {
  const result = Schema.decodeEither(
    DbUserDetailWithRolesToUserDetailWithRoles,
  )(userWithRoles);

  if (result._tag === 'Left') {
    throw new Error(`Failed to transform user: ${result.left.message}`);
  }

  return result.right;
};
