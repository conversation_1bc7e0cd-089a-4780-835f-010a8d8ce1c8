import { infrastructures, infrastructuresI18N } from '@rie/db-schema/schemas';
import type { InfrastructureInputSchema } from '@rie/domain/schemas';
import { PgDatabaseLive } from '@rie/postgres-db';
import { count, eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type InfrastructureInput = Schema.Schema.Type<typeof InfrastructureInputSchema>;

export class InfrastructuresRepositoryLive extends Effect.Service<InfrastructuresRepositoryLive>()(
  'InfrastructuresRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      /**
       * Find infrastructures with their related equipments by unit ID
       * This optimizes the access tree building by reducing multiple queries to a single query
       */
      const findInfrastructuresWithRelatedEntitiesByUnitId = dbClient.makeQuery(
        (execute, unitId: string) => {
          return execute((client) =>
            client.query.infrastructures.findMany({
              where: eq(infrastructures.unitId, unitId),
              columns: {
                id: true,
              },
              // no relations to keep typing minimal
            }),
          );
        },
      );
      const findAllInfrastructures = dbClient.makeQuery((execute) => {
        return execute((client) => {
          return client.query.infrastructures.findMany({
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              addressId: true,
              statusId: true,
              unitId: true,
              website: true,
              is_featured: true,
              visibilityId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
              equipments: {
                columns: { id: true },
              },
              type: {
                columns: { id: true },
                with: {
                  translations: { columns: { locale: true, name: true } },
                },
              },
              status: {
                columns: { id: true },
                with: {
                  translations: { columns: { locale: true, name: true } },
                },
              },
              unit: {
                columns: { id: true },
                with: {
                  translations: {
                    columns: { locale: true, name: true, description: true },
                  },
                },
              },
              address: {
                columns: {
                  id: true,
                  addressType: true,
                  campusAddressId: true,
                  civicAddressId: true,
                },
                with: {
                  civicAddress: {
                    columns: {
                      street1: true,
                      city: true,
                      state: true,
                      postalCode: true,
                      countryCode: true,
                    },
                  },
                  campusAddress: { columns: { id: true, room_id: true } },
                },
              },
              associatedScientificManagers: {
                columns: { id: true },
                with: {
                  person: {
                    columns: {
                      id: true,
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
              associatedOperationalManagers: {
                columns: { personId: true, infrastructureId: true },
                with: {
                  person: {
                    columns: {
                      id: true,
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          });
        });
      });

      const findInfrastructureById = dbClient.makeQuery(
        (execute, id: string) => {
          return execute((client) => {
            return client.query.infrastructures.findFirst({
              where: eq(infrastructures.id, id),
              columns: {
                id: true,
                guidId: true,
                typeId: true,
                addressId: true,
                statusId: true,
                unitId: true,
                website: true,
                is_featured: true,
                visibilityId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                    description: true,
                    otherNames: true,
                    acronyms: true,
                  },
                },
                type: {
                  columns: { id: true },
                  with: {
                    translations: {
                      columns: { locale: true, name: true, description: true },
                    },
                  },
                },
                status: {
                  columns: { id: true },
                  with: {
                    translations: {
                      columns: { locale: true, name: true, description: true },
                    },
                  },
                },
                unit: {
                  columns: { id: true },
                  with: {
                    translations: {
                      columns: { locale: true, name: true, description: true },
                    },
                  },
                },
                address: {
                  columns: {
                    id: true,
                    addressType: true,
                    campusAddressId: true,
                    civicAddressId: true,
                  },
                  with: {
                    civicAddress: {
                      columns: {
                        street1: true,
                        city: true,
                        state: true,
                        postalCode: true,
                        countryCode: true,
                      },
                    },
                    campusAddress: { columns: { id: true, room_id: true } },
                  },
                },
              },
            });
          });
        },
      );

      const findInfrastructureWithRelatedEquipmentsById = dbClient.makeQuery(
        (execute, id: string) => {
          return execute((client) =>
            client.query.infrastructures.findFirst({
              where: eq(infrastructures.id, id),
              columns: {
                id: true,
              },
              // no relations to keep typing minimal
            }),
          );
        },
      );

      // Relation probes
      // removed probe helpers

      const countAllInfrastructures = dbClient.makeQuery((execute) => {
        return execute(async (client) => {
          const rows = await client
            .select({ value: count() })
            .from(infrastructures);
          return Number(rows[0]?.value ?? 0);
        });
      });

      const createInfrastructure = (params: {
        infrastructure: InfrastructureInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Create the infrastructure
            const [createdInfrastructure] = yield* tx((client) =>
              client
                .insert(infrastructures)
                .values({
                  ...params.infrastructure,
                })
                .returning({
                  id: infrastructures.id,
                  guidId: infrastructures.guidId,
                  typeId: infrastructures.typeId,
                  addressId: infrastructures.addressId,
                  statusId: infrastructures.statusId,
                  unitId: infrastructures.unitId,
                  website: infrastructures.website,
                  is_featured: infrastructures.is_featured,
                  visibilityId: infrastructures.visibilityId,
                  createdAt: infrastructures.createdAt,
                  updatedAt: infrastructures.updatedAt,
                  modifiedBy: infrastructures.modifiedBy,
                }),
            );

            if (!createdInfrastructure) {
              return yield* Effect.fail(
                new Error('Failed to create infrastructure'),
              );
            }

            // Create the translations
            const translationsToInsert = params.infrastructure.translations.map(
              (translation) => ({
                dataId: createdInfrastructure.id,
                ...translation,
              }),
            );
            const createdTranslations = yield* tx((client) =>
              client
                .insert(infrastructuresI18N)
                .values(translationsToInsert)
                .returning({
                  id: infrastructuresI18N.id,
                  locale: infrastructuresI18N.locale,
                  name: infrastructuresI18N.name,
                  description: infrastructuresI18N.description,
                  otherNames: infrastructuresI18N.otherNames,
                  acronyms: infrastructuresI18N.acronyms,
                }),
            );

            // Return the infrastructure with its translations directly
            return {
              ...createdInfrastructure,
              translations: createdTranslations,
            };
          });
        });
      };

      const updateInfrastructure = (params: {
        infrastructureId: string;
        infrastructure: InfrastructureInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the infrastructure
            const [updatedInfrastructure] = yield* tx((client) =>
              client
                .update(infrastructures)
                .set({
                  ...params.infrastructure,
                })
                .where(eq(infrastructures.id, params.infrastructureId))
                .returning({
                  id: infrastructures.id,
                  guidId: infrastructures.guidId,
                  typeId: infrastructures.typeId,
                  addressId: infrastructures.addressId,
                  statusId: infrastructures.statusId,
                  unitId: infrastructures.unitId,
                  website: infrastructures.website,
                  is_featured: infrastructures.is_featured,
                  visibilityId: infrastructures.visibilityId,
                  createdAt: infrastructures.createdAt,
                  updatedAt: infrastructures.updatedAt,
                  modifiedBy: infrastructures.modifiedBy,
                }),
            );

            // Delete existing translations
            yield* tx((client) =>
              client
                .delete(infrastructuresI18N)
                .where(eq(infrastructuresI18N.dataId, params.infrastructureId)),
            );

            // Insert new translations
            const translationsToInsert = params.infrastructure.translations.map(
              (translation) => ({
                dataId: params.infrastructureId,
                ...translation,
              }),
            );

            const updatedTranslations = yield* tx((client) =>
              client
                .insert(infrastructuresI18N)
                .values(translationsToInsert)
                .returning({
                  id: infrastructuresI18N.id,
                  locale: infrastructuresI18N.locale,
                  name: infrastructuresI18N.name,
                  description: infrastructuresI18N.description,
                  otherNames: infrastructuresI18N.otherNames,
                  acronyms: infrastructuresI18N.acronyms,
                }),
            );

            // Return the infrastructure with its translations directly
            return {
              ...updatedInfrastructure,
              translations: updatedTranslations,
            };
          });
        });
      };

      const deleteInfrastructure = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(infrastructures)
            .where(eq(infrastructures.id, id))
            .returning({ id: infrastructures.id }),
        );
      });

      return {
        findInfrastructuresWithRelatedEntitiesByUnitId,
        findAllInfrastructures,
        findInfrastructureById,
        findInfrastructureWithRelatedEquipmentsById,
        countAllInfrastructures,
        createInfrastructure,
        updateInfrastructure,
        deleteInfrastructure,
      } as const;
    }),
  },
) {}
