'use client';

import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import type { InfrastructureFormSchema } from '@/schemas/infrastructure-form-schema';
import { clsx } from 'clsx';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const InCharge = () => {
  const tInfrastructures = useTranslations(
    'infrastructures.form.sections.description.inCharge',
  );
  const tCommon = useTranslations('common');
  const {
    formState: { errors },
    watch,
  } = useFormContext<InfrastructureFormSchema>();

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData([
      'person',
      'organisation',
      'unit',
      'fundingProjects',
      'local',
    ]);

  const scientificAndTechnicalManagerErrorMessage = getFieldErrorMessage(
    errors,
    'scientificAndTechnicalManager',
  );

  const scientificAndTechnicalManager = watch('scientificAndTechnicalManager');

  const isError = Boolean(
    scientificAndTechnicalManagerErrorMessage &&
      !scientificAndTechnicalManager?.scientificManager?.length &&
      !scientificAndTechnicalManager?.technicalManager?.length,
  );

  return (
    <FormSubsection title={tInfrastructures('title')}>
      <MultiselectField
        className={clsx({
          'text-red-500': isError,
        })}
        fieldName="scientificAndTechnicalManager.scientificManager"
        label={tInfrastructures(
          'fields.scientificAndTechnicalManager.scientificManager.label',
        )}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
      />

      <MultiselectField
        className={clsx({
          'text-red-500': isError,
        })}
        fieldName="scientificAndTechnicalManager.technicalManager"
        label={tInfrastructures(
          'fields.scientificAndTechnicalManager.technicalManager.label',
        )}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
      />

      <MultiselectField
        fieldName="sstManager"
        label={tInfrastructures('fields.sstManager.label')}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="organisation"
        fetchNextPage={selectsData.organisation?.fetchNextPage}
        fieldLabel={tInfrastructures('fields.jurisdiction.label')}
        fieldName="jurisdiction"
        hasNextPage={Boolean(selectsData.organisation?.hasNextPage)}
        isFetching={Boolean(selectsData.organisation?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.organisation?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.organisation?.data ?? []}
        placeholder={tCommon('select')}
        required
        tooltip={tInfrastructures('fields.jurisdiction.tooltip')}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="unit"
        fetchNextPage={selectsData.unit?.fetchNextPage}
        fieldLabel={tInfrastructures('fields.parentUnit.label')}
        fieldName="parentUnit"
        hasNextPage={Boolean(selectsData.unit?.hasNextPage)}
        isFetching={Boolean(selectsData.unit?.isFetching)}
        isFetchingNextPage={Boolean(selectsData.unit?.isFetchingNextPage)}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.unit?.data ?? []}
        placeholder={tCommon('select')}
        required
      />

      <MultiselectField
        fieldName="associatedFinancialProject"
        label={tInfrastructures('fields.associatedFinancialProject.label')}
        options={selectsData.financingProjectType?.data ?? []}
        placeholder={tCommon('select')}
        tooltip={tInfrastructures('fields.associatedFinancialProject.tooltip')}
      />

      <MultiselectField
        fieldName="locationBuilding"
        label={tInfrastructures('fields.locationBuilding.label')}
        options={selectsData.local?.data ?? []}
        placeholder={tCommon('select')}
      />
    </FormSubsection>
  );
};
