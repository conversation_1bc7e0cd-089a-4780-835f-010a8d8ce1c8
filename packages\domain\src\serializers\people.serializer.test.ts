import * as Either from 'effect/Either';
import * as Schema from 'effect/Schema';
import { describe, expect, it } from 'vitest';
import {
  DbPersonToPersonList,
  DbPersonToPersonSelect,
} from './people.serializer';

describe('people.serializer', () => {
  describe('DbPersonToPersonList', () => {
    it('should decode PersonSchema to PersonListSchema', () => {
      const rawPerson = {
        id: '123',
        guidId: 'guid-123',
        uid: 'uid-123',
        firstName: 'John',
        lastName: 'Doe',
        userId: 'user-123',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        modifiedBy: null,
        isActive: true,
        emails: [
          { id: 'email-1', address: '<EMAIL>' },
          { id: 'email-2', address: '<EMAIL>' },
        ],
      };

      const result = Schema.decodeEither(DbPersonToPersonList)(rawPerson);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({
          id: '123',
          firstName: 'John',
          lastName: 'Doe',
          email: ['<EMAIL>', '<EMAIL>'],
          lastUpdatedAt: '2023-01-01T00:00:00Z',
          guidId: 'guid-123',
          uid: 'uid-123',
          userId: 'user-123',
        });
      }
    });
  });

  describe('DbPersonToPersonSelect', () => {
    it('should decode PersonSchema to PersonSelectSchema', () => {
      const rawPerson = {
        id: '456',
        guidId: 'guid-456',
        uid: 'uid-456',
        firstName: 'Jane',
        lastName: 'Smith',
        userId: 'user-456',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        modifiedBy: null,
        isActive: true,
        emails: [{ id: 'email-3', address: '<EMAIL>' }],
      };

      const result = Schema.decodeEither(DbPersonToPersonSelect)(rawPerson);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({
          value: '456',
          label: 'Jane Smith',
        });
      }
    });
  });
});
