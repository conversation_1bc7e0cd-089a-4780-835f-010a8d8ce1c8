import EditionEstablishmentPage from '@/app/[locale]/bottin/etablissements/[id]/editer/edit-establishment-page';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { establishmentFormSections } from '@/constants/bottin/establishment';
import { getQueryClientOptions } from '@/constants/query-client';
import { getGenericByIdOptions } from '@/hooks/bottin/generic-list.options';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';

type EditEstablishmentPageParams = PageDetailsParams;
export default async function EditEstablishmentPage(
  props: EditEstablishmentPageParams,
) {
  const params = await props.params;

  const { id, locale } = params;

  const formSections = await getFormSections({
    resourceName: 'establishments',
    sections: establishmentFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const t0 = performance.now();

  const controlledLists: ControlledListKey[] = ['establishmentType'];

  const establishment = await Promise.all([
    queryClient.fetchQuery(
      getGenericByIdOptions({
        controlledListKey: 'establishment',
        id,
        view: 'edit',
      }),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "establishmentType" and "establishment" took ${t1 - t0} milliseconds.`,
  );

  if (!establishment) {
    return notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionEstablishmentPage
        formSections={formSections}
        id={id}
        locale={locale}
      />
    </HydrationBoundary>
  );
}
