import {
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import { AccessTreeServiceLive } from '../access-tree.service';
import { UserPermissionsServiceLive } from '../user-permissions.service';
import {
  type Policy,
  type UserPermissionsError,
  type UserPermissionsRequirements,
  all,
  any,
  checkPermission,
  permissionForResource,
  resourceAccess,
} from './policy.service';

/**
 * Domain-specific policy service for institution management.
 * This service provides composable policies for institution operations that require
 * both permission checking and resource-specific access verification.
 */
export class InstitutionPoliciesLive extends Effect.Service<InstitutionPoliciesLive>()(
  'InstitutionPoliciesLive',
  {
    effect: Effect.gen(function* () {
      /**
       * Policy to check if user can read institution information
       */
      const canReadInstitution = (
        institutionId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        institutionId
          ? permissionForResource('institution', 'read', institutionId)
          : checkPermission('institution:read');

      /**
       * Policy to check if user can create institutions
       * Typically only system administrators can create institutions
       */
      const canCreateInstitution = checkPermission('institution:create');

      /**
       * Policy to check if user can update specific institution
       */
      const canUpdateInstitution = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('institution', 'update', institutionId);

      /**
       * Policy to check if user can delete specific institution
       */
      const canDeleteInstitution = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('institution', 'delete', institutionId);

      /**
       * Policy for institution editors - can read and update, but not delete
       */
      const institutionEditor = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('institution', 'read', institutionId),
          permissionForResource('institution', 'update', institutionId),
        ]);

      /**
       * Policy for institution administrators - full institution management
       */
      const institutionAdmin = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('institution', 'read', institutionId),
          permissionForResource('institution', 'update', institutionId),
          permissionForResource('institution', 'delete', institutionId),
        ]);

      /**
       * Policy for institution export - requires read permission
       */
      const canExportInstitutions = checkPermission('institution:read');

      /**
       * Advanced policy that checks for institution access based on hierarchical rules
       */
      const hasInstitutionAccess = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        resourceAccess('institution', institutionId);

      /**
       * Policy for institution discovery - allows read access to institution listings
       */
      const canDiscoverInstitutions = checkPermission('institution:read');

      /**
       * Policy for managing institution permissions - only for institution administrators
       */
      const canManageInstitutionPermissions = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('institution', 'update', institutionId),
          checkPermission('userRole:update'), // Additional check for role management
        ]);

      /**
       * Policy to check if user can view institution financial information
       */
      const canViewInstitutionFinancials = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('institution', 'read', institutionId),
          checkPermission('fundingProject:read'), // Financial data access
        ]);

      /**
       * Policy to check if user can manage institution users
       */
      const canManageInstitutionUsers = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('institution', 'update', institutionId),
          checkPermission('user:update'),
        ]);

      /**
       * Policy for viewing institution statistics and reports
       */
      const canViewInstitutionReports = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('institution', 'read', institutionId),
          any([
            checkPermission('infrastructure:read'),
            checkPermission('equipment:read'),
            checkPermission('unit:read'),
          ]),
        ]);

      return {
        canReadInstitution,
        canCreateInstitution,
        canUpdateInstitution,
        canDeleteInstitution,
        institutionEditor,
        institutionAdmin,
        canExportInstitutions,
        hasInstitutionAccess,
        canDiscoverInstitutions,
        canManageInstitutionPermissions,
        canViewInstitutionFinancials,
        canManageInstitutionUsers,
        canViewInstitutionReports,
      } as const;
    }),
  },
) {}

/**
 * Default layer for InstitutionPoliciesLive
 */
export const InstitutionPoliciesLayer = Layer.mergeAll(
  InstitutionPoliciesLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
);
