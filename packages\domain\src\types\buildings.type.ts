import type * as Schema from 'effect/Schema';
import type {
  BuildingDetailSchema,
  BuildingEditSchema,
  BuildingFormSchema,
  BuildingInputSchema,
  BuildingListSchema,
  BuildingSchema,
  BuildingSelectSchema,
  DBBuildingInputSchema,
  DbBuildingSchema,
} from '../schemas/buildings.schema';

export type DbBuilding = Schema.Schema.Type<typeof DbBuildingSchema>;

export type Building = Schema.Schema.Type<typeof BuildingSchema>;

export type BuildingList = Schema.Schema.Type<typeof BuildingListSchema>;

export type BuildingSelect = Schema.Schema.Type<typeof BuildingSelectSchema>;

export type BuildingEdit = Schema.Schema.Type<typeof BuildingEditSchema>;

export type BuildingDetail = Schema.Schema.Type<typeof BuildingDetailSchema>;

export type BuildingInput = Schema.Schema.Type<typeof BuildingInputSchema>;

export type DBBuildingInput = Schema.Schema.Type<typeof DBBuildingInputSchema>;

export type BuildingFormSchemaType = Schema.Schema.Type<
  typeof BuildingFormSchema
>;
