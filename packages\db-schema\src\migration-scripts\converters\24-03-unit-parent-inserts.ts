import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';

export class JurisdictionMigrationConverter extends BaseConverter {
  private unitParentsMappings: Record<
    string,
    {
      unit_parent_id: string;
      parent: {
        type: 'institution' | 'unit';
        parentId: string;
      };
    }
  > = {};

  private parseUnitWithParents(
    unitWithParents: Record<
      string,
      {
        unitPostgresId: string;
        parent: {
          mysqlId: '1';
          type: 'institution';
          postgresId: 'sydsapwdh5p2m9z9tvnde2dp';
        };
      }
    >,
  ) {
    return Object.values(unitWithParents).reduce<
      {
        id: string;
        type: 'institution' | 'unit';
        institutionId: string | null;
        unitId: string | null;
      }[]
    >((acc, value) => {
      const postgresId = this.generateCuid2();
      const unitPostgresId = value.unitPostgresId;
      const mysqlParentId = value.parent.mysqlId;
      const postgresParentId = value.parent.postgresId;
      const parentType = value.parent.type;

      if (!postgresParentId) {
        console.warn(
          `No parent found for unit: ${mysqlParentId} - ${JSON.stringify(
            value,
            null,
            2,
          )}}`,
        );
      }

      if (!postgresParentId) {
        console.warn(`No mapping found for parent_id: ${mysqlParentId}`);
      } else {
        this.unitParentsMappings[unitPostgresId] = {
          unit_parent_id: postgresId,
          parent: {
            type: parentType,
            parentId: postgresParentId,
          },
        };

        acc.push(
          parentType === 'institution'
            ? {
                id: postgresId,
                type: 'institution',
                institutionId: postgresParentId,
                unitId: null,
              }
            : {
                id: postgresId,
                type: 'unit',
                institutionId: null,
                unitId: unitPostgresId,
              },
        );
      }
      return acc;
    }, []);
  }

  async convertFile(outputPath: string): Promise<void> {
    try {
      const unitMappings = await this.loadEntityIdMappings('unite_avec_parent');

      const unitParents = this.parseUnitWithParents(unitMappings);

      // Add the actual INSERT statement
      const columns = ['id', 'type', 'institution_id', 'unit_id'];

      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          unitParents,
          'unit_parents',
          'Unit Parents Inserts',
          columns,
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'juridiction', postgres: 'unit_parents' },
        this.unitParentsMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${unitParents.length} unit_parents records`);
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
