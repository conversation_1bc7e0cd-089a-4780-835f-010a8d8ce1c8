import {
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import { AccessTreeServiceLive } from '../access-tree.service';
import { UserPermissionsServiceLive } from '../user-permissions.service';
import {
  type Policy,
  type UserPermissionsError,
  type UserPermissionsRequirements,
  all,
  any,
  checkPermission,
  permissionForResource,
  resourceAccess,
} from './policy.service';

/**
 * Domain-specific policy service for unit management.
 * This service provides composable policies for unit operations that require
 * both permission checking and resource-specific access verification.
 */
export class UnitPoliciesLive extends Effect.Service<UnitPoliciesLive>()(
  'UnitPoliciesLive',
  {
    effect: Effect.gen(function* () {
      /**
       * Policy to check if user can read unit information
       */
      const canReadUnit = (
        unitId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        unitId
          ? permissionForResource('unit', 'read', unitId)
          : checkPermission('unit:read');

      /**
       * Policy to check if user can create units
       * This requires checking if they have create permission and access to parent institution
       */
      const canCreateUnit = (
        institutionId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> => {
        const createPermission = checkPermission('unit:create');

        if (institutionId) {
          return all([
            createPermission,
            resourceAccess('institution', institutionId),
          ]);
        }

        return createPermission;
      };

      /**
       * Policy to check if user can update specific unit
       */
      const canUpdateUnit = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('unit', 'update', unitId);

      /**
       * Policy to check if user can delete specific unit
       */
      const canDeleteUnit = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('unit', 'delete', unitId);

      /**
       * Policy to check if user can manage units within an institution
       */
      const canManageInstitutionUnits = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          resourceAccess('institution', institutionId),
          checkPermission('unit:update'),
        ]);

      /**
       * Policy for unit editors - can read and update, but not delete
       */
      const unitEditor = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('unit', 'read', unitId),
          permissionForResource('unit', 'update', unitId),
        ]);

      /**
       * Policy for unit managers - full unit management within their scope
       */
      const unitManager = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('unit', 'read', unitId),
          permissionForResource('unit', 'update', unitId),
          permissionForResource('unit', 'delete', unitId),
        ]);

      /**
       * Flexible policy that allows either direct unit permission or institution management
       */
      const canModifyUnit = (
        unitId: string,
        institutionId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> => {
        const directUnitPermission = permissionForResource(
          'unit',
          'update',
          unitId,
        );

        if (institutionId) {
          return any([
            directUnitPermission,
            canManageInstitutionUnits(institutionId),
          ] as const);
        }

        return directUnitPermission;
      };

      /**
       * Advanced policy that checks for unit access based on hierarchical rules
       */
      const hasUnitAccess = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        resourceAccess('unit', unitId);

      /**
       * Policy for unit discovery - allows read access to unit listings
       */
      const canDiscoverUnits = any([
        checkPermission('unit:read'),
        checkPermission('institution:read'),
      ]);

      /**
       * Policy for managing unit permissions - only for unit managers
       */
      const canManageUnitPermissions = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('unit', 'update', unitId),
          checkPermission('userRole:update'), // Additional check for role management
        ]);

      /**
       * Policy to check if user can view unit financial information
       */
      const canViewUnitFinancials = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          permissionForResource('unit', 'read', unitId),
          checkPermission('fundingProject:read'), // Financial data access
        ]);

      return {
        canReadUnit,
        canCreateUnit,
        canUpdateUnit,
        canDeleteUnit,
        canManageInstitutionUnits,
        unitEditor,
        unitManager,
        canModifyUnit,
        hasUnitAccess,
        canDiscoverUnits,
        canManageUnitPermissions,
        canViewUnitFinancials,
      } as const;
    }),
  },
) {}

/**
 * Default layer for UnitPoliciesLive
 */
export const UnitPoliciesLayer = Layer.mergeAll(
  UnitPoliciesLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
);
