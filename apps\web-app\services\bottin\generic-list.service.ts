import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type { ControlledListKey } from '@/types/common';
import type {
  BuildingDetail,
  BuildingEdit,
  BuildingList,
  BuildingSelect,
  CampusDetail,
  CampusEdit,
  CampusList,
  CampusSelect,
  CollectionViewParamType,
  FundingProjectDetail,
  FundingProjectEdit,
  FundingProjectList,
  FundingProjectSelect,
  InstitutionDetail,
  InstitutionEdit,
  InstitutionList,
  InstitutionSelect,
  PersonDetail,
  PersonEdit,
  PersonList,
  PersonSelect,
  ResourceViewType,
  RoomDetail,
  RoomEdit,
  RoomList,
  RoomSelect,
  UnitDetail,
  UnitEdit,
  UnitList,
  UnitSelect,
  VendorDetail,
  VendorEdit,
  VendorList,
  VendorSelect,
} from '@rie/domain/types';

// Mapping des controlledListKey vers les endpoints API
const API_ENDPOINTS: Partial<Record<ControlledListKey, string>> = {
  building: 'v2/buildings',
  campus: 'v2/campuses',
  organisation: 'v2/institutions',
  person: 'v2/people',
  unit: 'v2/units',
  establishment: 'v2/institutions',
  local: 'v2/rooms',
  supplier: 'v2/vendors',
  fundingProjects: 'v2/funding-projects',
  // Ajoutez d'autres mappings selon les besoins quand ils seront disponibles
} as const;

// Types spécifiques pour les bâtiments
export type BuildingListResultType<
  View extends CollectionViewParamType['view'],
> = View extends 'select' ? BuildingSelect[] : BuildingList[];

export type BuildingResourceResultType<View extends ResourceViewType> =
  View extends 'edit'
    ? BuildingEdit
    : View extends 'detail'
      ? BuildingDetail
      : never;

// Types génériques qui s'adaptent selon le controlledListKey
export type GenericListResultType<
  Key extends ControlledListKey,
  View extends CollectionViewParamType['view'],
> = Key extends 'building'
  ? BuildingListResultType<View>
  : Key extends 'campus'
    ? View extends 'select'
      ? CampusSelect[]
      : CampusList[]
    : Key extends 'organisation' | 'establishment'
      ? View extends 'select'
        ? InstitutionSelect[]
        : InstitutionList[]
      : Key extends 'person'
        ? View extends 'select'
          ? PersonSelect[]
          : PersonList[]
        : Key extends 'unit'
          ? View extends 'select'
            ? UnitSelect[]
            : UnitList[]
          : Key extends 'local'
            ? View extends 'select'
              ? RoomSelect[]
              : RoomList[]
            : Key extends 'supplier'
              ? View extends 'select'
                ? VendorSelect[]
                : VendorList[]
              : Key extends 'fundingProjects'
                ? View extends 'select'
                  ? FundingProjectSelect[]
                  : FundingProjectList[]
                : Array<{ value: string; label: string }>; // Fallback

export type GenericResourceResultType<
  Key extends ControlledListKey,
  View extends ResourceViewType,
> = Key extends 'building'
  ? BuildingResourceResultType<View>
  : Key extends 'campus'
    ? View extends 'edit'
      ? CampusEdit
      : CampusDetail
    : Key extends 'organisation' | 'establishment'
      ? View extends 'edit'
        ? InstitutionEdit
        : InstitutionDetail
      : Key extends 'person'
        ? View extends 'edit'
          ? PersonEdit
          : PersonDetail
        : Key extends 'unit'
          ? View extends 'edit'
            ? UnitEdit
            : UnitDetail
          : Key extends 'local'
            ? View extends 'edit'
              ? RoomEdit
              : RoomDetail
            : Key extends 'supplier'
              ? View extends 'edit'
                ? VendorEdit
                : VendorDetail
              : Key extends 'fundingProjects'
                ? View extends 'edit'
                  ? FundingProjectEdit
                  : FundingProjectDetail
                : { id: string; [key: string]: unknown }; // Fallback

export async function getGenericList<
  Key extends ControlledListKey,
  View extends CollectionViewParamType['view'],
>({
  controlledListKey,
  view,
}: {
  controlledListKey: Key;
  view: View;
}): Promise<GenericListResultType<Key, View>> {
  const endpoint = API_ENDPOINTS[controlledListKey];

  if (!endpoint) {
    throw new Error(
      `No API endpoint defined for controlledListKey: ${controlledListKey}`,
    );
  }

  try {
    const client = await getApiClient();
    return await client
      .get<GenericListResultType<Key, View>>(endpoint, {
        searchParams: {
          view,
        },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error(`Failed to fetch ${controlledListKey} list`);
  }
}

export async function getGenericById<
  Key extends ControlledListKey,
  View extends ResourceViewType,
>({
  controlledListKey,
  id,
  view,
}: {
  controlledListKey: Key;
  id: string;
  view: View;
}): Promise<GenericResourceResultType<Key, View>> {
  const endpoint = API_ENDPOINTS[controlledListKey];

  if (!endpoint) {
    throw new Error(
      `No API endpoint defined for controlledListKey: ${controlledListKey}`,
    );
  }

  try {
    const client = await getApiClient();
    return await client
      .get<GenericResourceResultType<Key, View>>(`${endpoint}/${id}`, {
        searchParams: {
          view,
        },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error(`Failed to fetch ${controlledListKey} with id ${id}`);
  }
}

// Generic create (POST) — expects client form payload; server will serialize
export async function createGeneric<
  Key extends ControlledListKey,
  Payload extends Record<string, unknown>,
>({
  controlledListKey,
  payload,
}: {
  controlledListKey: Key;
  payload: Payload;
}) {
  const endpoint = API_ENDPOINTS[controlledListKey];
  if (!endpoint) throw new Error(`No API endpoint for ${controlledListKey}`);
  try {
    const client = await getApiClient();
    return await client.post(`${endpoint}`, { json: payload }).json();
  } catch (error) {
    if (error instanceof APIV2Error) throw error;
    throw new Error(`Failed to create ${controlledListKey}`);
  }
}

// Generic update (PUT)
export async function updateGeneric<
  Key extends ControlledListKey,
  Payload extends Record<string, unknown>,
>({
  controlledListKey,
  id,
  payload,
}: {
  controlledListKey: Key;
  id: string;
  payload: Payload;
}) {
  const endpoint = API_ENDPOINTS[controlledListKey];
  if (!endpoint) throw new Error(`No API endpoint for ${controlledListKey}`);
  try {
    const client = await getApiClient();
    return await client.put(`${endpoint}/${id}`, { json: payload }).json();
  } catch (error) {
    if (error instanceof APIV2Error) throw error;
    throw new Error(`Failed to update ${controlledListKey}`);
  }
}

// Generic delete (DELETE)
export async function deleteGeneric<Key extends ControlledListKey>({
  controlledListKey,
  id,
}: {
  controlledListKey: Key;
  id: string;
}) {
  const endpoint = API_ENDPOINTS[controlledListKey];
  if (!endpoint) throw new Error(`No API endpoint for ${controlledListKey}`);
  try {
    const client = await getApiClient();
    return await client.delete(`${endpoint}/${id}`).json();
  } catch (error) {
    if (error instanceof APIV2Error) throw error;
    throw new Error(`Failed to delete ${controlledListKey}`);
  }
}
