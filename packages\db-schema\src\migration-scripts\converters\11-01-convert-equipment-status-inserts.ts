import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class EquipmentStatusMigrationConverter extends BaseConverter {
  private equipmentStatusMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlEquipmentStatus: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.equipmentStatusMappings.push({
      mysqlId: mysqlEquipmentStatus.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for etat_equipement table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'etat_equipement',
      );

      if (insertStatements.length === 0) {
        console.log('No etat_equipement INSERT statements found.');
        return;
      }

      const allPostgresEquipmentStatuses: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlEquipmentStatuses = this.parseInsertStatement(statement);
        const postgresEquipmentStatuses = mysqlEquipmentStatuses.map((es) =>
          this.convertToPostgres(es),
        );
        allPostgresEquipmentStatuses.push(...postgresEquipmentStatuses);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresEquipmentStatuses,
          'equipment_statuses',
          'Equipment Status Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.equipmentStatusMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'etat_equipement', postgres: 'equipment_statuses' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresEquipmentStatuses.length} equipment_statuses records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
