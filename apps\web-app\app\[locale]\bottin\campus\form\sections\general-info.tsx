import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { FieldInfo } from '@/components/FieldInfo';
import { CharacterCount } from '@/components/character-count/character-count';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { CampusFormSchema } from '@/schemas/bottin/campus-form-schema';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const GeneralInfo = () => {
  const tCampus = useTranslations(
    'directory.form.sections.description.generalInfo',
  );
  const tCommon = useTranslations('common');
  const { control, formState } = useFormContext<CampusFormSchema>();
  const nameFields = useTranslatedField(control, 'name');

  const nameErrorMessage = getFieldErrorMessage(formState.errors, 'name')
    ? getFieldErrorMessage(formState.errors, 'name')
    : undefined;

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['organisation']);

  return (
    <FormSubsection title={tCampus('title')}>
      <FieldWithTranslations
        control={control}
        errorMessage={nameErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="name"
        fields={nameFields.fields}
        label={(locale) =>
          tCampus('fields.name.label', { locale: tCommon(locale) })
        }
        maxLength={150}
        onAddTranslation={nameFields.handleAddTranslation}
        onRemoveTranslation={nameFields.handleRemoveTranslation}
        required={true}
      />
      <FormField
        control={control}
        name="pseudonym"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor="pseudonym"
              label={tCampus('fields.pseudonym.label')}
              tooltip={tCampus('fields.pseudonym.tooltip')}
            />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
              <CharacterCount count={field.value?.length ?? 0} max={1000} />
            </FieldInfo>
          </FormItem>
        )}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="organisation"
        fetchNextPage={selectsData.organisation?.fetchNextPage}
        fieldLabel={tCampus('fields.jurisdiction.label')}
        fieldName="jurisdiction"
        hasNextPage={selectsData.organisation?.hasNextPage ?? false}
        isFetching={Boolean(selectsData.organisation?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.organisation?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.organisation?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
    </FormSubsection>
  );
};
