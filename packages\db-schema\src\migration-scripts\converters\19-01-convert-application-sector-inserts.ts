import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class ApplicationSectorMigrationConverter extends BaseConverter {
  private applicationSectorMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlApplicationSector: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.applicationSectorMappings.push({
      mysqlId: mysqlApplicationSector.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for secteur_application table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'secteur_application',
      );

      if (insertStatements.length === 0) {
        console.log('No secteur_application INSERT statements found.');
        return;
      }

      const allPostgresApplicationSectors: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlApplicationSectors = this.parseInsertStatement(statement);
        const postgresApplicationSectors = mysqlApplicationSectors.map(
          (sector) => this.convertToPostgres(sector),
        );
        allPostgresApplicationSectors.push(...postgresApplicationSectors);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresApplicationSectors,
          'application_sectors',
          'Application Sector Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.applicationSectorMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'secteur_application', postgres: 'application_sectors' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresApplicationSectors.length} application_sectors records`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  protected extractValuesFromInsertStatement<T extends Record<string, any>>(
    sqlStatement: string,
  ): T {
    // Extract the column names and values from the INSERT statement
    const match = sqlStatement.match(
      /INSERT\s+INTO\s+(?:`|")?secteur_application(?:`|")?\s*\(([^)]+)\)\s*VALUES\s*\(([^)]+)\)/i,
    );

    if (!match) {
      throw new Error(
        `Could not extract values from INSERT statement: ${sqlStatement}`,
      );
    }

    const columns = match[1]
      ?.split(',')
      .map((col) => col.trim().replace(/`/g, ''));
    const values = match[2]
      ?.split(',')
      .map((val) => val.trim().replace(/^'|'$/g, ''));

    // Create a record mapping column names to their values
    let record = {} as T;
    if (!columns || !values) {
      return record;
    }
    for (let i = 0; i < columns.length; i++) {
      const column = columns[i] as keyof T;

      if (column) {
        record = { ...record, ...{ [column]: values[i] } };
      }
    }

    return record;
  }
}
