import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import { type I18NColumnMapper, i18nColumnMapper } from '../constants';
import type { Mapping, MySqlI18NBase, PgI18NBase } from '../types';

export class CampusI18nMigrationConverter extends BaseConverter {
  private campusI18nMappings: Mapping[] = [];

  private parseCampusI18NInsertStatement(
    sqlStatement: string,
  ): MySqlI18NBase[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        nom: values.nom,
      },
    ];
  }

  private convertToPostgres(
    mysqlRecord: MySqlI18NBase,
    campusIdMappings: Record<string, string>,
  ): PgI18NBase {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the campus
    const newCampusId = campusIdMappings[mysqlRecord.data_id.toString()];
    if (!newCampusId) {
      throw new Error(`No mapping found for campus_id: ${mysqlRecord.data_id}`);
    }
    // Store mapping for future reference
    this.campusI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newCampusId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for campus_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'campus_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No campus_trad INSERT statements found.');
        return;
      }

      // Load campus ID mappings
      const campusIdMappings = await this.loadEntityIdMappings('campus');

      const allPostgresRecords: PgI18NBase[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseCampusI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, campusIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      const columns = this.extractColumnNames(insertStatements[0] ?? '')?.map(
        (column) =>
          (i18nColumnMapper[column as I18NColumnMapper] ?? column) as string,
      );

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'campuses_i18n',
        'Campus I18n Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.campusI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'campus_trad',
          postgres: 'campuses_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresRecords.length} campuses_i18n records`);
    } catch (error) {
      console.error('Error converting campus i18n data:', error);
      throw error;
    }
  }
}
