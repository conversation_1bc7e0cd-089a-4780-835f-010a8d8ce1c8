{"permissions": {"address-read": "zuxy4il1q13feyubfr9y4oqn", "address-create": "gn8whwitx9m2ryuolsxvqblk", "address-update": "ugrkhk39ujs5b47pi7z8irbp", "address-delete": "rgxhcm4qn9du05pepyeq84kh", "applicationSector-read": "avauw1hahtq69s7d032ywfql", "applicationSector-create": "egqbonvsh01mn9i0rsc36bjk", "applicationSector-update": "e1olq66vye4j9sts7e6ely7s", "applicationSector-delete": "cmu6ba8eyesjtp84gl4nqdw6", "building-read": "kenj7z7h9d3fpqarozc6xvj0", "building-create": "wnpe82o5sbt923mbdnajfkpu", "building-update": "a32b0ltj4ty35z6svznuo7mh", "building-delete": "zb5up90jhtuay16hke433vz0", "campus-read": "qgfkn8bovzqc17mrq25phr5f", "campus-create": "enektv1uh1fg398gz7uag3h0", "campus-update": "rwqafgqx0zz211jeor9jar4l", "campus-delete": "utnhc1kr02hq9tn11srg7e8p", "equipment-read": "vzk1cudz6kfadq7ijcpn88n4", "equipment-create": "dc94esemtc5w5xz0zrpnf7d5", "equipment-update": "qxqu0a7ocay8fmrv45jk6sj0", "equipment-delete": "frsodzd74cuubztgg8x6orby", "excellenceHub-read": "rtonivqjy9pexen91qs5z0it", "excellenceHub-create": "tecm9y7u4qfjv5sq82zj4tv6", "excellenceHub-update": "iscvsnpxh9dwi031iccc989m", "excellenceHub-delete": "kime3duo2a90ia2y7gj96der", "fundingProject-read": "xgw8n1nfiaa1z7wp1h1eamyl", "fundingProject-create": "oj88qqv1opmvmdefkcr7fwak", "fundingProject-update": "jneop3ni56q4dw8cyehho1l0", "fundingProject-delete": "xxle35pqrlsxtysjlx0u67z8", "infrastructure-read": "qxl20jz66rtmwc6nqn314rjj", "infrastructure-create": "scu9ra8n8cq9hv6n7avuo105", "infrastructure-update": "yn52215fknv2ac6ybsxnq4fi", "infrastructure-delete": "voy8xwtiqgzuzb2mj8f1vtot", "innovationLab-read": "k59wyrqgxtrz69ybfy3np58g", "innovationLab-create": "uq578o2lm4lyifxkuwdb79oq", "innovationLab-update": "wr3lcrurnmkzv3b98qqjzafd", "innovationLab-delete": "je87athw1w2vniaywjfn0gu6", "institution-read": "prr5q163na53u1ebbxn6qwb8", "institution-create": "csrnf8749mwgsbvti7egbopj", "institution-update": "sz3t2lrxdqousfd7gc71rz01", "institution-delete": "f66y7r8s07oiosaehbemoc8m", "media-read": "udc4w6x51ey9wlkasianetab", "media-create": "vd6xsvmd0txfpbsuy5uuf4f2", "media-update": "nx3628ueh7903p69d4zre2m3", "media-delete": "jup5ry46x2njlsrwgdgcaiw0", "people-read": "dkq5nek6qmrmbce0xei3bfse", "people-create": "n0b6ip7ukgalbswkc3m2pi34", "people-update": "u8p4axshxkc1xd0d1n3jmuoo", "people-delete": "jlc12oksclcvyf1rxqitoqek", "researchField-read": "rcovsqzbjvmi6cdr586konnx", "researchField-create": "cavabi5xh17w2kbptzsyorot", "researchField-update": "mem9z8yol5is0a8e1u6l25uh", "researchField-delete": "v809935ht4wcha47oipj2v3v", "room-read": "chvkh84dpfsxajq70k9a9n2f", "room-create": "yd4j12iam5mu9t0kk4bhnpqw", "room-update": "lii7x8xovjl7xwpk474i5bm2", "room-delete": "v4vkt80mb6ay6dotcpj3o091", "serviceContract-read": "ltm0eqe7ejknockh5yz4whwj", "serviceContract-create": "zr2slo5k7r6cnzvsajdmyvqm", "serviceContract-update": "po21ya6r0x2kj716cfhghjw0", "serviceContract-delete": "gsxsojkx5ttg4z86w72nyxlq", "serviceOffer-read": "hmaq9q1nzqhb7jrc9ex4lmxt", "serviceOffer-create": "xxtnv790eyugoz1s9h4bucjf", "serviceOffer-update": "qgqf51hwblu1cdfun3i5ymc4", "serviceOffer-delete": "fgt0izxm1043gunfsiksb482", "technique-read": "k9pyeyvt9udj9r8l4s8d8pmr", "technique-create": "r6n1egnbfsjgsgb9b994919u", "technique-update": "x768iyf57kxuvnu44ugpcj9k", "technique-delete": "s5dr1mioxpzlx931st87zj28", "unit-read": "g4rhgqkt8yvu4xq21eycqzin", "unit-create": "fybdu0h2gcr3dltebgm4w1ru", "unit-update": "y47fcor1qvbbsgs1kag1p0rs", "unit-delete": "j1v7wgn7ltkuaicuxjri1nvc", "vendor-read": "eczttj9n4wxrcczk4xv6qv47", "vendor-create": "sjngu7onkpk3ztpau8bqujox", "vendor-update": "yk0y6wf3yh2i89sitz4kz6jk", "vendor-delete": "u6am6xzf9lrqxyc0damca3j4", "visibility-read": "u8loy75qw08z10zvredjd0b7", "visibility-create": "wk4zi8k99vw02my9zmpgws3c", "visibility-update": "c3yk4wd1qk83z288dpckmerk", "visibility-delete": "asbsp99lss4ddhcjzsmvwo0n"}}