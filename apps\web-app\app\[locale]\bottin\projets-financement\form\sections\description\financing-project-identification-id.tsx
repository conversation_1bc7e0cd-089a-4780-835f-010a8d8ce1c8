import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxField } from '@/components/form-fields/combobox-field';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

type IdProps = {
  index: number;
};

export const FinancingProjectIdentificationId = ({ index }: IdProps) => {
  const tProjects = useTranslations(
    'financingProjects.form.sections.description.generalInfo.fields',
  );
  const tCommon = useTranslations('common');
  const { control } = useFormContext<FinancingProjectFormSchema>();
  const { selectsData } = useControlledListSelectsData(['numberType']);

  return (
    <div className="flex flex-wrap items-center gap-4">
      <div className="min-w-[200px] flex-1">
        <FormField
          control={control}
          name={`identificationIds.${index}.identificationId`}
          render={({ field }) => (
            <FormItem>
              <LabelTooltip
                label={tProjects(
                  'otherIdentificationNumbers.identificationId.label',
                )}
              />
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FieldInfo>
                <FormMessage />
              </FieldInfo>
            </FormItem>
          )}
        />
      </div>
      <div className="min-w-[200px] flex-1">
        <ComboboxField
          clearErrorsOnChange={false}
          fieldLabel={tProjects('otherIdentificationNumbers.type.label')}
          fieldName={`identificationIds.${index}.type`}
          options={selectsData?.numberType?.data ?? []}
          placeholder={tCommon('select')}
        />
      </div>
    </div>
  );
};
