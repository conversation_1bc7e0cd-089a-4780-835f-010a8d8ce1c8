import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { unitFormSections } from '@/constants/bottin/unit';
import { getQueryClientOptions } from '@/constants/query-client';
import { getGenericByIdOptions } from '@/hooks/bottin/generic-list.options';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';
import EditionUnitPage from './edit-unit-page';

type EditUnitPageParams = PageDetailsParams;
export default async function EditUnitPage(props: EditUnitPageParams) {
  const params = await props.params;

  const { id, locale } = params;

  const formSections = await getFormSections({
    resourceName: 'units',
    sections: unitFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  const t0 = performance.now();

  const controlledLists: ControlledListKey[] = [
    'organisation',
    'unitType',
    'person',
  ];

  const [unit] = await Promise.all([
    queryClient.fetchQuery(
      getGenericByIdOptions({ controlledListKey: 'unit', id, view: 'edit' }),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "organization", "unitType", "person" and "unit" took ${t1 - t0} milliseconds.`,
  );
  if (!unit) {
    return notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionUnitPage formSections={formSections} id={id} locale={locale} />
    </HydrationBoundary>
  );
}
