import type {
  DbInfrastructureSchema,
  InfrastructureInputSchema,
  InfrastructureListSchema,
  InfrastructureSchema,
} from '../schemas/infrastructures.schema';

import type * as Schema from 'effect/Schema';

export type DbInfrastructure = Schema.Schema.Type<
  typeof DbInfrastructureSchema
>;
export type Infrastructure = Schema.Schema.Type<typeof InfrastructureSchema>;
export type InfrastructureInput = Schema.Schema.Type<
  typeof InfrastructureInputSchema
>;
export type InfrastructureList = Schema.Schema.Type<
  typeof InfrastructureListSchema
>;

// Smarter include mask for infra relations
export type InfrastructureInclude = Partial<
  Record<'type' | 'status' | 'unit' | 'address', boolean>
>;
