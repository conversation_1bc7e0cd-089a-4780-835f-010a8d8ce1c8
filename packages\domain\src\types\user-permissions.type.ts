import type * as Schema from 'effect/Schema';
import type {
  DbUserDetailWithRolesSchema,
  ResourceTypeSchema,
  UserDetailWithRolesSchemas,
} from '../schemas';
import type { PermissionAction } from './permissions.type';

export type ResourceType = Schema.Schema.Type<typeof ResourceTypeSchema>;

// Export the types using the actual Schema.decodeSync to ensure proper type resolution
export type DbUserDetailWithRoles = Schema.Schema.Type<
  typeof DbUserDetailWithRolesSchema
>;

export type UserDetailWithRoles = Schema.Schema.Type<
  typeof UserDetailWithRolesSchemas
>;

export type AccessTree = Record<ResourceType, string[]> & {
  globalPermissions: { domain: ResourceType; action: PermissionAction }[];
};
