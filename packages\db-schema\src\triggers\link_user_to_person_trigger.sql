-- Function to link a new user to an existing person with the same email
CREATE OR REPLACE FUNCTION link_user_to_person()
RETURNS TRIGGER AS $$
DECLARE
  matching_person_id TEXT;
BEGIN
  -- Find a person with an email matching the new user's email
  SELECT p.id INTO matching_person_id
  FROM people p
  JOIN people_associated_emails e ON p.id = e.person_id
  WHERE e.address = NEW.email
  LIMIT 1;

  -- If a matching person is found, update their userId field
  IF matching_person_id IS NOT NULL THEN
    UPDATE people
    SET user_id = NEW.id,
        updated_at = NOW()
    WHERE id = matching_person_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS link_user_to_person_trigger ON users;
CREATE TRIGGER link_user_to_person_trigger
AFTER INSERT ON users
FOR EACH ROW
EXECUTE FUNCTION link_user_to_person();