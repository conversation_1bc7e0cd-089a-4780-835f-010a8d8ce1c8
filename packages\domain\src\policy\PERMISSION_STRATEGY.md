# Permission Strategy Documentation

This document outlines the permission strategy used in the application, including best practices and implementation decisions.

## Permission System Overview

Our application uses a sophisticated role-based access control (RBAC) system with attribute-based access control (ABAC) elements and hierarchical resource access:

- **Permissions**: Follow the format `domain:action` (e.g., `equipment:read`, `infrastructure:manage`)
- **Roles**: Can inherit permissions from other roles and permission groups
- **Context-aware permissions**: Allow for resource-specific access control with hierarchical inheritance
- **Hierarchical access**: Institution → Unit → Infrastructure → Equipment inheritance
- **Automatic role assignment**: Users get the "User" role by default upon signup
- **Centralized permission logic**: The policy system and UserPermissionsServiceLive handle all permission operations

## Core Components

### 1. UserPermissionsServiceLive
The central service for managing user permissions, located at `apps/api/src/infrastructure/services/user-permissions.service.ts`.

**Key Responsibilities:**
- User role management with context (institution, unit, infrastructure, none)
- Permission checking with hierarchical access validation
- Integration with AccessTreeServiceLive for resource access caching
- Role assignment and removal operations

### 2. AccessTreeServiceLive
Handles hierarchical access tree building and caching for performance optimization.

### 3. Database Schema
- **users**: User accounts
- **roles**: Available roles in the system
- **permissions**: Individual permissions (domain:action pairs)
- **userRoles**: Junction table linking users to roles with context
- **rolePermissions**: Direct role-to-permission assignments
- **permissionGroups**: Grouped permissions for easier management
- **rolePermissionGroups**: Role-to-permission-group assignments
- **roleInheritance**: Role hierarchy definitions

## Permission Checking Strategy

### Backend Permission Checks

We implement a multi-layered approach to permission checking:

1. **Middleware Level**
   - Coarse-grained access control
   - Applied to entire route groups requiring specific roles
   - Prevents unauthorized requests early in the request lifecycle

2. **Route Handler Level**
   - Resource-specific permission checks
   - Used when permissions depend on the resource being accessed
   - Can access route parameters and query strings
   - Primary location for most permission checks

3. **Service Level (UserPermissionsServiceLive)**
   - Business logic permission checks
   - Hierarchical access validation through AccessTreeServiceLive
   - Context-aware permission evaluation
   - Used for complex permission rules involving multiple resources
   - Has access to full business context

### Permission Checking Methods

#### 1. userHasPermission()
Checks if a user has a specific permission for a domain/action combination:

```typescript
const hasPermission = yield* userPermissionsService.userHasPermission({
  userId: 'user-id',
  domain: 'equipment',
  action: 'read',
  resourceId: 'equipment-id',
  resourceType: 'equipment',
});
```

#### 2. userHasAccessToResource()
Validates hierarchical access to resources using the access tree:

```typescript
const hasAccess = yield* userPermissionsService.userHasAccessToResource(
  'user-id',
  'infrastructure',
  'infrastructure-id'
);
```

#### 3. getUserPermissionsForResource()
Returns all permissions a user has for a specific resource:

```typescript
const permissions = yield* userPermissionsService.getUserPermissionsForResource(
  'user-id',
  'equipment',
  'equipment-id'
);
```

### Frontend Permission Checks

- Session contains user's permissions loaded at login time
- UI components use permissions to determine visibility and enabled actions
- `PermissionGate` component controls rendering based on permissions

## Role Management

### User Role Assignment

Users can be assigned roles in different contexts:

#### 1. Generic Roles (No Context)
```typescript
yield* userPermissionsService.assignRoleToUser({
  userId: 'user-id',
  roleId: 'equipment-editor-role-id',
  domain: null, 
  domainId: null,
});
```

#### 2. Context-Specific Roles
```typescript
// Assign role for a specific institution
yield* userPermissionsService.assignRoleToUser({
  userId: 'user-id',
  roleId: 'manager-role-id',
  domain: 'institution',
  domainId: 'institution-id',
  grantedBy: 'admin-user-id',
});

// Assign role for a specific unit
yield* userPermissionsService.assignRoleToUser({
  userId: 'user-id',
  roleId: 'technician-role-id',
  domain: 'unit',
  domainId: 'unit-id',
  grantedBy: 'admin-user-id',
});
```

### Role Removal
```typescript
yield* userPermissionsService.removeRoleFromUser({
  userId: 'user-id',
  roleId: 'role-id',
  domain: 'institution',
  domainId: 'institution-id',
});
```

### Hierarchical Access Inheritance

When a user is assigned a role at a higher level, they automatically gain access to all resources below:

- **Institution role** → Access to all units, infrastructures, and equipment within that institution
- **Unit role** → Access to all infrastructures and equipment within that unit
- **Infrastructure role** → Access to all equipment within that infrastructure
- **Equipment role** → Access to that specific equipment only

## Permission Caching

- **Access Tree Caching**: User access trees are cached for 30 minutes (SESSION_EXPIRES_IN_SECONDS)
- **Cache Invalidation**: Automatic invalidation when roles change or permission updates occur
- **Performance Optimization**: Implemented using the Effect-based cache system in AccessTreeServiceLive
- **Cache Key Strategy**: User ID-based caching for efficient lookups

## Implementation Guidelines

### 1. Server-Side Permission Verification
Always verify permissions server-side before any protected action using UserPermissionsServiceLive:

```typescript
// In route handlers or services
const program = Effect.gen(function* () {
  const userPermissionsService = yield* UserPermissionsServiceLive;

  // Check permission before proceeding
  const hasPermission = yield* userPermissionsService.userHasPermission({
    userId,
    domain: 'equipment',
    action: 'update',
    resourceId: equipmentId,
    resourceType: 'equipment',
  });

  if (!hasPermission) {
    return yield* Effect.fail(new PermissionDeniedError());
  }

  // Proceed with protected logic
});
```

### 2. Policy Module Integration
Use the `policy` module for declarative permission checks:

```typescript
withPolicy(permission('equipment:read'))(
  Effect.gen(function* (_) {
    // Protected logic here
  })
)
```

### 3. Complex Permission Scenarios
Compose policies for complex permission scenarios:

```typescript
withPolicy(all([
  permission('equipment:read'),
  permission('infrastructure:read')
]))(effect)
```

### 4. Frontend Permission Controls
For UI rendering decisions, use the permission hooks and components:

```tsx
<PermissionGate
  resource="equipment"
  operation="read"
  equipmentId={id}
>
  <ProtectedComponent />
</PermissionGate>
```

### 5. Role Management Best Practices

#### Assign Roles with Proper Context
```typescript
// For institution-wide access
yield* userPermissionsService.assignRoleToUser({
  userId,
  roleId: 'institution-manager',
  domain: 'institution',
  domainId: institutionId,
  grantedBy: currentUserId,
});

// For global access (use sparingly)
yield* userPermissionsService.assignRoleToUser({
  userId,
  roleId: 'system-admin',
  domain: null,
  domainId: null,
});
```

#### Check User Access Before Operations
```typescript
// Always verify access before showing resources
const hasAccess = yield* userPermissionsService.userHasAccessToResource(
  userId,
  'infrastructure',
  infrastructureId
);

if (!hasAccess) {
  return yield* Effect.fail(new ResourceNotFoundError());
}
```

## Service Architecture

### UserPermissionsServiceLive Dependencies
```typescript
dependencies: [
  UsersRepositoryLive.Default,      // User data access
  AccessTreeServiceLive.Default,    // Hierarchical access caching
  RolesServiceLive.Default,         // Role management
  Database.PgDatabase,              // Database transactions
]
```

### Service Separation
- **UsersServiceLive**: Handles user CRUD operations and profile management
- **UserPermissionsServiceLive**: Dedicated to permission and role management
- **AccessTreeServiceLive**: Manages hierarchical access tree building and caching
- **RolesServiceLive**: Manages role definitions and role-permission relationships

## Error Handling

### Permission-Related Errors
```typescript
// User not found
UserNotFoundError({ id: userId })

// Role not found
RoleNotFoundError({ id: roleId })

// Duplicate role assignment
UserRoleAlreadyExistsError({
  userId,
  roleId,
  domain,
  domainId
})

// Role assignment not found for removal
UserRoleNotFoundError({
  userId,
  roleId,
  domain,
  domainId
})
```

### Error Handling Best Practices
1. **Graceful Degradation**: When permission checks fail, provide meaningful error messages
2. **Security**: Don't expose sensitive information in error messages
3. **Logging**: Log permission failures for security monitoring
4. **User Experience**: Provide clear feedback when access is denied

## Security Considerations

### 1. Principle of Least Privilege
- Users start with minimal permissions (only "User" role)
- Grant additional permissions only as needed
- Use context-specific roles instead of global permissions when possible

### 2. Defense in Depth
- Multiple layers of permission checking (middleware, route, service)
- Server-side validation is mandatory, client-side is for UX only
- Database constraints prevent invalid role assignments

### 3. Audit Trail
- All role assignments include `grantedBy` field
- Timestamps track when permissions were granted
- Permission changes should be logged for compliance

### 4. Cache Security
- Access trees are user-specific and cannot be accessed by other users
- Cache invalidation prevents stale permissions after role changes
- Session-based cache expiration aligns with authentication

## Audit and Monitoring

- **Permission Changes**: Logged with the user who made the change (`grantedBy` field)
- **Role Assignments**: Track who granted the role and when (`createdAt`, `grantedBy`)
- **Permission Denials**: Should be logged for security monitoring and compliance
- **Access Patterns**: Monitor unusual access patterns for security threats
- **Performance Metrics**: Track permission check performance and cache hit rates

## Testing Strategy

### Unit Testing UserPermissionsServiceLive
```typescript
describe('UserPermissionsServiceLive', () => {
  it('should assign role to user with context', async () => {
    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;

      return yield* userPermissionsService.assignRoleToUser({
        userId: 'test-user-id',
        roleId: 'test-role-id',
        domain: 'institution',
        domainId: 'test-institution-id',
        grantedBy: 'admin-user-id',
      });
    });

    const result = await UserPermissionsRuntime.runPromise(program);
    expect(result).toBeDefined();
  });

  it('should check user permissions correctly', async () => {
    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;

      return yield* userPermissionsService.userHasPermission({
        userId: 'test-user-id',
        domain: 'equipment',
        action: 'read',
        resourceId: 'test-equipment-id',
        resourceType: 'equipment',
      });
    });

    const hasPermission = await UserPermissionsRuntime.runPromise(program);
    expect(typeof hasPermission).toBe('boolean');
  });
});
```

### Integration Testing
- Test permission inheritance through the hierarchy
- Verify cache invalidation on role changes
- Test error scenarios (user not found, role not found, etc.)
- Validate transaction rollback on failures

## Common Usage Patterns

### 1. Equipment Management Scenario
```typescript
// User assigned as equipment manager for a specific infrastructure
yield* userPermissionsService.assignRoleToUser({
  userId: 'technician-id',
  roleId: 'equipment-manager-role',
  domain: 'infrastructure',
  domainId: 'lab-infrastructure-id',
  grantedBy: 'supervisor-id',
});

// Check if user can modify equipment in that infrastructure
const canModify = yield* userPermissionsService.userHasPermission({
  userId: 'technician-id',
  domain: 'equipment',
  action: 'update',
  resourceId: 'microscope-id',
  resourceType: 'equipment',
});
```

### 2. Institution Administrator Scenario
```typescript
// Assign institution-wide admin role
yield* userPermissionsService.assignRoleToUser({
  userId: 'admin-id',
  roleId: 'institution-admin-role',
  domain: 'institution',
  domainId: 'university-id',
  grantedBy: 'super-admin-id',
});

// Admin automatically has access to all units, infrastructures, and equipment
const accessTree = yield* userPermissionsService.getUserAccessTree('admin-id');
// accessTree.institutions includes 'university-id'
// accessTree.units includes all units under the university
// accessTree.infrastructures includes all infrastructures under those units
// accessTree.equipments includes all equipment under those infrastructures
```

### 3. Multi-Context User Scenario
```typescript
// User can have roles in multiple contexts
yield* userPermissionsService.assignRoleToUser({
  userId: 'researcher-id',
  roleId: 'researcher-role',
  domain: 'unit',
  domainId: 'chemistry-unit-id',
  grantedBy: 'unit-head-id',
});

yield* userPermissionsService.assignRoleToUser({
  userId: 'researcher-id',
  roleId: 'equipment-specialist-role',
  domain: 'infrastructure',
  domainId: 'spectroscopy-lab-id',
  grantedBy: 'lab-manager-id',
});

// User now has different permissions in different contexts
const userRoles = yield* userPermissionsService.getUserRolesWithContext('researcher-id');
// Returns array with both role assignments and their contexts
```

## Migration and Deployment

### Database Migrations
- Ensure all permission-related tables are properly migrated
- Seed default roles and permissions
- Create the default "User" role that's assigned automatically

### Performance Considerations
- Monitor cache hit rates for access trees
- Consider database indexing on frequently queried fields
- Implement pagination for large role/permission lists

### Rollback Strategy
- Maintain backup of role assignments before major changes
- Test permission changes in staging environment
- Have rollback scripts for critical permission modifications