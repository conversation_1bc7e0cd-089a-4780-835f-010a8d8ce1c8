import { DBSchema } from '@rie/db-schema';
import type { VendorInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class VendorsRepositoryLive extends Effect.Service<VendorsRepositoryLive>()(
  'VendorsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      const findAllVendors = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.vendors.findMany({
            columns: {
              id: true,
              isActive: true,
              startDate: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  website: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const findVendorById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.vendors.findFirst({
            where: eq(DBSchema.vendors.id, id),
            columns: {
              id: true,
              isActive: true,
              startDate: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  website: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const createVendor = (params: { vendor: VendorInput }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Create the vendor
            const [createdVendor] = yield* tx((client) =>
              client.insert(DBSchema.vendors).values(params.vendor).returning({
                id: DBSchema.vendors.id,
                isActive: DBSchema.vendors.isActive,
                startDate: DBSchema.vendors.startDate,
                endDate: DBSchema.vendors.endDate,
                createdAt: DBSchema.vendors.createdAt,
                updatedAt: DBSchema.vendors.updatedAt,
                modifiedBy: DBSchema.vendors.modifiedBy,
              }),
            );

            if (!createdVendor) {
              return yield* Effect.fail(new Error('Failed to create vendor'));
            }

            // Create the translations
            const translationsToInsert = params.vendor.translations.map(
              (translation) => ({
                dataId: createdVendor.id,
                ...translation,
              }),
            );

            const createdTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.vendorsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.vendorsI18N.id,
                  locale: DBSchema.vendorsI18N.locale,
                  name: DBSchema.vendorsI18N.name,
                  website: DBSchema.vendorsI18N.website,
                  description: DBSchema.vendorsI18N.description,
                  otherNames: DBSchema.vendorsI18N.otherNames,
                }),
            );

            return {
              ...createdVendor,
              translations: createdTranslations,
            };
          });
        });
      };

      const updateVendor = (params: {
        vendorId: string;
        vendor: VendorInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            const [vendor] = yield* tx((client) =>
              client
                .update(DBSchema.vendors)
                .set({
                  ...params.vendor,
                })
                .where(eq(DBSchema.vendors.id, params.vendorId))
                .returning({
                  id: DBSchema.vendors.id,
                  isActive: DBSchema.vendors.isActive,
                  startDate: DBSchema.vendors.startDate,
                  endDate: DBSchema.vendors.endDate,
                  createdAt: DBSchema.vendors.createdAt,
                  updatedAt: DBSchema.vendors.updatedAt,
                  modifiedBy: DBSchema.vendors.modifiedBy,
                }),
            );

            // Remove existing translations
            yield* tx((client) =>
              client
                .delete(DBSchema.vendorsI18N)
                .where(eq(DBSchema.vendorsI18N.dataId, params.vendorId)),
            );

            // Insert new translations
            const translationsToInsert = params.vendor.translations.map(
              (translation) => ({
                dataId: params.vendorId,
                ...translation,
              }),
            );

            const updatedTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.vendorsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.vendorsI18N.id,
                  locale: DBSchema.vendorsI18N.locale,
                  name: DBSchema.vendorsI18N.name,
                  website: DBSchema.vendorsI18N.website,
                  description: DBSchema.vendorsI18N.description,
                  otherNames: DBSchema.vendorsI18N.otherNames,
                }),
            );

            // Return the vendor with its translations directly
            return {
              ...vendor,
              translations: updatedTranslations,
            };
          });
        });
      };

      const deleteVendor = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.vendors)
            .where(eq(DBSchema.vendors.id, id))
            .returning({ id: DBSchema.vendors.id }),
        );
      });

      return {
        findAllVendors,
        findVendorById,
        createVendor,
        updateVendor,
        deleteVendor,
      } as const;
    }),
  },
) {}
