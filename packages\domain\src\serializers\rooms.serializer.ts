import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  DbRoomSchema,
  RoomEditSchema,
  RoomInputSchema,
  RoomListSchema,
  RoomSchema,
  RoomSelectSchema,
} from '../schemas';
import type {
  CollectionViewType,
  ResourceViewType,
  Room,
  RoomList,
  RoomSelect,
} from '../types';

// Schema transformer for converting database room to list view
export const DbRoomToRoomList = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.String,
    number: Schema.String,
    area: Schema.NullishOr(Schema.Number),
    floorLoad: Schema.NullishOr(Schema.Number),
    buildingId: Schema.NullishOr(Schema.String),
    createdAt: Schema.String,
    updatedAt: Schema.String,
    modifiedBy: Schema.NullishOr(Schema.String),
    building: Schema.NullishOr(
      Schema.Struct({
        id: Schema.String,
        translations: Schema.Array(
          Schema.Struct({
            id: Schema.String,
            locale: Schema.String,
            name: Schema.NullishOr(Schema.String),
          }),
        ),
      }),
    ),
  }),
  RoomListSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get building name from building relation
          let buildingName = null;
          if (raw.building?.translations) {
            const buildingTranslations = raw.building.translations;
            buildingName =
              buildingTranslations.find((t) => t.locale === 'fr')?.name ||
              buildingTranslations.find((t) => t.locale === 'en')?.name ||
              buildingTranslations?.[0]?.name ||
              null;
          }

          // TODO: Get jurisdiction from institution/unit hierarchy
          const jurisdiction = null;

          return {
            id: String(raw.id || ''),
            numero: String(raw.number || ''),
            building: buildingName,
            jurisdiction,
            lastUpdatedAt: String(raw.updatedAt || ''),
            area: typeof raw.area === 'number' ? raw.area : null,
            floorLoad: typeof raw.floorLoad === 'number' ? raw.floorLoad : null,
            buildingId: raw.buildingId ? String(raw.buildingId) : null,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse room for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database room to select view
export const DbRoomToRoomSelect = Schema.transformOrFail(
  RoomSchema,
  RoomSelectSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            value: raw.id,
            label: raw.number,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse room for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

export const DbRoomToRoomEdit = Schema.transformOrFail(
  DbRoomSchema,
  RoomEditSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get building name from building relation
          let buildingLabel = null;
          if (raw.building?.translations) {
            const buildingTranslations = raw.building.translations;
            buildingLabel =
              buildingTranslations.find((t) => t.locale === 'fr')?.name ||
              buildingTranslations.find((t) => t.locale === 'en')?.name ||
              buildingTranslations?.[0]?.name ||
              null;
          }

          return {
            id: raw.id,
            alias: '', // Room edit doesn't have alias, use empty string
            jurisdiction: { value: null, label: null }, // TODO: Get jurisdiction
            building: raw.buildingId
              ? {
                value: raw.buildingId,
                label: buildingLabel || raw.buildingId,
              }
              : { value: null, label: null },
            categories: [], // TODO: Get room categories
            area: raw.area || undefined,
            capacity: raw.floorLoad || undefined,
            roomNumber: raw.number,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse room for edit format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of Room to RoomSelect[]
export const dbRoomsToRoomSelect = (dbRooms: Room[]): RoomSelect[] => {
  return dbRooms.map((dbRoom) => {
    return {
      value: dbRoom.id,
      label: dbRoom.number,
    };
  });
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbRoomsToRooms = (
  dbRooms: Array<{
    id: string;
    number: string;
    area: number | null;
    floorLoad: number | null;
    buildingId: string | null;
    createdAt: string;
    updatedAt: string;
    modifiedBy: string | null;
    building?: {
      id: string;
      translations: Array<{
        id: string;
        locale: string;
        name: string | null;
      }>;
    } | null;
  }>,
  view: CollectionViewType,
): RoomList[] | RoomSelect[] => {
  return view === 'select'
    ? dbRooms.map((room) => Schema.decodeUnknownSync(DbRoomToRoomSelect)(room))
    : dbRooms.map((room) => Schema.decodeUnknownSync(DbRoomToRoomList)(room));
};

// New serializer function for Room with view parameter
export const dbRoomToRoom = (
  dbRoom: {
    id: string;
    number: string;
    area: number | null;
    floorLoad: number | null;
    buildingId: string | null;
    createdAt: string;
    updatedAt: string;
    modifiedBy: string | null;
    building?: {
      id: string;
      translations: Array<{
        id: string;
        locale: string;
        name: string | null;
      }>;
    } | null;
  },
  view: ResourceViewType,
) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbRoomToRoomEdit)(dbRoom)
    : Schema.decodeUnknownSync(DbRoomToRoomList)(dbRoom);
};

// Transform client RoomFormSchema payload into RoomInputSchema (DB input)
export const RoomFormToDBInput = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.optional(Schema.String),
    alias: Schema.String,
    jurisdiction: Schema.Struct({
      label: Schema.NullishOr(Schema.String),
      value: Schema.NullishOr(Schema.String),
    }),
    building: Schema.Struct({
      label: Schema.NullishOr(Schema.String),
      value: Schema.NullishOr(Schema.String),
    }),
    categories: Schema.Array(Schema.String),
    area: Schema.optional(Schema.Number),
    capacity: Schema.optional(Schema.Number),
    roomNumber: Schema.String,
  }),
  RoomInputSchema,
  {
    strict: false,
    decode: (val, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            number: val.roomNumber,
            area: val.area || null,
            floorLoad: val.capacity || null,
            buildingId: val.building.value || null,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            val,
            error instanceof Error
              ? error.message
              : 'Failed to convert room form to DB input',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);
