import { FundingProjectNotFoundError } from '@rie/domain/errors';
import type { FundingProjectInputSchema } from '@rie/domain/schemas';
import { FundingProjectsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type FundingProjectInput = Schema.Schema.Type<typeof FundingProjectInputSchema>;

export class FundingProjectsServiceLive extends Effect.Service<FundingProjectsServiceLive>()(
  'FundingProjectsServiceLive',
  {
    dependencies: [FundingProjectsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const fundingProjectsRepository = yield* FundingProjectsRepositoryLive;

      const getAllFundingProjects = () => {
        return Effect.gen(function* () {
          return yield* fundingProjectsRepository.findAllFundingProjects();
        });
      };

      const getFundingProjectById = (id: string) => {
        return Effect.gen(function* () {
          const project =
            yield* fundingProjectsRepository.findFundingProjectById(id);
          if (!project) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }
          return project;
        });
      };

      const createFundingProject = (project: FundingProjectInput) => {
        return Effect.gen(function* () {
          return yield* fundingProjectsRepository.createFundingProject({
            project,
          });
        });
      };

      const updateFundingProject = ({
        id,
        project,
      }: { id: string; project: FundingProjectInput }) => {
        return Effect.gen(function* () {
          const existingProject =
            yield* fundingProjectsRepository.findFundingProjectById(id);
          if (!existingProject) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }

          return yield* fundingProjectsRepository.updateFundingProject({
            projectId: id,
            project,
          });
        });
      };

      const deleteFundingProject = (id: string) => {
        return Effect.gen(function* () {
          const existingProject =
            yield* fundingProjectsRepository.findFundingProjectById(id);
          if (!existingProject) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }
          const result =
            yield* fundingProjectsRepository.deleteFundingProject(id);
          return result.length > 0;
        });
      };

      return {
        getAllFundingProjects,
        getFundingProjectById,
        createFundingProject,
        updateFundingProject,
        deleteFundingProject,
      } as const;
    }),
  },
) {}
