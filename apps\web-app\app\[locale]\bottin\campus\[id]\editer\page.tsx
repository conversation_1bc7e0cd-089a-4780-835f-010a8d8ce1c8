import EditionCampusPage from '@/app/[locale]/bottin/campus/[id]/editer/edit-campus-page';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { campusFormSections } from '@/constants/bottin/campus';
import { getQueryClientOptions } from '@/constants/query-client';
import { getGenericByIdOptions } from '@/hooks/bottin/generic-list.options';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';

type EditCampusPageParams = PageDetailsParams;
export default async function EditCampusPage(props: EditCampusPageParams) {
  const params = await props.params;

  const { id, locale } = params;

  const formSections = await getFormSections({
    resourceName: 'directory',
    sections: campusFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const t0 = performance.now();

  const controlledLists: ControlledListKey[] = ['organisation'];

  const campus = await Promise.all([
    queryClient.fetchQuery(
      getGenericByIdOptions({ controlledListKey: 'campus', id, view: 'edit' }),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "organization" and "campus" took ${t1 - t0} milliseconds.`,
  );

  if (!campus) {
    return notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionCampusPage formSections={formSections} id={id} locale={locale} />
    </HydrationBoundary>
  );
}
