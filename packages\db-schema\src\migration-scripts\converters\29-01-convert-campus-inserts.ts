import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlCampus {
  id: number;
  uid: string | null;
  etablissement_id: number;
}

interface PostgresCampus {
  id: string;
  sad_id: string | null;
  institution_id: string | null;
}

export class CampusMigrationConverter extends BaseConverter {
  private campusMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlCampus[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        uid: this.parseNullableString(values.uid),
        etablissement_id: Number.parseInt(values.etablissement_id),
      },
    ];
  }

  private convertToPostgres(
    mysqlCampus: MySqlCampus,
    guidIdMappings: Record<string, string>,
    institutionIdMappings: Record<string, string>,
  ): PostgresCampus {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.campusMappings.push({
      mysqlId: mysqlCampus.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      sad_id: mysqlCampus.uid
        ? (guidIdMappings[mysqlCampus.uid] ?? null)
        : null,
      institution_id: institutionIdMappings[mysqlCampus.id.toString()] ?? null,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for campus table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'campus',
      );

      if (insertStatements.length === 0) {
        console.log('No campus INSERT statements found.');
        return;
      }

      // Load institution ID mappings
      const institutionIdMappings =
        await this.loadEntityIdMappings('etablissement');
      const guidIdMappings = await this.loadEntityIdMappings('guid');

      const allPostgresCampuses: PostgresCampus[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlCampuses = this.parseInsertStatement(statement);
        const postgresCampuses = mysqlCampuses.map((campus) =>
          this.convertToPostgres(campus, guidIdMappings, institutionIdMappings),
        );
        allPostgresCampuses.push(...postgresCampuses);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresCampuses,
          'campuses',
          'Campus Inserts',
          ['id', 'sad_id', 'institution_id'],
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.campusMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'campus', postgres: 'campuses' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresCampuses.length} campuses records`);
    } catch (error) {
      console.error('Error converting campus data:', error);
      throw error;
    }
  }
}
