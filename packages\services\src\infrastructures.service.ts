import { InfrastructureNotFoundError } from '@rie/domain/errors';
import type { InfrastructureInputSchema } from '@rie/domain/schemas';
import type { InfrastructureInclude } from '@rie/domain/types';
import { InfrastructuresRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type InfrastructureInput = Schema.Schema.Type<typeof InfrastructureInputSchema>;

export class InfrastructuresServiceLive extends Effect.Service<InfrastructuresServiceLive>()(
  'InfrastructuresServiceLive',
  {
    dependencies: [InfrastructuresRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllInfrastructures = (includes?: InfrastructureInclude) => {
        return Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          return yield* repo.findAllInfrastructures();
        });
      };

      const countInfrastructures = () =>
        Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          return yield* repo.countAllInfrastructures();
        });

      const getInfrastructureById = (
        id: string,
        includes?: InfrastructureInclude,
      ) => {
        return Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          const infrastructure = yield* repo.findInfrastructureById(id);
          if (!infrastructure) {
            return yield* Effect.fail(new InfrastructureNotFoundError({ id }));
          }
          return infrastructure;
        });
      };

      const createInfrastructure = (infrastructure: InfrastructureInput) =>
        Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          return yield* repo.createInfrastructure({ infrastructure });
        });

      const updateInfrastructure = ({
        id,
        infrastructure,
      }: { infrastructure: InfrastructureInput; id: string }) => {
        return Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          const existingInfrastructure = yield* repo.findInfrastructureById(id);
          if (!existingInfrastructure) {
            return yield* Effect.fail(new InfrastructureNotFoundError({ id }));
          }
          const updatedInfrastructure = yield* repo.updateInfrastructure({
            infrastructureId: id,
            infrastructure,
          });
          return updatedInfrastructure;
        });
      };

      const deleteInfrastructure = (id: string) => {
        return Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          const existingInfrastructure = yield* repo.findInfrastructureById(id);
          if (!existingInfrastructure) {
            return yield* Effect.fail(new InfrastructureNotFoundError({ id }));
          }
          const result = yield* repo.deleteInfrastructure(id);
          return result.length > 0;
        });
      };

      return {
        getAllInfrastructures,
        countInfrastructures,
        getInfrastructureById,
        createInfrastructure,
        updateInfrastructure,
        deleteInfrastructure,
      } as const;
    }),
  },
) {}
