import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlEquipmentI18n {
  id: number;
  data_id: number;
  language_id: string;
  nom: string | null;
  description: string | null;
  utilisation: string | null;
  disposition: string | null;
  risque: string | null;
  commentaire: string | null;
}

interface PostgresEquipmentI18n {
  id: string;
  data_id: string;
  locale: string;
  name: string | null;
  description: string | null;
  usage_context: string | null;
  specification: string | null;
  risk: string | null;
  comment: string | null;
}

export class EquipmentI18nMigrationConverter extends BaseConverter {
  private equipmentI18nMappings: Mapping[] = [];

  private parseEquipmentI18NInsertStatement(
    sqlStatement: string,
  ): MySqlEquipmentI18n[] {
    // Special case for problematic entries with 'empilement simple'
    if (
      sqlStatement.includes('empilement simple') ||
      sqlStatement.includes('HPLC')
    ) {
      try {
        // Extract the values directly from the SQL statement
        const regex = /VALUES\s*\((\d+),\s*(\d+),\s*'([^']*)',\s*'([^']*)'\)/i;
        const match = sqlStatement.match(regex);

        if (match) {
          const [, id, data_id, language_id, nom] = match;
          if (typeof id !== 'string') {
            throw new Error('Invalid id');
          }

          if (typeof data_id !== 'string') {
            throw new Error('Invalid data_id');
          }

          if (typeof language_id !== 'string') {
            throw new Error('Invalid language_id');
          }

          if (typeof nom !== 'string') {
            throw new Error('Invalid nom');
          }

          return [
            {
              id: Number.parseInt(id),
              data_id: Number.parseInt(data_id),
              language_id: language_id,
              nom: nom,
              description: null,
              utilisation: null,
              disposition: null,
              risque: null,
              commentaire: null,
            },
          ];
        }
      } catch (error) {
        console.error('Error parsing special case:', error);
      }
    }

    const values = this.extractValuesFromInsertStatement(sqlStatement);

    // Check if values are valid
    if (!values.id || !values.data_id) {
      console.error('sqlStatement', sqlStatement);
      console.error('Invalid values:', values);
      return [];
    }

    try {
      // Fix the nom field - it contains all the other fields
      let nom = values.nom;

      // If nom contains a comma and NULL, it's likely that it contains other fields
      if (nom?.includes("', NULL")) {
        // Extract just the name part
        nom = nom.split("', NULL")[0];
      }

      return [
        {
          id: Number.parseInt(values.id),
          data_id: Number.parseInt(values.data_id),
          language_id: values.language_id,
          nom: nom,
          description: values.description,
          utilisation: values.utilisation,
          disposition: values.disposition,
          risque: values.risque,
          commentaire: values.commentaire,
        },
      ];
    } catch (error) {
      console.error('Error parsing values:', values, error);
      return [];
    }
  }

  private convertToPostgres(
    mysqlRecord: MySqlEquipmentI18n,
    equipmentIdMappings: Record<string, string>,
  ): PostgresEquipmentI18n {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the equipment
    const newEquipmentId = equipmentIdMappings[mysqlRecord.data_id.toString()];
    if (!newEquipmentId) {
      throw new Error(
        `No mapping found for equipment_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.equipmentI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newEquipmentId,
      locale: mysqlRecord.language_id,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
      specification: mysqlRecord.disposition,
      usage_context: mysqlRecord.utilisation,
      risk: mysqlRecord.risque,
      comment: mysqlRecord.commentaire,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for equipement_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'equipement_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No equipement_trad INSERT statements found.');
        return;
      }

      // Load equipment ID mappings
      const equipmentIdMappings = await this.loadEntityIdMappings('equipement');

      const allPostgresRecords: PostgresEquipmentI18n[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        // Parse the MySQL records from the statement
        const mysqlRecords = this.parseEquipmentI18NInsertStatement(statement);

        // Skip empty records
        if (mysqlRecords.length === 0) {
          continue;
        }

        // Convert each record to PostgreSQL format
        const postgresRecords = [];
        for (const record of mysqlRecords) {
          try {
            // Check if we have a mapping for this equipment ID
            if (!equipmentIdMappings[record.data_id.toString()]) {
              console.log(
                `No mapping found for equipment_id: ${record.data_id}, skipping record`,
              );
              continue;
            }

            const postgresRecord = this.convertToPostgres(
              record,
              equipmentIdMappings,
            );
            postgresRecords.push(postgresRecord);
          } catch (error) {
            console.error('Error converting record:', record, error);
          }
        }

        allPostgresRecords.push(...postgresRecords);
      }

      // Add the actual INSERT statement with all columns
      const columnNames = [
        'id',
        'data_id',
        'locale',
        'name',
        'description',
        'specification',
        'usage_context',
        'risk',
        'comment',
      ];

      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresRecords,
          'equipments_i18n',
          'Equipment I18n Inserts',
          columnNames,
        );

      // console.log('postgresInsertsWithMappings', postgresInsertsWithMappings);

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.equipmentI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'equipement_trad', postgres: 'equipments_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} equipments_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
