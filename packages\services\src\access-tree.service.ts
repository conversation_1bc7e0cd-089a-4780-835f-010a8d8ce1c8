import { ADMIN_ROLE, SESSION_EXPIRES_IN_SECONDS } from '@rie/constants';
import * as Cache from '@rie/domain/cache';
import type {
  AccessTree,
  PermissionAction,
  ResourceType,
} from '@rie/domain/types';
import {
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import * as Duration from 'effect/Duration';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const makeService = () =>
  Effect.gen(function* () {
    // Create a cache for user access trees with 30 minute TTL
    const accessTreeCache = yield* Cache.make<string, AccessTree>({
      capacity: 1000,
      timeToLive: Duration.seconds(SESSION_EXPIRES_IN_SECONDS),
    });

    const basePermissions = {
      address: [],
      applicationSector: [],
      building: [],
      campus: [],
      equipment: [],
      excellenceHub: [],
      fundingProject: [],
      infrastructure: [],
      innovationLab: [],
      institution: [],
      media: [],
      people: [],
      researchField: [],
      room: [],
      serviceContract: [],
      serviceOffer: [],
      supplier: [],
      technique: [],
      unit: [],
      user: [],
      userRole: [],
      vendor: [],
      visibility: [],
      documentationCategory: [],
      equipmentCategory: [],
      equipmentStatus: [],
      equipmentType: [],
      fundingProjectType: [],
      fundingProjectIdentifierType: [],
      infrastructureStatus: [],
      infrastructureType: [],
      institutionType: [],
      mediaType: [],
      peopleRoleType: [],
      roomCategory: [],
      unitType: [],
    };

    // Build the complete access tree for a user
    const buildUserAccessTree = (userId: string) => {
      return Effect.gen(function* () {
        const usersRepository = yield* UsersRepositoryLive;
        const unitsRepository = yield* UnitsRepositoryLive;
        const infrastructuresRepository = yield* InfrastructuresRepositoryLive;
        const equipmentsRepository = yield* EquipmentsRepositoryLive;

        // Try to get from cache first
        const cachedTree = yield* accessTreeCache.get(userId);

        if (cachedTree._tag === 'Some') {
          return cachedTree.value;
        }

        // If not in cache, build the tree
        const user = yield* usersRepository.findUserById(userId);

        const ids = {
          institutions: new Set<string>(),
          units: new Set<string>(),
          infrastructures: new Set<string>(),
          equipments: new Set<string>(),
        };

        // Collect all permissions from user roles
        const userPermissions = new Set<{
          domain: ResourceType;
          action: PermissionAction;
        }>();

        // Only provide full permissions if the user isn't admin, if so we simply rely on the role
        if (user?.userRoles.some((ur) => ur.role.name === ADMIN_ROLE)) {
          return {
            globalPermissions: [],
            ...basePermissions,
          } satisfies AccessTree;
        }
        for (const userRole of user?.userRoles ?? []) {
          // Add permissions from this role
          // Handle both direct permissions array and nested rolePermissions
          // Direct permissions array (from transformed response)
          for (const permission of userRole.role.rolePermissionGroups) {
            for (const groupPermission of permission.group.permissions) {
              userPermissions.add({
                domain: groupPermission.permission.domain,
                action: groupPermission.permission.action,
              });
            }
          }

          if (userRole.resourceId) {
            if (userRole.resourceType === 'institution') {
              ids.institutions.add(userRole.resourceId);

              const allRelatedEntities =
                yield* unitsRepository.findUnitsWithRelatedEntitiesByInstitutionId(
                  userRole.resourceId,
                );

              for (const entity of allRelatedEntities) {
                ids.units.add(entity.unit.id);

                for (const infrastructure of entity.unit.infrastructures ||
                  []) {
                  ids.infrastructures.add(infrastructure.id);

                  const equipments =
                    yield* equipmentsRepository.findEquipmentsByInfrastructureId(
                      infrastructure.id,
                    );
                  for (const equipment of equipments ?? []) {
                    ids.equipments.add(equipment.id);
                  }
                }
              }
            } else if (userRole.resourceType === 'unit') {
              ids.units.add(userRole.resourceId);

              const infrastructuresWithEquipments =
                yield* infrastructuresRepository.findInfrastructuresWithRelatedEntitiesByUnitId(
                  userRole.resourceId,
                );

              for (const infrastructure of infrastructuresWithEquipments) {
                ids.infrastructures.add(infrastructure.id);

                const equipments =
                  yield* equipmentsRepository.findEquipmentsByInfrastructureId(
                    infrastructure.id,
                  );
                for (const equipment of equipments ?? []) {
                  ids.equipments.add(equipment.id);
                }
              }
            } else if (userRole.resourceType === 'infrastructure') {
              ids.infrastructures.add(userRole.resourceId);

              const equipments =
                yield* equipmentsRepository.findEquipmentsByInfrastructureId(
                  userRole.resourceId,
                );

              for (const equipment of equipments ?? []) {
                ids.equipments.add(equipment.id);
              }
            }
          }
        }

        const result: AccessTree = {
          globalPermissions: Array.from(userPermissions),
          ...basePermissions,
          equipment: Array.from(ids.equipments),
          infrastructure: Array.from(ids.infrastructures),
          institution: Array.from(ids.institutions),
          unit: Array.from(ids.units),
        };

        yield* accessTreeCache.set(userId, result);

        return result;
      });
    };

    // Check if a user has access to a specific resource
    const userHasAccessTo = (
      userId: string,
      resourceType: ResourceType,
      resourceId: string,
    ) => {
      return Effect.gen(function* () {
        const accessTree = yield* buildUserAccessTree(userId);
        if (!accessTree[resourceType]) {
          return false;
        }

        return accessTree[resourceType].includes(resourceId);
      });
    };

    // Invalidate a user's access tree (call when roles change)
    const invalidateUserAccessTree = (userId: string) => {
      return accessTreeCache.invalidate(userId);
    };

    return {
      buildUserAccessTree,
      userHasAccessTo,
      invalidateUserAccessTree,
    } as const;
  });

type Shape = Effect.Effect.Success<ReturnType<typeof makeService>>;

export class AccessTreeServiceLive extends Effect.Tag('AccessTreeServiceLive')<
  AccessTreeServiceLive,
  Shape
>() {
  static readonly Default = Layer.scoped(this, makeService()).pipe(
    Layer.provide(UsersRepositoryLive.Default),
    Layer.provide(UnitsRepositoryLive.Default),
    Layer.provide(InfrastructuresRepositoryLive.Default),
    Layer.provide(EquipmentsRepositoryLive.Default),
  );
}
