import { BuildingNotFoundError } from '@rie/domain/errors';
import type { BuildingInputSchema } from '@rie/domain/schemas';
import { BuildingInputToDBInput } from '@rie/domain/serializers';
import type { DBBuildingInput } from '@rie/domain/types';
import { BuildingsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';

export class BuildingsServiceLive extends Effect.Service<BuildingsServiceLive>()(
  'BuildingsServiceLive',
  {
    dependencies: [BuildingsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllBuildings = () => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const all = yield* repo.findAllBuildings();
          const t1 = performance.now();
          console.log(`Call to getAllBuildings took ${t1 - t0} ms.`);
          return all;
        });
      };

      const getBuildingById = (id: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const building = yield* repo.findBuildingById(id);
          if (!building) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const t1 = performance.now();
          console.log(`Call to getBuildingById took ${t1 - t0} ms.`);
          return building;
        });
      };
      const createBuilding = ({
        building,
        modifiedBy,
      }: {
        building: Schema.Schema.Type<typeof BuildingInputSchema>;
        modifiedBy?: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const dbBuilding: DBBuildingInput = yield* Schema.decode(
            BuildingInputToDBInput,
          )(building);
          // Ajouter modifiedBy si fourni
          const buildingWithMetadata = modifiedBy
            ? { ...dbBuilding, modifiedBy }
            : dbBuilding;
          return yield* repo.createBuilding({ building: buildingWithMetadata });
        });

      const updateBuilding = ({
        id,
        building,
        modifiedBy,
      }: {
        building: Schema.Schema.Type<typeof BuildingInputSchema>;
        id: string;
        modifiedBy?: string;
      }) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existingBuilding = yield* repo.findBuildingById(id);
          if (!existingBuilding) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const dbBuilding: DBBuildingInput = yield* Schema.decode(
            BuildingInputToDBInput,
          )(building);
          // Ajouter modifiedBy si fourni
          const buildingWithMetadata = modifiedBy
            ? { ...dbBuilding, modifiedBy }
            : dbBuilding;
          const updatedBuilding = yield* repo.updateBuilding({
            buildingId: id,
            building: buildingWithMetadata,
          });
          const t1 = performance.now();
          console.log(`Call to updateBuilding took ${t1 - t0} ms.`);
          return updatedBuilding;
        });
      };

      const deleteBuilding = (id: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existingBuilding = yield* repo.findBuildingById(id);
          if (!existingBuilding) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const result = yield* repo.deleteBuilding(id);
          const t1 = performance.now();
          console.log(`Call to deleteBuilding took ${t1 - t0} ms.`);
          return result.length > 0;
        });
      };

      return {
        getAllBuildings,
        getBuildingById,
        createBuilding,
        updateBuilding,
        deleteBuilding,
      } as const;
    }),
  },
) {}
