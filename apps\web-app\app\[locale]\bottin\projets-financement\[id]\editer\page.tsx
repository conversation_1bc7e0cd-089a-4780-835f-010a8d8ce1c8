import EditionFundingProjectPage from '@/app/[locale]/bottin/projets-financement/[id]/editer/edit-funding-project-page';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { projectFormSections } from '@/constants/bottin/financing-project';
import { getQueryClientOptions } from '@/constants/query-client';
import { getGenericByIdOptions } from '@/hooks/bottin/generic-list.options';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';

type EditFundingProjectPageParams = PageDetailsParams;
export default async function EditFundingProjectPage(
  props: EditFundingProjectPageParams,
) {
  const params = await props.params;

  const { id, locale } = params;

  const formSections = await getFormSections({
    resourceName: 'directory',
    sections: projectFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = [
    'person',
    'financingProjectType',
    'numberType',
    'purchasedEquipment',
  ];

  const t0 = performance.now();

  const fundingProject = await Promise.all([
    queryClient.fetchQuery(
      getGenericByIdOptions({
        controlledListKey: 'fundingProjects',
        id,
        view: 'edit',
      }),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "person", "financingProjectType", "numberType", "purchasedEquipment" and "fundingProject" took ${t1 - t0} milliseconds.`,
  );

  if (!fundingProject) {
    return notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionFundingProjectPage
        formSections={formSections}
        id={id}
        locale={locale}
      />
    </HydrationBoundary>
  );
}
