import type { DbPermissionGroup } from '@rie/db-schema/entity-types';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  DbPermissionsGroupSchema,
  PermissionGroupSelectSchema,
  PermissionsGroupEditSchema,
  PermissionsGroupInputSchema,
  PermissionsGroupListSchema,
} from '../schemas';
import type {
  CollectionViewType,
  PermissionsGroupList,
  PermissionsGroupSelect,
  ResourceViewType,
} from '../types';

// Schema transformer for converting database permission group to list view
export const DbPermissionGroupToPermissionsGroupList = Schema.transformOrFail(
  DbPermissionsGroupSchema,
  PermissionsGroupListSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            name: raw.name,
            permissions: raw.permissions.map(({ permission }) => ({
              id: permission.id,
              domain: permission.domain,
              action: permission.action,
            })),
            description: raw.description,
            createdAt: raw.createdAt,
            updatedAt: raw.updatedAt,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse permission group for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database permission group to select view
export const DbPermissionGroupToPermissionsGroupSelect = Schema.transformOrFail(
  DbPermissionsGroupSchema,
  PermissionGroupSelectSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            value: raw.id,
            label: raw.name,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse permission group for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database permission group to input format
export const DbPermissionGroupToPermissionsGroupEdit = Schema.transformOrFail(
  DbPermissionsGroupSchema,
  PermissionsGroupEditSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            name: raw.name,
            description: raw.description === null ? undefined : raw.description,
            permissions: raw.permissions.map(({ permission }) => ({
              value: permission.id,
              label: `${permission.domain}:${permission.action}`,
            })),
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse permission group for input format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting input format to database input format
export const PermissionsGroupInputToDBInput = Schema.transformOrFail(
  PermissionsGroupInputSchema,
  Schema.Struct({
    name: Schema.String,
    description: Schema.optional(Schema.String),
    permissions: Schema.Array(Schema.String),
  }),
  {
    strict: true,
    decode: (val, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            name: val.name,
            description: val.description,
            permissions: val.permissions.map((p) => p.value),
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            val,
            error instanceof Error
              ? error.message
              : 'Failed to convert input format to database input format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of DbPermissionGroups to PermissionsGroupSelect[]
export const dbPermissionGroupsToPermissionsGroupSelect = (
  dbPermissionGroups: DbPermissionGroup[],
): PermissionsGroupSelect[] => {
  return dbPermissionGroups.map((dbPermissionGroup) => ({
    value: dbPermissionGroup.id,
    label: dbPermissionGroup.name,
  }));
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbPermissionGroupsToPermissionGroups = (
  dbPermissionGroups: DbPermissionGroup[],
  view: CollectionViewType,
): PermissionsGroupList[] | PermissionsGroupSelect[] => {
  return view === 'select'
    ? dbPermissionGroups.map((group) =>
        Schema.decodeUnknownSync(DbPermissionGroupToPermissionsGroupSelect)(
          group,
        ),
      )
    : dbPermissionGroups.map((group) =>
        Schema.decodeUnknownSync(DbPermissionGroupToPermissionsGroupList)(
          group,
        ),
      );
};

// Schema transformer for converting database permission group to list view
export const DbPermissionsGroupToPermissionsGroupList = Schema.transformOrFail(
  DbPermissionsGroupSchema,
  PermissionsGroupListSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            name: raw.name,
            permissions: raw.permissions.map(({ permission }) => ({
              id: permission.id,
              domain: permission.domain,
              action: permission.action,
            })),
            description: raw.description,
            createdAt: raw.createdAt,
            updatedAt: raw.updatedAt,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse permission group for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// New serializer function for DbPermissionsGroupSchema with view parameter
export const dbPermissionsGroupToPermissionGroup = (
  dbPermissionGroup: DbPermissionGroup,
  view: ResourceViewType,
) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbPermissionGroupToPermissionsGroupEdit)(
        dbPermissionGroup,
      )
    : Schema.decodeUnknownSync(DbPermissionsGroupToPermissionsGroupList)(
        dbPermissionGroup,
      );
};
