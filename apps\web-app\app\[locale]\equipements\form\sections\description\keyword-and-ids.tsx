import { AddTechniqueButton } from '@/app/[locale]/equipements/form/sections/description/add-technique-button';
import { FieldInfo } from '@/components/FieldInfo';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { Switch } from '@/ui/switch';
import { Textarea } from '@/ui/textarea';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const KeywordsAndEquipmentIdentification = () => {
  const locale = useAvailableLocale();
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations(
    'equipments.form.sections.description.keywordAndIds',
  );
  const { watch, control } = useFormContext<EquipmentFormSchema>();
  const isSafe = watch('isSafe', false);
  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData([
      'applicationSector',
      'researchDomain',
      'excellenceCenter',
      'technic',
    ]);
  return (
    <FormSubsection title={tEquipments('title')}>
      <FormField
        control={control}
        name="isSafe"
        render={({ field }) => {
          return (
            <FormItem>
              <FormControl>
                <div className="flex gap-x-2">
                  <Switch
                    checked={field.value}
                    id={field.name}
                    onCheckedChange={field.onChange}
                  />
                  <LabelTooltip
                    htmlFor={field.name}
                    label={tEquipments(`fields.${field.name}.label`)}
                  />
                </div>
              </FormControl>
              <FieldInfo>
                <FormMessage />
              </FieldInfo>
            </FormItem>
          );
        }}
      />
      <FormField
        control={control}
        name="registrationNumber"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip label={tEquipments(`fields.${field.name}.label`)} />
            <FormControl>
              <Input
                {...field}
                disabled={!isSafe}
                className={`transition-opacity ${!isSafe ? 'opacity-50 bg-gray-200 text-gray-400' : ''}`}
              />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <MultiselectField
        fieldName="researchFields"
        label={tEquipments('fields.researchField.label')}
        options={selectsData.researchDomain?.data ?? []}
        placeholder={tCommon('select')}
      />
      <MultiselectField
        fieldName="socioEconomicObjectives"
        label={tEquipments('fields.socioEconomicObjective.label')}
        options={selectsData.applicationSector?.data ?? []}
        placeholder={tCommon('select')}
      />
      <MultiselectField
        fieldName="excellencePole"
        label={tEquipments('fields.excellencePole.label')}
        options={selectsData.excellenceCenter?.data ?? []}
        placeholder={tCommon('select')}
      />
      <div className="space-y-2">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <MultiselectField
              fieldName="technics"
              label={tEquipments('fields.technics.label')}
              options={selectsData.technic?.data ?? []}
              placeholder={tCommon('select')}
            />
          </div>
          <div className="pt-7 ml-4">
            <AddTechniqueButton
              locale={locale}
              onSuccess={async () => {
                await selectsData.technic?.refetch();
              }}
            />
          </div>
        </div>
      </div>
      <FormField
        control={control}
        name="DOIOfArticles"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip label={tEquipments(`fields.${field.name}.label`)} />
            <FormControl>
              <Textarea {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="highlight"
        render={({ field }) => {
          return (
            <FormItem>
              <FormControl>
                <div className="flex gap-x-2">
                  <Switch
                    checked={field.value}
                    id={field.name}
                    onCheckedChange={field.onChange}
                  />
                  <LabelTooltip
                    htmlFor={field.name}
                    label={tEquipments(`fields.${field.name}.label`)}
                  />
                </div>
              </FormControl>
              <FieldInfo>
                <FormMessage />
              </FieldInfo>
            </FormItem>
          );
        }}
      />
    </FormSubsection>
  );
};
