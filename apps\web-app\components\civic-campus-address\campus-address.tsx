import { CampusTitle } from '@/app/[locale]/infrastructures/form/sections/description/components/campus-title';
import { NoRoomsAssociated } from '@/components/civic-campus-address/no-rooms-associated';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useGetRoomsByBuildingId } from '@/hooks/use-get-rooms-by-building-id';
import { cn } from '@/lib/utils';
import type { OptionalSelectOption } from '@/schemas/common-schema';
import { ComboboxInfiniteScroll } from '@/ui/combobox-infinite-scroll';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import { useFormContext, type FieldErrors } from 'react-hook-form';

type CampusAddressProps = {
  onSelectBuildingChange: (value: OptionalSelectOption) => void;
  onSelectRoomChange: (value: OptionalSelectOption) => void;
  selectedBuilding: OptionalSelectOption;
  selectedRoom: OptionalSelectOption;
  disabled?: boolean;
  buildingPlaceholder: string;
  roomPlaceholder: string;
  buildingFieldPath: string;
  roomFieldPath: string;
};

export const CampusAddress = ({
  onSelectBuildingChange,
  onSelectRoomChange,
  selectedBuilding,
  selectedRoom,
  disabled,
  buildingPlaceholder,
  roomPlaceholder,
  buildingFieldPath,
  roomFieldPath,
}: CampusAddressProps) => {
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations('equipments');
  const [roomSearchTerm, setRoomSearchTerm] = useState('');
  const { formState, clearErrors, setError } = useFormContext();

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['building']);

  const {
    data: rooms,
    status: roomsStatus,
    isPending: roomsIsPending,
  } = useGetRoomsByBuildingId(selectedBuilding?.value);

  useEffect(() => {
    if (
      selectedBuilding?.value &&
      roomsStatus === 'success' &&
      rooms &&
      rooms.length > 0 && // Rooms are available
      (!selectedRoom?.value || selectedRoom.value === null)
    ) {
      // Set custom error for room field
      setError(roomFieldPath, {
        type: 'custom',
        message: tEquipments(
          'form.sections.description.generalInfo.fields.buildingLocal.error.requiredWhenAvailable',
        ),
      });
    } else if (selectedRoom?.value) {
      // Clear room error when a room is selected
      clearErrors(roomFieldPath);
    }
  }, [
    selectedBuilding?.value,
    roomsStatus,
    rooms,
    selectedRoom?.value,
    roomFieldPath,
    setError,
    clearErrors,
    tEquipments,
  ]);

  const handleBuildingSearchChange = (searchTerm: string) => {
    handleOnSearchTermChange(searchTerm, 'building');
  };

  const handleRoomSearchChange = (searchTerm: string) => {
    setRoomSearchTerm(searchTerm);
  };

  const handleBuildingChangeWithErrorClear = (
    building: OptionalSelectOption,
  ) => {
    // Clear building errors when user makes a selection
    clearErrors(buildingFieldPath);
    onSelectBuildingChange(building);
  };

  const handleRoomChangeWithErrorClear = (room: OptionalSelectOption) => {
    // Clear room errors when user makes a selection
    clearErrors(roomFieldPath);
    onSelectRoomChange(room);
  };

  // Get errors for building and room fields
  const getBuildingError = (): string | undefined => {
    const pathParts = buildingFieldPath.split('.');
    let error: FieldErrors | undefined = formState.errors;
    for (const part of pathParts) {
      error = error?.[part] as FieldErrors | undefined;
      if (!error) break;
    }
    return typeof error?.message === 'string' ? error.message : undefined;
  };

  const getRoomError = (): string | undefined => {
    const pathParts = roomFieldPath.split('.');
    let error: FieldErrors | undefined = formState.errors;
    for (const part of pathParts) {
      error = error?.[part] as FieldErrors | undefined;
      if (!error) break;
    }
    return typeof error?.message === 'string' ? error.message : undefined;
  };

  const buildingError = getBuildingError();
  const roomError = getRoomError();

  // Check if room is required (when building is selected and rooms are available)
  const isRoomRequired = Boolean(
    selectedBuilding?.value &&
      roomsStatus === 'success' &&
      rooms &&
      rooms.length > 0,
  );

  // Filter rooms based on search term
  const filteredRooms = useMemo(() => {
    if (!rooms) return [];

    if (!roomSearchTerm.trim()) {
      return rooms;
    }

    return rooms.filter((room) =>
      room.label.toLowerCase().includes(roomSearchTerm.toLowerCase()),
    );
  }, [rooms, roomSearchTerm]);

  return (
    <FormSubsection title={<CampusTitle />}>
      <div className="flex flex-col">
        <LabelTooltip
          className={cn(buildingError && 'text-red-500')}
          label={tCommon('components.address.campus.building.label')}
          required={true}
        />
        <ComboboxInfiniteScroll
          className={cn('w-full', buildingError && 'border-red-500')}
          disabled={disabled}
          fetchNextPage={selectsData?.building?.fetchNextPage}
          hasNextPage={Boolean(selectsData?.building?.hasNextPage)}
          isFetching={Boolean(selectsData?.building?.isFetching)}
          isFetchingNextPage={Boolean(
            selectsData?.building?.isFetchingNextPage,
          )}
          noResultsText={tCommon('noResults')}
          onSearchChange={handleBuildingSearchChange}
          onValueChange={handleBuildingChangeWithErrorClear}
          options={selectsData.building?.data ?? []}
          placeholder={buildingPlaceholder}
          selected={selectedBuilding}
        />
        {buildingError && (
          <p className="text-sm font-medium text-red-500 mt-1">
            {buildingError}
          </p>
        )}
      </div>

      {selectedBuilding?.value ? (
        roomsStatus === 'success' && rooms && rooms.length > 0 ? (
          <div className="flex flex-col">
            <LabelTooltip
              className={cn(roomError && 'text-red-500')}
              label={tCommon('components.address.campus.room.label')}
              required={isRoomRequired}
            />
            <ComboboxInfiniteScroll
              className={cn('w-full', roomError && 'border-red-500')}
              disabled={disabled}
              fetchNextPage={undefined} // No pagination for rooms since they're filtered by building
              hasNextPage={false}
              isFetching={roomsIsPending}
              isFetchingNextPage={false}
              noResultsText={tCommon('noResults')}
              onSearchChange={handleRoomSearchChange}
              onValueChange={handleRoomChangeWithErrorClear}
              options={filteredRooms}
              placeholder={roomPlaceholder}
              selected={selectedRoom}
            />
            {roomError && (
              <p className="text-sm font-medium text-red-500 mt-1">
                {roomError}
              </p>
            )}
          </div>
        ) : (
          <NoRoomsAssociated
            label={tCommon('components.address.campus.room.label')}
            text={
              roomsStatus === 'success' && rooms && rooms.length === 0
                ? tCommon(
                    'components.address.campus.room.noRoomsAssociatedWithBuilding',
                  )
                : roomsIsPending
                  ? tCommon('loading')
                  : tCommon(
                      'components.address.campus.room.awaitingForBuildingSelection',
                    )
            }
          />
        )
      ) : (
        <NoRoomsAssociated
          label={tCommon('components.address.campus.room.label')}
          text={tCommon(
            'components.address.campus.room.awaitingForBuildingSelection',
          )}
        />
      )}
    </FormSubsection>
  );
};
