import { rooms } from '@/schemas/main/rooms.schema';
import { DbUtils } from '@rie/utils';
import { and, eq, isNotNull, isNull, or, relations } from 'drizzle-orm';
import { pgEnum, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const addressTypes = pgEnum('address_types', ['campus', 'civic']);

export const campusAddresses = pgTable('campus_addresses', {
  id: text()
    .$defaultFn(() => DbUtils.cuid2())
    .primaryKey(),
  room_id: text()
    .notNull()
    .references(() => rooms.id),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
});

export const civicAddresses = pgTable('civic_addresses', {
  id: text()
    .$defaultFn(() => DbUtils.cuid2())
    .primaryKey(),
  street1: text().notNull(),
  street2: text(),
  city: text().notNull(),
  state: text().notNull(),
  postalCode: text().notNull(),
  countryCode: text().notNull(),
  placeId: text().notNull(),
  lat: text(),
  lon: text(),
  createdAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp({ withTimezone: true }).notNull().defaultNow(),
});

export const addresses = pgTable(
  'addresses',
  {
    id: text()
      .$defaultFn(() => DbUtils.cuid2())
      .primaryKey(),
    addressType: addressTypes().notNull(),
    campusAddressId: text().references(() => campusAddresses.id),
    civicAddressId: text().references(() => civicAddresses.id),
  },
  (table) => [
    {
      // Ensure only one type of address is set
      check: or(
        and(
          eq(table.addressType, 'campus'),
          isNotNull(table.campusAddressId),
          isNull(table.civicAddressId),
        ),
        and(
          eq(table.addressType, 'civic'),
          isNotNull(table.civicAddressId),
          isNull(table.campusAddressId),
        ),
      ),
    },
  ],
);

export const addressesRelations = relations(addresses, ({ one }) => ({
  civicAddress: one(civicAddresses, {
    fields: [addresses.civicAddressId],
    references: [civicAddresses.id],
  }),
  campusAddress: one(campusAddresses, {
    fields: [addresses.campusAddressId],
    references: [campusAddresses.id],
  }),
}));
