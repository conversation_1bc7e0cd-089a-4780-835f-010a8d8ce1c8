import type {
  VendorDetailSchema,
  VendorEditSchema,
  VendorFormSchema,
  VendorInputSchema,
  VendorListSchema,
  VendorSchema,
  VendorSelectSchema,
} from '../schemas/vendors.schema';

import type * as Schema from 'effect/Schema';

export type Vendor = Schema.Schema.Type<typeof VendorSchema>;
export type VendorInput = Schema.Schema.Type<typeof VendorInputSchema>;
export type VendorFormSchemaType = Schema.Schema.Type<typeof VendorFormSchema>;
export type VendorList = Schema.Schema.Type<typeof VendorListSchema>;
export type VendorSelect = Schema.Schema.Type<typeof VendorSelectSchema>;
export type VendorEdit = Schema.Schema.Type<typeof VendorEditSchema>;
export type VendorDetail = Schema.Schema.Type<typeof VendorDetailSchema>;
