import {
  DbFundingProjectI18NSelectSchema,
  DbFundingProjectInputSchema,
  DbFundingProjectSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth1500MaxLengthSchema,
  requiredFieldWth150MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';

// — Full Funding Project shape
export const FundingProjectSchema = Schema.Struct({
  ...DbFundingProjectSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    DbFundingProjectI18NSelectSchema.omit('id', 'dataId'),
  ),
});

// — Translation input schema
export const FundingProjectI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: requiredFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
});

// — Funding Project List view schema (for directory table)
export const FundingProjectListSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String, // nom par défaut depuis traductions
  titulaire: Schema.NullishOr(Schema.String), // titulaire
  infrastructure: Schema.NullishOr(Schema.String), // infrastructure
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  holderId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
  fciId: Schema.NullishOr(Schema.String),
  synchroId: Schema.NullishOr(Schema.String),
  obtainingYear: Schema.NullishOr(Schema.Number),
  endDate: Schema.NullishOr(Schema.String),
});

// — Funding Project Select view schema
export const FundingProjectSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Funding Project Edit view schema
export const FundingProjectEditSchema = Schema.Struct({
  id: Schema.String,
  holderId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
  fciId: Schema.NullishOr(Schema.String),
  synchroId: Schema.NullishOr(Schema.String),
  obtainingYear: Schema.NullishOr(Schema.Number),
  endDate: Schema.NullishOr(Schema.String),
  translations: Schema.Array(FundingProjectI18NInputSchema),
});

// — Funding Project Detail view schema (same as list for now)
export const FundingProjectDetailSchema = FundingProjectListSchema;

// — Input (create/update) shape
export const FundingProjectInputSchema = Schema.Struct({
  ...DbFundingProjectInputSchema.omit('id').fields,
  translations: Schema.Array(FundingProjectI18NInputSchema),
});

// — Database schema (for serializers)
export const DbFundingProjectSchema = FundingProjectSchema;
