import { AddBuilding } from '@/app/[locale]/bottin/batiments/ajouter/add-building';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { buildingFormSections } from '@/constants/bottin/building';
import { getQueryClientOptions } from '@/constants/query-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function NewBuildingPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  const formSections = await getFormSections({
    resourceName: 'buildings',
    sections: buildingFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = ['campus', 'organisation'];

  const t0 = performance.now();

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  );

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "organization", "campus" took ${t1 - t0} milliseconds.`,
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AddBuilding locale={locale} formSections={formSections} />
    </HydrationBoundary>
  );
}
