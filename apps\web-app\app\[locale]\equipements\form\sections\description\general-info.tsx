import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { FieldInfo } from '@/components/FieldInfo';
import { CivicCampusAddress } from '@/components/civic-campus-address/civic-campus-address';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { PermissionGate } from '@/components/permissions/permission-gate';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { Textarea } from '@/ui/textarea';
import { useTranslations } from 'next-intl';
import { useCallback } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

export const GeneralInfo = () => {
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations(
    'equipments.form.sections.description.generalInfo',
  );
  const {
    control,
    watch,
    formState: { errors },
  } = useFormContext<EquipmentFormSchema>();

  const watchStatus = watch('status');

  const {
    append: appendName,
    fields: nameFields,
    remove: removeName,
  } = useFieldArray<EquipmentFormSchema>({
    control,
    name: 'name',
  });

  const handleAddNameTranslation = useCallback(
    (locale: string) => {
      appendName({
        locale: locale,
        value: '',
      });
    },
    [appendName],
  );

  const handleRemoveNameTranslation = useCallback(
    (index: number) => {
      removeName(index);
    },
    [removeName],
  );

  const {
    append: appendDescription,
    fields: descriptionFields,
    remove: removeDescription,
  } = useFieldArray<EquipmentFormSchema>({
    control,
    name: 'description',
  });

  const handleAddDescriptionTranslation = useCallback(
    (locale: string) => {
      appendDescription({
        locale: locale,
        value: '',
      });
    },
    [appendDescription],
  );

  const handleRemoveDescriptionTranslation = useCallback(
    (index: number) => {
      removeDescription(index);
    },
    [removeDescription],
  );

  const nameErrorMessage = getFieldErrorMessage(errors, 'name')
    ? getFieldErrorMessage(errors, 'name')
    : undefined;
  const manufacturerProviderErrorMessage = getFieldErrorMessage(
    errors,
    'manufacturerProvider',
  );

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData([
      'supplier',
      'equipmentType',
      'organisation',
      'person',
      'building',
      'local',
      'equipmentStatus',
      'equipmentCategory',
    ]);

  return (
    <FormSubsection title={tEquipments('title')}>
      <FieldWithTranslations
        control={control}
        errorMessage={nameErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="name"
        fields={nameFields}
        label={(locale) =>
          tEquipments('fields.name.labelWithLocale', {
            locale: tCommon(locale),
          })
        }
        onAddTranslation={handleAddNameTranslation}
        onRemoveTranslation={handleRemoveNameTranslation}
        required={true}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="description"
        fields={descriptionFields}
        label={(locale) =>
          tEquipments('fields.description.labelWithLocale', {
            locale: tCommon(locale),
          })
        }
        onAddTranslation={handleAddDescriptionTranslation}
        onRemoveTranslation={handleRemoveDescriptionTranslation}
      />
      <div>
        <ComboboxFieldInfiniteScroll
          clearErrorsOnChange
          controlledListKey="supplier"
          errorFieldName="manufacturerProvider"
          fetchNextPage={selectsData.supplier?.fetchNextPage}
          fieldLabel={tEquipments(
            'fields.manufacturerProvider.manufacturer.label',
          )}
          fieldName="manufacturerProvider.manufacturer"
          hasNextPage={Boolean(selectsData.supplier?.hasNextPage)}
          isFetching={Boolean(selectsData.supplier?.isFetching)}
          isFetchingNextPage={Boolean(selectsData.supplier?.isFetchingNextPage)}
          isOnError={Boolean(manufacturerProviderErrorMessage)}
          onSearchChange={handleOnSearchTermChange}
          options={selectsData.supplier?.data ?? []}
          placeholder={tCommon('select')}
        />
        <ComboboxFieldInfiniteScroll
          clearErrorsOnChange
          controlledListKey="supplier"
          errorFieldName="manufacturerProvider"
          fetchNextPage={selectsData.supplier?.fetchNextPage}
          fieldLabel={tEquipments('fields.manufacturerProvider.provider.label')}
          fieldName="manufacturerProvider.supplier"
          hasNextPage={Boolean(selectsData.supplier?.hasNextPage)}
          isFetching={Boolean(selectsData.supplier?.isFetching)}
          isFetchingNextPage={Boolean(selectsData.supplier?.isFetchingNextPage)}
          isOnError={Boolean(manufacturerProviderErrorMessage)}
          onSearchChange={handleOnSearchTermChange}
          options={selectsData.supplier?.data ?? []}
          placeholder={tCommon('select')}
        />
        <FormMessage className="-mt-6">
          {manufacturerProviderErrorMessage
            ? manufacturerProviderErrorMessage
            : null}
        </FormMessage>
      </div>
      <FormField
        control={control}
        name="model"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor="model"
              label={tEquipments(`fields.${field.name}.label`)}
            />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="serialNumber"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor="serialNumber"
              label={tEquipments(`fields.${field.name}.label`)}
            />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="equipmentType"
        fetchNextPage={selectsData.equipmentType?.fetchNextPage}
        fieldLabel={tEquipments('fields.type.label')}
        fieldName="type"
        hasNextPage={Boolean(selectsData.equipmentType?.hasNextPage)}
        isFetching={Boolean(selectsData.equipmentType?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.equipmentType?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.equipmentType?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
      <MultiselectField
        fieldName="categories"
        label={tEquipments('fields.categories.label')}
        options={selectsData.equipmentCategory?.data ?? []}
        placeholder={tCommon('select')}
        required
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="organisation"
        fetchNextPage={selectsData.organisation?.fetchNextPage}
        fieldLabel={tEquipments('fields.jurisdiction.label')}
        fieldName="jurisdiction"
        hasNextPage={Boolean(selectsData.organisation?.hasNextPage)}
        isFetching={Boolean(selectsData.organisation?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.organisation?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.organisation?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="person"
        fetchNextPage={selectsData.person?.fetchNextPage}
        fieldLabel={tEquipments('fields.equipmentHolder.label')}
        fieldName="equipmentHolder"
        hasNextPage={Boolean(selectsData.person?.hasNextPage)}
        isFetching={Boolean(selectsData.person?.isFetching)}
        isFetchingNextPage={Boolean(selectsData.person?.isFetchingNextPage)}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
      {/*TODO: When connecting to the new API, we can simply read field `isCampusAddressConfidential` to know if we should display the address, we won't need `PermissionGate` */}
      <PermissionGate action="read" resourceType="equipment">
        <CivicCampusAddress
          addressFieldPath="address"
          totalAddresses={0}
          withAddressTypeToggle={false}
        />
      </PermissionGate>
      <div className="h-px bg-border my-6" />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="equipmentStatus"
        fetchNextPage={selectsData.equipmentStatus?.fetchNextPage}
        fieldLabel={tEquipments('fields.status.label')}
        fieldName="status"
        hasNextPage={Boolean(selectsData.equipmentStatus?.hasNextPage)}
        isFetching={Boolean(selectsData.equipmentStatus?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.equipmentStatus?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.equipmentStatus?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
      {(watchStatus.value === '2' ||
        watchStatus.label === 'Partiellement fonctionnel') && (
        <>
          <FormField
            control={control}
            name="percentageFunctionality"
            render={({ field }) => (
              <FormItem>
                <LabelTooltip
                  htmlFor="percentageFunctionality"
                  label={tEquipments('fields.percentageFunctionality.label')}
                />
                <FormControl>
                  <Input
                    {...field}
                    inputMode="numeric"
                    max={100}
                    min={0}
                    onChange={(e) => {
                      let value = e.target.value;
                      value = value.replace(/^0+/, '');
                      // Pour que ca reste entre 0-100
                      if (value === '' || Number.isNaN(Number(value))) {
                        value = '0';
                      } else if (Number(value) < 0) {
                        value = '0';
                      } else if (Number(value) > 100) {
                        value = '100';
                      }
                      field.onChange(value);
                    }}
                    type="number"
                  />
                </FormControl>
                <FieldInfo>
                  <FormMessage />
                </FieldInfo>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="breakdown"
            render={({ field }) => (
              <FormItem>
                <LabelTooltip label={tEquipments('fields.breakdown.label')} />
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FieldInfo>
                  <FormMessage />
                </FieldInfo>
              </FormItem>
            )}
          />
        </>
      )}
      <FormField
        control={control}
        name="inventoryNumber"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip label={tEquipments('fields.inventoryNumber.label')} />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
    </FormSubsection>
  );
};
