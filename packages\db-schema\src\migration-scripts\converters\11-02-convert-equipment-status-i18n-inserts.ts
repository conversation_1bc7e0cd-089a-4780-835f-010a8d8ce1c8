import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class InstitutionsI18nMigrationConverter extends BaseConverter {
  private institutionsI18nMappings: Mapping[] = [];

  private parseInstitutionI18NInsertStatement(
    sqlStatement: string,
  ): MySQLI18NDescription[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        nom: values.nom,
        description: values.description,
      },
    ];
  }

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    equipmentStatusIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the equipment_status
    const newEquipmentStatusId =
      equipmentStatusIdMappings[mysqlRecord.data_id.toString()];
    if (!newEquipmentStatusId) {
      throw new Error(
        `No mapping found for equipment_status_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.institutionsI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newEquipmentStatusId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for etat_equipement_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'etat_equipement_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No etat_equipement_trad INSERT statements found.');
        return;
      }

      // Load equipment_status ID mappings
      const equipmentStatusIdMappings =
        await this.loadEntityIdMappings('etat_equipement');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords =
          this.parseInstitutionI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, equipmentStatusIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      // Generate output
      const postgresInserts = this.generatePostgresI18NInsert(
        allPostgresRecords,
        'equipment_statuses_i18n',
        'Equipment Status I18n Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.institutionsI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'etat_equipement_trad', postgres: 'equipment_statuses_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} equipment_statuses_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
