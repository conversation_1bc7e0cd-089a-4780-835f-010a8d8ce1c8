import { LocalCapacity } from '@/app/[locale]/bottin/locaux/form/sections/local-capacity';
import { FieldInfo } from '@/components/FieldInfo';
import { CharacterCount } from '@/components/character-count/character-count';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const GeneralInfo = () => {
  const tRooms = useTranslations('rooms.form.sections.description.generalInfo');
  const tCommon = useTranslations('common');
  const { control } = useFormContext<RoomFormSchema>();

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['building', 'organisation', 'roomCategory']);

  return (
    <FormSubsection title={tRooms('title')}>
      <FormField
        control={control}
        name="roomNumber"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor="roomNumber"
              label={tRooms('fields.roomNumber.label')}
              required
            />
            <FormControl>
              <Input {...field} data-testid="roomNumber" />
            </FormControl>
            <FieldInfo>
              <FormMessage />
              <CharacterCount count={field.value?.length ?? 0} max={1000} />
            </FieldInfo>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="alias"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor="alias"
              label={tRooms('fields.alias.label')}
              tooltip={tRooms('fields.alias.tooltip')}
            />
            <FormControl>
              <Input {...field} data-testid="alias" />
            </FormControl>
            <FieldInfo>
              <FormMessage />
              <CharacterCount count={field.value?.length ?? 0} max={1000} />
            </FieldInfo>
          </FormItem>
        )}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="building"
        fetchNextPage={selectsData.building?.fetchNextPage}
        fieldLabel={tRooms('fields.building.label')}
        fieldName="building"
        hasNextPage={selectsData.building?.hasNextPage ?? false}
        isFetching={Boolean(selectsData.building?.isFetching)}
        isFetchingNextPage={Boolean(selectsData.building?.isFetchingNextPage)}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.building?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="organisation"
        fetchNextPage={selectsData.organisation?.fetchNextPage}
        fieldLabel={tRooms('fields.jurisdiction.label')}
        fieldName="jurisdiction"
        hasNextPage={selectsData.organisation?.hasNextPage ?? false}
        isFetching={Boolean(selectsData.organisation?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.organisation?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.organisation?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
      <MultiselectField
        testId="categories"
        fieldName="categories"
        label={tRooms('fields.categories.label')}
        options={selectsData.roomCategory?.data ?? []}
        placeholder={tCommon('select')}
      />
      <LocalCapacity />
    </FormSubsection>
  );
};
