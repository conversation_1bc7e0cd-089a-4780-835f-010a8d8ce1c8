import * as path from 'node:path';
import { SQL_FILE_NAME } from '@/migration-scripts/constants';
import { UnitMigrationConverter } from './converters/24-01-convert-unit-inserts';

async function convertUnitData() {
  const scriptDir = __dirname;
  // Input file is in the data directory
  const inputFile = path.join(
    scriptDir,
    'data',
    'mysql_riedb-content_24-04-2025.sql',
  );
  // Output will go to migration-scripts/output
  const outputDir = path.join(scriptDir, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new UnitMigrationConverter();
    // Convert the file
    await converter.convertFile(inputFile, outputFile);
    console.log(`Conversion completed. Output saved to ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertUnitData().catch(console.error);
