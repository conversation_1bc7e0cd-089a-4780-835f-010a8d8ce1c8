import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import type { InfrastructureFormSchema } from '@/schemas/infrastructure-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

type AffiliatedPersonProps = {
  index: number;
};
export const AffiliatedPerson = ({ index }: AffiliatedPersonProps) => {
  const tInfrastructures = useTranslations(
    'infrastructures.form.sections.affiliations.affiliatedPersons',
  );
  const tCommon = useTranslations('common');
  const { control } = useFormContext<InfrastructureFormSchema>();

  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['person', 'jobType']);

  return (
    <>
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="person"
        fetchNextPage={selectsData.person?.fetchNextPage}
        fieldLabel={tInfrastructures('fields.person.label')}
        fieldName={`affiliatedPersons.${index}.person`}
        hasNextPage={Boolean(selectsData.person?.hasNextPage)}
        isFetching={Boolean(selectsData.person?.isFetching)}
        isFetchingNextPage={Boolean(selectsData.person?.isFetchingNextPage)}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.person?.data ?? []}
        placeholder={tCommon('select')}
        required
      />
      <FormField
        control={control}
        name={`affiliatedPersons.${index}.jobTitle`}
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              label={tInfrastructures('fields.jobTitle.label')}
              required
            />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="jobType"
        fetchNextPage={selectsData.jobType?.fetchNextPage}
        fieldLabel={tInfrastructures('fields.jobType.label')}
        fieldName={`affiliatedPersons.${index}.jobType`}
        hasNextPage={Boolean(selectsData.jobType?.hasNextPage)}
        isFetching={Boolean(selectsData.jobType?.isFetching)}
        isFetchingNextPage={Boolean(selectsData.jobType?.isFetchingNextPage)}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.jobType?.data ?? []}
        placeholder={tCommon('select')}
        required
      />
    </>
  );
};
