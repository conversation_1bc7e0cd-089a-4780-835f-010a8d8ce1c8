import type {
  UnitDetailSchema,
  UnitEditSchema,
  UnitInputSchema,
  UnitListSchema,
  UnitSchema,
  UnitSelectSchema,
} from '../schemas/units.schema';

import type * as Schema from 'effect/Schema';

export type Unit = Schema.Schema.Type<typeof UnitSchema>;
export type UnitInput = Schema.Schema.Type<typeof UnitInputSchema>;
export type UnitList = Schema.Schema.Type<typeof UnitListSchema>;
export type UnitSelect = Schema.Schema.Type<typeof UnitSelectSchema>;
export type UnitEdit = Schema.Schema.Type<typeof UnitEditSchema>;
export type UnitDetail = Schema.Schema.Type<typeof UnitDetailSchema>;
