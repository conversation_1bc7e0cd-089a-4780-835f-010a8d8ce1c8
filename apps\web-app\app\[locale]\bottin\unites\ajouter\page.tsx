import { AddUnit } from '@/app/[locale]/bottin/unites/ajouter/add-unit';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { unitFormSections } from '@/constants/bottin/unit';
import { getQueryClientOptions } from '@/constants/query-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function NewUnitPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  const formSections = await getFormSections({
    resourceName: 'units',
    sections: unitFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = [
    'organisation',
    'unitType',
    'organisation',
    'person',
  ];

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AddUnit locale={locale} formSections={formSections} />
    </HydrationBoundary>
  );
}
