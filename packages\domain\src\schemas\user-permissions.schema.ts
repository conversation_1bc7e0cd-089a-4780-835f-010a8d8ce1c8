import {
  DbPermissionGroupSelectSchema,
  DbPermissionSelectSchema,
  DbRoleSelectSchema,
  DbUserRoleSelectSchema,
  DbUserSelectSchema,
} from '@rie/db-schema/entity-schemas';
import { domainEnum } from '@rie/db-schema/schemas';
import { permissionActionEnum } from '@rie/db-schema/schemas';
import * as Schema from 'effect/Schema';

export const ResourceTypeSchema = Schema.Literal(...domainEnum.enumValues);

const PermissionActionSchema = Schema.Literal(
  ...permissionActionEnum.enumValues,
);
/**
 * Schema for removing a role from a user
 * Similar to assignment but without grantedBy field
 */
export const RemoveRoleFromUserSchema = Schema.Struct({
  userId: Schema.String,
  roleId: Schema.String,
  resourceType: Schema.optional(ResourceTypeSchema),
  resourceId: Schema.optional(Schema.String),
});

export type RemoveRoleFromUser = Schema.Schema.Type<
  typeof RemoveRoleFromUserSchema
>;

/**
 * Schema for checking user permissions
 */
export const CheckUserPermissionSchema = Schema.Struct({
  userId: Schema.String,
  domain: ResourceTypeSchema,
  action: PermissionActionSchema,
  resourceId: Schema.optional(Schema.String),
});

export type CheckUserPermission = Schema.Schema.Type<
  typeof CheckUserPermissionSchema
>;

/**
 * Schema for checking user access to a resource
 */
export const CheckUserAccessSchema = Schema.Struct({
  userId: Schema.String,
  resourceType: ResourceTypeSchema,
  resourceId: Schema.String,
});

export type CheckUserAccess = Schema.Schema.Type<typeof CheckUserAccessSchema>;

// Additional DB schemas are imported at the top of the file

/**
 * Schema for user role assignment response
 * Reuses DbUserRoleSelectSchema from the database
 */
export const UserRoleAssignmentResponseSchema = DbUserRoleSelectSchema;

export type UserRoleAssignmentResponse = Schema.Schema.Type<
  typeof UserRoleAssignmentResponseSchema
>;

/**
 * Schema for permission check response
 */
export const PermissionCheckResponseSchema = Schema.Struct({
  hasPermission: Schema.Boolean,
  userId: Schema.String,
  domain: ResourceTypeSchema,
  action: PermissionActionSchema,
  resourceId: Schema.optional(Schema.String),
  resourceType: Schema.optional(ResourceTypeSchema),
});

export type PermissionCheckResponse = Schema.Schema.Type<
  typeof PermissionCheckResponseSchema
>;

/**
 * Schema for access check response
 */
export const AccessCheckResponseSchema = Schema.Struct({
  hasAccess: Schema.Boolean,
  userId: Schema.String,
  resourceType: ResourceTypeSchema,
  resourceId: Schema.String,
});

export type AccessCheckResponse = Schema.Schema.Type<
  typeof AccessCheckResponseSchema
>;

/**
 * Schema for access tree response
 */
export const AccessTreeResponseSchema = Schema.Struct({
  institutions: Schema.Array(Schema.String),
  units: Schema.Array(Schema.String),
  infrastructures: Schema.Array(Schema.String),
  equipments: Schema.Array(Schema.String),
});

export type AccessTreeResponse = Schema.Schema.Type<
  typeof AccessTreeResponseSchema
>;

/**
 * Schema for user permissions for resource response
 */
// DbPermissionSelectSchema is imported at the top of the file

export const UserPermissionsForResourceResponseSchema = Schema.Struct({
  permissions: Schema.Array(DbPermissionSelectSchema),
  userId: Schema.String,
  resourceType: ResourceTypeSchema,
  resourceId: Schema.String,
});

export type UserPermissionsForResourceResponse = Schema.Schema.Type<
  typeof UserPermissionsForResourceResponseSchema
>;

/**
 * Schema for success response
 */
export const SuccessResponseSchema = Schema.Struct({
  success: Schema.Boolean,
  message: Schema.String,
});

export type SuccessResponse = Schema.Schema.Type<typeof SuccessResponseSchema>;

/**
 * Schema for error response
 */
export const ErrorResponseSchema = Schema.Struct({
  error: Schema.String,
});

export type ErrorResponse = Schema.Schema.Type<typeof ErrorResponseSchema>;

const GroupPermissionsPermissionSchema = Schema.Struct({
  permission: DbPermissionSelectSchema.omit('createdAt', 'updatedAt'),
});

const GroupPermissionsSchema = Schema.Struct({
  permissions: Schema.Array(GroupPermissionsPermissionSchema),
});

const GroupSchema = Schema.Struct({
  ...DbPermissionGroupSelectSchema.omit('createdAt', 'updatedAt').fields,
  ...GroupPermissionsSchema.fields,
});

const RolePermissionsGroupsSchema = Schema.Array(
  Schema.Struct({
    group: GroupSchema,
  }),
);

const RoleSchema = Schema.Struct({
  ...DbRoleSelectSchema.omit('createdAt', 'updatedAt').fields,
  rolePermissionGroups: RolePermissionsGroupsSchema,
});

export const UserRoleSchema = Schema.Struct({
  ...DbUserRoleSelectSchema.omit('createdAt', 'roleId', 'userId', 'grantedBy')
    .fields,
  role: RoleSchema,
});

export const DbUserDetailWithRolesSchema = Schema.Struct({
  ...DbUserSelectSchema.omit('createdAt', 'updatedAt').fields,
  userRoles: Schema.Array(UserRoleSchema),
});

export const UserDetailWithRolesSchemas = Schema.Struct({
  ...DbUserSelectSchema.omit('createdAt', 'updatedAt').fields,
  roles: Schema.Array(
    Schema.Struct({
      id: Schema.String,
      name: Schema.String,
      description: Schema.NullOr(Schema.String),
      resourceType: ResourceTypeSchema,
      resourceId: Schema.NullOr(Schema.String),
      permissions: Schema.Array(
        Schema.Struct({
          id: Schema.String,
          domain: ResourceTypeSchema,
          action: PermissionActionSchema,
        }),
      ),
    }),
  ),
});
