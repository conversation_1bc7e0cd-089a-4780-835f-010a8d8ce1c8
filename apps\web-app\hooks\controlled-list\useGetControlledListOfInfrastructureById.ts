import { getControlledListsOfInfrastructureById } from '@/services/controled-lists';
import { mapControlledListsToSelectOptions } from '@/services/mappers/controlled-list-select-data';
import type { ControlledListKey, SelectOption } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import { queryOptions, useQueries } from '@tanstack/react-query';

type UseGetControlledListOfInfrastructureByIdInput = {
  controlledLists: ControlledListKey[];
  id: number | string;
  locale: SupportedLocale;
};

export const controlledListOfInfrastructureByIdOptions = (
  controlledListKey: ControlledListKey,
  id: number | string,
  locale: SupportedLocale,
) =>
  queryOptions({
    queryFn: () =>
      getControlledListsOfInfrastructureById({ controlledListKey, id, locale }),
    queryKey: ['controlledLists', controlledListKey, id, locale],
    select: (controlledList) =>
      mapControlledListsToSelectOptions(controlledList.data),
  });

export const useGetControlledListOfInfrastructureById = ({
  controlledLists,
  id,
  locale,
}: UseGetControlledListOfInfrastructureByIdInput) => {
  return useQueries({
    combine: (results) => {
      return results.reduce<Partial<Record<ControlledListKey, SelectOption[]>>>(
        (acc, result, index) => {
          const listKey = controlledLists[index];
          acc[listKey] = result.data ?? [];
          return acc;
        },
        {},
      );
    },
    queries: controlledLists.map((controlledListKey) => {
      return controlledListOfInfrastructureByIdOptions(
        controlledListKey,
        id,
        locale,
      );
    }),
  });
};
