import type { RoleList, RoleSelect } from '../types';
import type { CollectionViewType } from '../types/query.types';

// Type for the database role with all related data
type DbRoleWithRelations = {
  id: string;
  name: string;
  description: string | null;
  createdAt: string;
  updatedAt: string;
  rolePermissions: Array<{
    permissionId: string;
    roleId: string;
    permission: {
      id: string;
      domain: string;
      action: string;
    };
  }>;
  rolePermissionGroups: Array<{
    roleId: string;
    groupId: string;
    group: {
      id: string;
      name: string;
      description: string | null;
    };
  }>;
  parentRoles: Array<{
    parentRoleId: string;
    childRoleId: string;
    parentRole: {
      id: string;
      name: string;
    };
  }>;
  childRoles: Array<{
    parentRoleId: string;
    childRoleId: string;
    childRole: {
      id: string;
      name: string;
    };
  }>;
};

// Transform DbRoleWithRelations to RoleList (for list view)
export const transformDbRoleToRoleList = (
  dbRole: DbRoleWithRelations,
): RoleList => {
  return {
    id: dbRole.id,
    name: dbRole.name,
    description: dbRole.description,
    createdAt: dbRole.createdAt,
    updatedAt: dbRole.updatedAt,
    directPermissions: dbRole.rolePermissions.map((rp) => ({
      id: rp.permission.id,
      domain: rp.permission.domain,
      action: rp.permission.action,
    })),
    permissionsGroups: dbRole.rolePermissionGroups.map((rpg) => ({
      id: rpg.group.id,
      name: rpg.group.name,
      description: rpg.group.description,
    })),
    parentRoles: dbRole.parentRoles.map((pr) => ({
      id: pr.parentRole.id,
      name: pr.parentRole.name,
    })),
  };
};

// Transform basic DbRole to RoleSelect (for select view)
export const transformDbRoleToRoleSelect = (dbRole: {
  id: string;
  name: string;
}): RoleSelect => {
  return {
    value: dbRole.id,
    label: dbRole.name,
  };
};

// Helper function to transform an array of DbRolesWithRelations to RoleList[]
export const dbRolesToRoleList = (
  dbRoles: DbRoleWithRelations[],
): RoleList[] => {
  return dbRoles.map(transformDbRoleToRoleList);
};

// Helper function to transform an array of basic DbRoles to RoleSelect[]
export const dbRolesToRoleSelect = (
  dbRoles: { id: string; name: string }[],
): RoleSelect[] => {
  return dbRoles.map(transformDbRoleToRoleSelect);
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbRolesToRoles = (
  dbRoles: DbRoleWithRelations[] | { id: string; name: string }[],
  view: CollectionViewType,
): RoleList[] | RoleSelect[] => {
  if (view === 'select') {
    return dbRolesToRoleSelect(dbRoles as { id: string; name: string }[]);
  }
  return dbRolesToRoleList(dbRoles as DbRoleWithRelations[]);
};
