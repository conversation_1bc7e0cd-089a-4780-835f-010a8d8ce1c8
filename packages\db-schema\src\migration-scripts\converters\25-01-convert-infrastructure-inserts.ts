import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlInfrastructure {
  infrastructure_id: number;
  guid_id: number | null;
  uid: string | null;
  type_infrastructure_id: number | null;
  statut_infrastructure_id: number | null;
  visibilite_id: number | null;
  pseudonyme: string | null;
  url: string | null;
  telephone: string | null;
  mettre_en_vedette: number | null;
}

interface PostgresInfrastructure {
  id: string;
  guid_id: string | null;
  type_id: string | null;
  status_id: string | null;
  website: string | null;
  is_featured: boolean;
  visibility_id: string | null;
}

export class InfrastructureMigrationConverter extends BaseConverter {
  private infrastructureMappings: Mapping[] = [];
  private guidIdMappings: Record<string, string> = {};
  private infrastructureTypeIdMappings: Record<string, string> = {};
  private infrastructureStatusIdMappings: Record<string, string> = {};
  private visibilityIdMappings: Record<string, string> = {};

  private parseInsertStatement(sqlStatement: string): MySqlInfrastructure[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        infrastructure_id: Number.parseInt(values.infrastructure_id),
        guid_id:
          values.guid_id !== null ? Number.parseInt(values.guid_id) : null,
        uid: values.uid,
        type_infrastructure_id:
          values.type_infrastructure_id !== null
            ? Number.parseInt(values.type_infrastructure_id)
            : null,
        statut_infrastructure_id:
          values.statut_infrastructure_id !== null
            ? Number.parseInt(values.statut_infrastructure_id)
            : null,
        visibilite_id:
          values.visibilite_id !== null
            ? Number.parseInt(values.visibilite_id)
            : null,
        pseudonyme: values.pseudonyme,
        url: values.url,
        telephone: values.telephone,
        mettre_en_vedette:
          values.mettre_en_vedette !== null
            ? Number.parseInt(values.mettre_en_vedette)
            : null,
      },
    ];
  }

  private async loadAllMappings(): Promise<void> {
    try {
      this.guidIdMappings = await this.loadEntityIdMappings('guid');
      this.infrastructureTypeIdMappings = await this.loadEntityIdMappings(
        'type_infrastructure',
      );
      this.infrastructureStatusIdMappings = await this.loadEntityIdMappings(
        'statut_infrastructure',
      );
      this.visibilityIdMappings = await this.loadEntityIdMappings('visibilite');
    } catch (error) {
      console.error('Error loading mappings:', error);
      throw error;
    }
  }

  private convertToPostgres(
    mysqlInfrastructure: MySqlInfrastructure,
  ): PostgresInfrastructure {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.infrastructureMappings.push({
      mysqlId: mysqlInfrastructure.infrastructure_id,
      postgresId: postgresId,
    });

    // Map foreign keys using the loaded mappings
    const guidId =
      mysqlInfrastructure.guid_id !== null
        ? this.guidIdMappings[mysqlInfrastructure.guid_id.toString()] || null
        : null;

    const typeId =
      mysqlInfrastructure.type_infrastructure_id !== null
        ? this.infrastructureTypeIdMappings[
            mysqlInfrastructure.type_infrastructure_id.toString()
          ] || null
        : null;

    const statusId =
      mysqlInfrastructure.statut_infrastructure_id !== null
        ? this.infrastructureStatusIdMappings[
            mysqlInfrastructure.statut_infrastructure_id.toString()
          ] || null
        : null;

    const visibilityId =
      mysqlInfrastructure.visibilite_id !== null
        ? this.visibilityIdMappings[
            mysqlInfrastructure.visibilite_id.toString()
          ] || null
        : null;

    return {
      id: postgresId,
      guid_id: guidId,
      type_id: typeId,
      status_id: statusId,
      website: mysqlInfrastructure.url,
      is_featured: mysqlInfrastructure.mettre_en_vedette === 1,
      visibility_id: visibilityId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Load all required ID mappings
      await this.loadAllMappings();

      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for infrastructure table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'infrastructure',
      );

      if (insertStatements.length === 0) {
        console.log('No infrastructure INSERT statements found.');
        return;
      }

      const allPostgresInfrastructures: PostgresInfrastructure[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlInfrastructures = this.parseInsertStatement(statement);
        const postgresInfrastructures = mysqlInfrastructures.map((infra) =>
          this.convertToPostgres(infra),
        );
        allPostgresInfrastructures.push(...postgresInfrastructures);
      }

      // Generate output
      let output = this.generateCommentHeader('Infrastructure Inserts');

      // Add the actual INSERT statement with all columns
      const columns = [
        'id',
        'guid_id',
        'type_id',
        'status_id',
        'website',
        'is_featured',
        'visibility_id',
      ];

      const values = allPostgresInfrastructures
        .map((infrastructure) => {
          return `(${this.formatSqlValue(infrastructure.id)}, 
            ${this.formatSqlValue(infrastructure.guid_id)}, 
            ${this.formatSqlValue(infrastructure.type_id)}, 
            ${this.formatSqlValue(infrastructure.status_id)}, 
            ${this.formatSqlValue(infrastructure.website)}, 
            ${infrastructure.is_featured}, 
            ${this.formatSqlValue(infrastructure.visibility_id)})`;
        })
        .join(',\n');

      output += `INSERT INTO "infrastructures" (${columns
        .map((c) => `"${c}"`)
        .join(', ')}) VALUES\n${values};\n\n`;

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, output);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.infrastructureMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'infrastructure', postgres: 'infrastructures' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresInfrastructures.length} infrastructures records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
