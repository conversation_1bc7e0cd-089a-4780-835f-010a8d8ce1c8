import {
  DbUnitI18NSelectSchema,
  DbUnitInputSchema,
  DbUnitSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import { descriptionSchema } from './base.schema';

// — Translation shape
export const UnitTranslationSchema = Schema.Struct({
  id: Schema.String,
  locale: Schema.String,
  name: Schema.NullishOr(Schema.String),
  description: descriptionSchema,
  otherNames: Schema.NullishOr(Schema.String),
  acronyms: Schema.NullishOr(Schema.String),
});

export type UnitTranslation = Schema.Schema.Type<typeof UnitTranslationSchema>;

// — Translation Input for API
export const UnitI18NInputSchema = Schema.Struct({
  locale: Schema.String,
  name: Schema.NullishOr(Schema.String),
  description: descriptionSchema,
  otherNames: Schema.NullishOr(Schema.String),
  acronyms: Schema.NullishOr(Schema.String),
});

// — Full Unit shape (with translations)
export const UnitSchema = Schema.Struct({
  ...DbUnitSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(DbUnitI18NSelectSchema),
});

// — Input (create/update) shape
export const UnitInputSchema = Schema.Struct({
  ...DbUnitInputSchema.omit('id').fields,
  translations: Schema.Array(UnitI18NInputSchema),
});

// — Unit List view schema (for directory table)
export const UnitListSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String, // nom par défaut depuis traductions
  acronym: Schema.NullishOr(Schema.String), // acronyme depuis traductions
  parentName: Schema.NullishOr(Schema.String), // nom du parent
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  guidId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
  parentId: Schema.NullishOr(Schema.String),
});

// — Unit Select view schema
export const UnitSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Unit Edit view schema
export const UnitEditSchema = Schema.Struct({
  id: Schema.String,
  guidId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
  parentId: Schema.NullishOr(Schema.String),
  translations: Schema.Array(UnitI18NInputSchema),
});

// — Unit Detail view schema (same as list for now)
export const UnitDetailSchema = UnitListSchema;

// — Database schema (for serializers)
export const DbUnitSchema = UnitSchema;

// — Item schema (for serializers)
export const UnitItemSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

export type UnitItem = Schema.Schema.Type<typeof UnitItemSchema>;

// Types are exported from @/types/units.type.ts

// — Frontend Form Schema (client payload shape)
export const UnitFormSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  names: Schema.Array(
    Schema.Struct({ locale: Schema.String, value: Schema.String }),
  ),
  alias: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: Schema.optional(Schema.String),
    }),
  ),
  pseudonym: Schema.Array(
    Schema.Struct({ locale: Schema.String, value: Schema.String }),
  ),
  parentUnit: Schema.Struct({
    label: Schema.optional(Schema.String),
    value: Schema.NullishOr(Schema.String),
  }),
  relatedOrganizations: Schema.Array(Schema.String),
  unitType: Schema.String,
});
