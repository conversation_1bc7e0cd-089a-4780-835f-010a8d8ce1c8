import { InstitutionNotFoundError } from '@rie/domain/errors';
import type { InstitutionInputSchema } from '@rie/domain/schemas';
import { InstitutionsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type InstitutionInput = Schema.Schema.Type<typeof InstitutionInputSchema>;

export class InstitutionsServiceLive extends Effect.Service<InstitutionsServiceLive>()(
  'InstitutionsServiceLive',
  {
    dependencies: [InstitutionsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllInstitutions = () =>
        Effect.gen(function* () {
          const repo = yield* InstitutionsRepositoryLive;
          return yield* repo.findAllInstitutions();
        });

      const getInstitutionById = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* InstitutionsRepositoryLive;
          const institution = yield* repo.findInstitutionById(id);
          if (!institution) {
            return yield* Effect.fail(new InstitutionNotFoundError({ id }));
          }
          return institution;
        });

      const createInstitution = (institution: InstitutionInput) =>
        Effect.gen(function* () {
          const repo = yield* InstitutionsRepositoryLive;
          return yield* repo.createInstitution({ institution });
        });

      const updateInstitution = ({
        id,
        institution,
      }: { institution: InstitutionInput; id: string }) =>
        Effect.gen(function* () {
          const repo = yield* InstitutionsRepositoryLive;
          const existingInstitution = yield* repo.findInstitutionById(id);
          if (!existingInstitution) {
            return yield* Effect.fail(new InstitutionNotFoundError({ id }));
          }
          return yield* repo.updateInstitution({
            institutionId: id,
            institution,
          });
        });

      const deleteInstitution = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* InstitutionsRepositoryLive;
          const existingInstitution = yield* repo.findInstitutionById(id);
          if (!existingInstitution) {
            return yield* Effect.fail(new InstitutionNotFoundError({ id }));
          }
          const result = yield* repo.deleteInstitution(id);
          return result.length > 0;
        });

      return {
        getAllInstitutions,
        getInstitutionById,
        createInstitution,
        updateInstitution,
        deleteInstitution,
      } as const;
    }),
  },
) {}
