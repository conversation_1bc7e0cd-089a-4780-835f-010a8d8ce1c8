import { users } from '@/schemas/auth/auth.schema';
import {
  equipmentAssociatedRepairers,
  equipmentAssociatedRetailers,
  equipments,
} from '@/schemas/main/equipments.schema';
import { locales } from '@/schemas/main/locales.schema';
import { serviceContracts } from '@/schemas/main/service-contracts.schema';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  boolean,
  date,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const vendors = pgTable('vendors', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  isActive: boolean().default(true),
  startDate: date({ mode: 'string' }),
  endDate: date({ mode: 'string' }),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const vendorsRelations = relations(vendors, ({ one, many }) => ({
  translations: many(vendorsI18N),

  // Direct relations from equipments
  manufacturedEquipment: many(equipments, {
    relationName: 'equipment_to_manufacturer',
  }),
  suppliedEquipments: many(equipments, {
    relationName: 'equipment_to_supplier',
  }),

  // Relations through junction tables
  repairedEquipments: many(equipmentAssociatedRepairers, {
    relationName: 'repairer_to_equipment',
  }),
  retailedEquipments: many(equipmentAssociatedRetailers, {
    relationName: 'retailer_to_equipment',
  }),

  serviceContracts: many(serviceContracts),
  contacts: many(vendorAssociatedContacts),
}));

export const vendorsI18N = pgTable(
  'vendors_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => vendors.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    website: text(),
    description: text(),
    otherNames: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const vendorsI18NRelations = relations(vendorsI18N, ({ one }) => ({
  vendor: one(vendors, {
    fields: [vendorsI18N.dataId],
    references: [vendors.id],
  }),
  locale: one(locales, {
    fields: [vendorsI18N.locale],
    references: [locales.code],
  }),
}));

export const vendorContacts = pgTable('vendor_contacts', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  phoneNumber: text(),
  email: text(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const vendorContactsRelations = relations(
  vendorContacts,
  ({ many }) => ({
    vendors: many(vendorAssociatedContacts),
    translations: many(vendorContactsI18n),
  }),
);

export const vendorContactsI18n = pgTable(
  'vendor_contacts_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => vendorContacts.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const vendorContactsI18nRelations = relations(
  vendorContactsI18n,
  ({ one }) => ({
    vendorContact: one(vendorContacts, {
      fields: [vendorContactsI18n.dataId],
      references: [vendorContacts.id],
    }),
    locale: one(locales, {
      fields: [vendorContactsI18n.locale],
      references: [locales.code],
    }),
  }),
);

export const vendorAssociatedContacts = pgTable(
  'vendor_associated_contacts',
  {
    vendorId: text()
      .notNull()
      .references(() => vendors.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    vendorContactId: text()
      .notNull()
      .references(() => vendorContacts.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      uniqueVendorContact: primaryKey({
        columns: [table.vendorId, table.vendorContactId],
      }),
    },
  ],
);

export const vendorAssociatedContactsRelations = relations(
  vendorAssociatedContacts,
  ({ one }) => ({
    vendor: one(vendors, {
      fields: [vendorAssociatedContacts.vendorId],
      references: [vendors.id],
    }),
    contact: one(vendorContacts, {
      fields: [vendorAssociatedContacts.vendorContactId],
      references: [vendorContacts.id],
    }),
  }),
);
