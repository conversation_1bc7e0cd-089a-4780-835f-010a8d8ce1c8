import * as Data from 'effect/Data';

/**
 * Error thrown when attempting to create a vendor that already exists
 */
export class VendorAlreadyExistsError extends Data.TaggedError(
  'VendorAlreadyExistsError',
)<{
  readonly name: string;
}> {}

/**
 * Error thrown when a vendor cannot be found
 */
export class VendorNotFoundError extends Data.TaggedError(
  'VendorNotFoundError',
)<{
  readonly id?: string;
  readonly name?: string;
}> {}

/**
 * Error thrown when vendor validation fails
 */
export class VendorValidationError extends Data.TaggedError(
  'VendorValidationError',
)<{
  readonly message: string;
  readonly fields?: Record<string, string>;
}> {}
