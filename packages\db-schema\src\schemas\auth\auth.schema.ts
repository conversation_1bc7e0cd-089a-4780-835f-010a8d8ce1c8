import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { boolean, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { userPermissions, userRoles } from './permissions.schema';

export const users = pgTable('users', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  name: text().notNull(),
  email: text().notNull().unique(),
  emailVerified: boolean().notNull(),
  image: text(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
});

export const sessions = pgTable('sessions', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  expiresAt: timestamp('expires_at').notNull(),
  token: text('token').notNull().unique(),
  ipAddress: text(),
  userAgent: text(),
  userId: text()
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  createdAt: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
});

export const accounts = pgTable('accounts', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  accountId: text().notNull(),
  providerId: text().notNull(),
  userId: text()
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  accessToken: text(),
  refreshToken: text(),
  idToken: text(),
  accessTokenExpiresAt: timestamp(),
  refreshTokenExpiresAt: timestamp(),
  scope: text(),
  password: text(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
});

export const verifications = pgTable('verifications', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  identifier: text().notNull(),
  value: text().notNull(),
  expiresAt: timestamp().notNull(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' }),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' }),
});

export const usersRelations = relations(users, ({ many }) => ({
  sessions: many(sessions),
  accounts: many(accounts),
  verifications: many(verifications),
  userRoles: many(userRoles, { relationName: 'userToUserRoles' }),
  grantedUserRoles: many(userRoles, { relationName: 'grantedByToUserRoles' }),
  directPermissions: many(userPermissions, {
    relationName: 'userToUserPermissions',
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));
