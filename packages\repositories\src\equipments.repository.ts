import { equipments, equipmentsI18N } from '@rie/db-schema/schemas';
import type { EquipmentInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { count, eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class EquipmentsRepositoryLive extends Effect.Service<EquipmentsRepositoryLive>()(
  'EquipmentsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      /**
       * Find all equipments
       */
      const findAllEquipments = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.equipments.findMany({
            columns: {
              id: true,
              guidId: true,
              campusAddressId: true,
              isCampusAddressConfidential: true,
              model: true,
              serialNumber: true,
              homologationNumber: true,
              inventoryNumber: true,
              doi: true,
              useInClinicalTrial: true,
              isHidden: true,
              typeId: true,
              statusId: true,
              workingPercentage: true,
              monetaryCost: true,
              inKindCost: true,
              manufactureYear: true,
              acquisitionDate: true,
              installationDate: true,
              decommissioningDate: true,
              scientificManagerId: true,
              manufacturerId: true,
              supplierId: true,
              infrastructureId: true,
              isActive: true,
              isFeatured: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  specification: true,
                  usageContext: true,
                  risk: true,
                  comment: true,
                },
              },
              infrastructure: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              type: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              status: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              scientificManager: {
                columns: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  uid: true,
                },
              },
              manufacturer: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              supplier: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      /**
       * Find equipments for collection (list/grid) with minimal relations and optional limit
       */
      const findAllEquipmentsForCollection = dbClient.makeQuery(
        (execute, limit?: number) => {
          return execute((client) =>
            client.query.equipments.findMany({
              columns: {
                id: true,
                model: true,
                updatedAt: true,
                infrastructureId: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                  },
                },
                type: {
                  columns: { id: true },
                  with: {
                    translations: {
                      columns: { locale: true, name: true },
                    },
                  },
                },
                status: {
                  columns: { id: true },
                  with: {
                    translations: {
                      columns: { locale: true, name: true },
                    },
                  },
                },
                manufacturer: {
                  columns: { id: true },
                  with: {
                    translations: {
                      columns: { locale: true, name: true },
                    },
                  },
                },
                infrastructure: {
                  columns: { id: true },
                  with: {
                    translations: {
                      columns: { locale: true, name: true },
                    },
                  },
                },
              },
              ...(typeof limit === 'number' && limit > 0 ? { limit } : {}),
            }),
          );
        },
      );

      /**
       * Find all equipments under a specific infrastructure
       */
      const findEquipmentsByInfrastructureId = dbClient.makeQuery(
        (execute, infrastructureId: string) => {
          return execute((client) =>
            client
              .select({
                id: equipments.id,
                guidId: equipments.guidId,
                campusAddressId: equipments.campusAddressId,
                isCampusAddressConfidential:
                  equipments.isCampusAddressConfidential,
                model: equipments.model,
                serialNumber: equipments.serialNumber,
                homologationNumber: equipments.homologationNumber,
                inventoryNumber: equipments.inventoryNumber,
                doi: equipments.doi,
                useInClinicalTrial: equipments.useInClinicalTrial,
                isHidden: equipments.isHidden,
                typeId: equipments.typeId,
                statusId: equipments.statusId,
                workingPercentage: equipments.workingPercentage,
                monetaryCost: equipments.monetaryCost,
                inKindCost: equipments.inKindCost,
                manufactureYear: equipments.manufactureYear,
                acquisitionDate: equipments.acquisitionDate,
                installationDate: equipments.installationDate,
                decommissioningDate: equipments.decommissioningDate,
                scientificManagerId: equipments.scientificManagerId,
                manufacturerId: equipments.manufacturerId,
                supplierId: equipments.supplierId,
                infrastructureId: equipments.infrastructureId,
                isFeatured: equipments.isFeatured,
                institutionId: equipments.institutionId,
                createdAt: equipments.createdAt,
                updatedAt: equipments.updatedAt,
                modifiedBy: equipments.modifiedBy,
              })
              .from(equipments)
              .where(eq(equipments.infrastructureId, infrastructureId)),
          );
        },
      );

      /**
       * Find all equipments under a specific infrastructure with translations and related data
       */
      const findEquipmentsByInfrastructureIdWithRelations = dbClient.makeQuery(
        (execute, infrastructureId: string) => {
          return execute((client) =>
            client.query.equipments.findMany({
              where: eq(equipments.infrastructureId, infrastructureId),
              columns: {
                id: true,
                guidId: true,
                campusAddressId: true,
                isCampusAddressConfidential: true,
                model: true,
                serialNumber: true,
                homologationNumber: true,
                inventoryNumber: true,
                doi: true,
                useInClinicalTrial: true,
                isHidden: true,
                typeId: true,
                statusId: true,
                workingPercentage: true,
                monetaryCost: true,
                inKindCost: true,
                manufactureYear: true,
                acquisitionDate: true,
                installationDate: true,
                decommissioningDate: true,
                scientificManagerId: true,
                manufacturerId: true,
                supplierId: true,
                infrastructureId: true,
                isFeatured: true,
                institutionId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                    description: true,
                  },
                },
                type: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                status: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                scientificManager: {
                  columns: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    uid: true,
                  },
                },
                manufacturer: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                supplier: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      const findEquipmentById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.equipments.findFirst({
            where: eq(equipments.id, id),
            columns: {
              id: true,
              guidId: true,
              campusAddressId: true,
              isCampusAddressConfidential: true,
              model: true,
              serialNumber: true,
              homologationNumber: true,
              inventoryNumber: true,
              doi: true,
              useInClinicalTrial: true,
              isHidden: true,
              typeId: true,
              statusId: true,
              workingPercentage: true,
              monetaryCost: true,
              inKindCost: true,
              manufactureYear: true,
              acquisitionDate: true,
              installationDate: true,
              decommissioningDate: true,
              scientificManagerId: true,
              manufacturerId: true,
              supplierId: true,
              infrastructureId: true,
              isActive: true,
              isFeatured: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  specification: true,
                  usageContext: true,
                  risk: true,
                  comment: true,
                },
              },
              type: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              status: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              scientificManager: {
                columns: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  uid: true,
                },
              },
              manufacturer: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              supplier: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
              infrastructure: {
                columns: {
                  id: true,
                  guidId: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      /**
       * Create equipment with translations
       */
      const createEquipment = (params: { equipment: EquipmentInput }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Create the equipment
            const [createdEquipment] = yield* tx((client) =>
              client
                .insert(equipments)
                .values({
                  ...params.equipment,
                })
                .returning({
                  id: equipments.id,
                  guidId: equipments.guidId,
                  campusAddressId: equipments.campusAddressId,
                  isCampusAddressConfidential:
                    equipments.isCampusAddressConfidential,
                  model: equipments.model,
                  serialNumber: equipments.serialNumber,
                  homologationNumber: equipments.homologationNumber,
                  inventoryNumber: equipments.inventoryNumber,
                  doi: equipments.doi,
                  useInClinicalTrial: equipments.useInClinicalTrial,
                  isHidden: equipments.isHidden,
                  typeId: equipments.typeId,
                  statusId: equipments.statusId,
                  workingPercentage: equipments.workingPercentage,
                  monetaryCost: equipments.monetaryCost,
                  inKindCost: equipments.inKindCost,
                  manufactureYear: equipments.manufactureYear,
                  acquisitionDate: equipments.acquisitionDate,
                  installationDate: equipments.installationDate,
                  decommissioningDate: equipments.decommissioningDate,
                  scientificManagerId: equipments.scientificManagerId,
                  manufacturerId: equipments.manufacturerId,
                  supplierId: equipments.supplierId,
                  infrastructureId: equipments.infrastructureId,
                  isFeatured: equipments.isFeatured,
                  institutionId: equipments.institutionId,
                  createdAt: equipments.createdAt,
                  updatedAt: equipments.updatedAt,
                  modifiedBy: equipments.modifiedBy,
                }),
            );

            if (!createdEquipment) {
              return yield* Effect.fail(
                new Error('Failed to create equipment'),
              );
            }

            // Create the translations
            const translationsToInsert = params.equipment.translations.map(
              (translation) => ({
                dataId: createdEquipment.id,
                ...translation,
              }),
            );
            const createdTranslations = yield* tx((client) =>
              client
                .insert(equipmentsI18N)
                .values(translationsToInsert)
                .returning({
                  id: equipmentsI18N.id,
                  locale: equipmentsI18N.locale,
                  name: equipmentsI18N.name,
                  description: equipmentsI18N.description,
                  specification: equipmentsI18N.specification,
                  usageContext: equipmentsI18N.usageContext,
                  risk: equipmentsI18N.risk,
                  comment: equipmentsI18N.comment,
                }),
            );

            // Return the equipment with its translations directly
            return {
              ...createdEquipment,
              translations: createdTranslations,
            };
          });
        });
      };

      /**
       * Update equipment with translations
       */
      const updateEquipment = (params: {
        equipmentId: string;
        equipment: EquipmentInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the equipment
            const [updatedEquipment] = yield* tx((client) =>
              client
                .update(equipments)
                .set({
                  ...params.equipment,
                })
                .where(eq(equipments.id, params.equipmentId))
                .returning({
                  id: equipments.id,
                  guidId: equipments.guidId,
                  campusAddressId: equipments.campusAddressId,
                  isCampusAddressConfidential:
                    equipments.isCampusAddressConfidential,
                  model: equipments.model,
                  serialNumber: equipments.serialNumber,
                  homologationNumber: equipments.homologationNumber,
                  inventoryNumber: equipments.inventoryNumber,
                  doi: equipments.doi,
                  useInClinicalTrial: equipments.useInClinicalTrial,
                  isHidden: equipments.isHidden,
                  typeId: equipments.typeId,
                  statusId: equipments.statusId,
                  workingPercentage: equipments.workingPercentage,
                  monetaryCost: equipments.monetaryCost,
                  inKindCost: equipments.inKindCost,
                  manufactureYear: equipments.manufactureYear,
                  acquisitionDate: equipments.acquisitionDate,
                  installationDate: equipments.installationDate,
                  decommissioningDate: equipments.decommissioningDate,
                  scientificManagerId: equipments.scientificManagerId,
                  manufacturerId: equipments.manufacturerId,
                  supplierId: equipments.supplierId,
                  infrastructureId: equipments.infrastructureId,
                  isFeatured: equipments.isFeatured,
                  institutionId: equipments.institutionId,
                  createdAt: equipments.createdAt,
                  updatedAt: equipments.updatedAt,
                  modifiedBy: equipments.modifiedBy,
                }),
            );

            // Delete existing translations
            yield* tx((client) =>
              client
                .delete(equipmentsI18N)
                .where(eq(equipmentsI18N.dataId, params.equipmentId)),
            );

            // Insert new translations
            const translationsToInsert = params.equipment.translations.map(
              (translation) => ({
                dataId: params.equipmentId,
                ...translation,
              }),
            );

            const updatedTranslations = yield* tx((client) =>
              client
                .insert(equipmentsI18N)
                .values(translationsToInsert)
                .returning({
                  id: equipmentsI18N.id,
                  locale: equipmentsI18N.locale,
                  name: equipmentsI18N.name,
                  description: equipmentsI18N.description,
                  specification: equipmentsI18N.specification,
                  usageContext: equipmentsI18N.usageContext,
                  risk: equipmentsI18N.risk,
                  comment: equipmentsI18N.comment,
                }),
            );

            // Return the equipment with its translations directly
            return {
              ...updatedEquipment,
              translations: updatedTranslations,
            };
          });
        });
      };

      /**
       * Delete equipment
       */
      const deleteEquipment = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(equipments)
            .where(eq(equipments.id, id))
            .returning({ id: equipments.id }),
        );
      });

      const countAllEquipments = dbClient.makeQuery((execute) => {
        return execute(async (client) => {
          const rows = await client.select({ value: count() }).from(equipments);
          return Number(rows[0]?.value ?? 0);
        });
      });

      return {
        findAllEquipments,
        findAllEquipmentsForCollection,
        findEquipmentsByInfrastructureId,
        findEquipmentsByInfrastructureIdWithRelations,
        findEquipmentById,
        countAllEquipments,
        createEquipment,
        updateEquipment,
        deleteEquipment,
      } as const;
    }),
  },
) {}
