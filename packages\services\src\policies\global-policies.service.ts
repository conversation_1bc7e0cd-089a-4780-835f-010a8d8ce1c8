import {
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import { AccessTreeServiceLive } from '../access-tree.service';
import { UserPermissionsServiceLive } from '../user-permissions.service';
import {
  type Policy,
  type UserPermissionsError,
  type UserPermissionsRequirements,
  checkPermission,
  permissionForResource,
  resourceAccess,
} from './policy.service';

export class GlobalPoliciesLive extends Effect.Service<GlobalPoliciesLive>()(
  'GlobalPoliciesLive',
  {
    effect: Effect.gen(function* () {
      // Application Sectors
      const canReadApplicationSectors = checkPermission(
        'applicationSector:read',
      );
      const canCreateApplicationSector = checkPermission(
        'applicationSector:create',
      );
      const canUpdateApplicationSector = checkPermission(
        'applicationSector:update',
      );
      const canDeleteApplicationSector = checkPermission(
        'applicationSector:delete',
      );

      // Buildings (global)
      const canReadBuildings = checkPermission('building:read');
      const canCreateBuilding = checkPermission('building:create');
      const canUpdateBuilding = checkPermission('building:update');
      const canDeleteBuilding = checkPermission('building:delete');

      // Campuses
      const canReadCampuses = checkPermission('campus:read');
      const canCreateCampus = checkPermission('campus:create');
      const canUpdateCampus = checkPermission('campus:update');
      const canDeleteCampus = checkPermission('campus:delete');

      // Vendors
      const canReadVendors = checkPermission('vendor:read');
      const canCreateVendor = checkPermission('vendor:create');
      const canUpdateVendor = checkPermission('vendor:update');
      const canDeleteVendor = checkPermission('vendor:delete');

      // Rooms (global)
      const canReadRooms = checkPermission('room:read');
      const canCreateRoom = checkPermission('room:create');
      const canUpdateRoom = checkPermission('room:update');
      const canDeleteRoom = checkPermission('room:delete');

      // People (global)
      const canReadPeople = checkPermission('people:read');
      const canCreatePerson = checkPermission('people:create');
      const canUpdatePerson = checkPermission('people:update');
      const canDeletePerson = checkPermission('people:delete');

      // Service Offers
      const canReadServiceOffers = checkPermission('serviceOffer:read');
      const canCreateServiceOffer = checkPermission('serviceOffer:create');
      const canUpdateServiceOffer = checkPermission('serviceOffer:update');
      const canDeleteServiceOffer = checkPermission('serviceOffer:delete');

      // Funding Projects
      const canReadFundingProjects = checkPermission('fundingProject:read');
      const canCreateFundingProject = checkPermission('fundingProject:create');
      const canUpdateFundingProject = checkPermission('fundingProject:update');
      const canDeleteFundingProject = checkPermission('fundingProject:delete');

      // User Management (global)
      const canReadUsers = checkPermission('user:read');
      const canCreateUser = checkPermission('user:create');
      const canUpdateUser = checkPermission('user:update');
      const canDeleteUser = checkPermission('user:delete');

      // Resource-specific permissions
      const canReadUnit = (
        unitId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        unitId
          ? permissionForResource('unit', 'read', unitId)
          : checkPermission('unit:read');
      const canCreateUnit = checkPermission('unit:create');
      const canUpdateUnit = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('unit', 'update', unitId);
      const canDeleteUnit = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('unit', 'delete', unitId);

      const canReadInstitution = (
        institutionId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        institutionId
          ? permissionForResource('institution', 'read', institutionId)
          : checkPermission('institution:read');
      const canCreateInstitution = checkPermission('institution:create');
      const canUpdateInstitution = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('institution', 'update', institutionId);
      const canDeleteInstitution = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('institution', 'delete', institutionId);

      const canReadInfrastructure = (
        infrastructureId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        infrastructureId
          ? permissionForResource('infrastructure', 'read', infrastructureId)
          : checkPermission('infrastructure:read');
      const canCreateInfrastructure = checkPermission('infrastructure:create');
      const canUpdateInfrastructure = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('infrastructure', 'update', infrastructureId);
      const canDeleteInfrastructure = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('infrastructure', 'delete', infrastructureId);

      const canReadEquipment = (
        equipmentId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        equipmentId
          ? permissionForResource('equipment', 'read', equipmentId)
          : checkPermission('equipment:read');
      const canCreateEquipment = checkPermission('equipment:create');
      const canUpdateEquipment = (
        equipmentId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('equipment', 'update', equipmentId);
      const canDeleteEquipment = (
        equipmentId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('equipment', 'delete', equipmentId);

      // Resource access checks
      const hasUnitAccess = (
        unitId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        resourceAccess('unit', unitId);
      const hasInstitutionAccess = (
        institutionId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        resourceAccess('institution', institutionId);
      const hasInfrastructureAccess = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        resourceAccess('infrastructure', infrastructureId);
      const hasEquipmentAccess = (
        equipmentId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        resourceAccess('equipment', equipmentId);

      return {
        canReadApplicationSectors,
        canCreateApplicationSector,
        canUpdateApplicationSector,
        canDeleteApplicationSector,
        canReadBuildings,
        canCreateBuilding,
        canUpdateBuilding,
        canDeleteBuilding,
        canReadCampuses,
        canCreateCampus,
        canUpdateCampus,
        canDeleteCampus,
        canReadVendors,
        canCreateVendor,
        canUpdateVendor,
        canDeleteVendor,
        canReadRooms,
        canCreateRoom,
        canUpdateRoom,
        canDeleteRoom,
        canReadPeople,
        canCreatePerson,
        canUpdatePerson,
        canDeletePerson,
        canReadServiceOffers,
        canCreateServiceOffer,
        canUpdateServiceOffer,
        canDeleteServiceOffer,
        canReadFundingProjects,
        canCreateFundingProject,
        canUpdateFundingProject,
        canDeleteFundingProject,
        canReadUsers,
        canCreateUser,
        canUpdateUser,
        canDeleteUser,
        canReadUnit,
        canCreateUnit,
        canUpdateUnit,
        canDeleteUnit,
        canReadInstitution,
        canCreateInstitution,
        canUpdateInstitution,
        canDeleteInstitution,
        canReadInfrastructure,
        canCreateInfrastructure,
        canUpdateInfrastructure,
        canDeleteInfrastructure,
        canReadEquipment,
        canCreateEquipment,
        canUpdateEquipment,
        canDeleteEquipment,
        hasUnitAccess,
        hasInstitutionAccess,
        hasInfrastructureAccess,
        hasEquipmentAccess,
      } as const;
    }),
  },
) {}

export const GlobalPoliciesLayer = Layer.mergeAll(
  GlobalPoliciesLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
);
