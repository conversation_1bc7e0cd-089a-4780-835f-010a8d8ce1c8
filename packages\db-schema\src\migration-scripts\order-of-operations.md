## Base Tables (No Foreign Key Dependencies)

- [x] locale
- [ ] user
- [x] media_type
- [ ] generic_status
- [x] visibility
- [ ] time_unit
- [ ] equipment_relationship_type
- [ ] equipment_status
- [ ] equipment_precision
- [ ] identifier_type

## First Level Dependencies

- [x] guid (depends on: generic_status)
- [ ] media (depends on: media_type)
- [x] media_type_i18n
- [x] visibility_i18n
- [x] institution_type
- [ ] unit_type
- [ ] room_category
- [ ] equipment_category
- [ ] application_sector
- [ ] research_field
- [ ] documentation_category
- [ ] excellence_hub
- [ ] innovation_lab
- [ ] vendor

## Second Level Dependencies

- [ ] person (depends on: guid)
- [ ] institution (depends on: guid, institution_type)
- [ ] unit (depends on: guid, unit_type)

## Third Level Dependencies

- [ ] campus (depends on: institution)
- [ ] account (depends on: user)
- [ ] session (depends on: user)
- [ ] funding_project_type
- [ ] infrastructure_type
- [ ] infrastructure_status
- [ ] infrastructure_role_type
- [ ] institution_role_type
- [ ] unit_role_type

## Fourth Level Dependencies

- [ ] building (depends on: campus, institution, unit)
- [ ] funding_project (depends on: person, funding_project_type)

## Fifth Level Dependencies

- [ ] room (depends on: building, institution, unit)
- [ ] equipment (depends on: guid)
- [ ] infrastructure (depends on: guid, institution, infrastructure_type, infrastructure_status, visibility)

## Relationship/Junction Tables

- [ ] equipment_dependency
- [ ] equipment_associated_category
- [ ] equipment_associated_application_sector
- [ ] equipment_associated_research_field
- [ ] equipment_associated_excellence_hub
- [ ] equipment_location
- [ ] equipment_lifecycle
- [ ] equipment_maintenance_frequency
- [ ] equipment_supervisor
- [ ] equipment_photo
- [ ] equipment_video
- [ ] infrastructure_associated_room
- [ ] infrastructure_associated_person
- [ ] infrastructure_associated_location
- [ ] infrastructure_associated_innovation_lab
- [ ] room_associated_category
- [ ] service_contract
- [ ] funding_project_infrastructure
- [ ] institution_associated_person
- [ ] unit_associated_person