import { users } from '@/schemas/auth/auth.schema';
import { buildings } from '@/schemas/main/buildings.schema';
import { locales } from '@/schemas/main/locales.schema';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  boolean,
  doublePrecision,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const rooms = pgTable('rooms', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  isActive: boolean().default(true),
  number: text().notNull(),
  area: doublePrecision(),
  floorLoad: doublePrecision(),
  buildingId: text().references(() => buildings.id, {
    onDelete: 'cascade',
    onUpdate: 'cascade',
  }),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const roomsRelations = relations(rooms, ({ one, many }) => ({
  building: one(buildings, {
    fields: [rooms.buildingId],
    references: [buildings.id],
  }),
  categories: many(roomAssociatedCategories),
  // equipmentAddress: one(equipments, {
  //   fields: [equipments.address],
  //   references: [equipments.id],
  //}
  // ), // TODO: Check with Sakinah about this relation
}));

export const roomCategories = pgTable('room_categories', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
});

export const roomCategoriesRelations = relations(
  roomCategories,
  ({ many }) => ({
    translations: many(roomCategoriesI18N),
    rooms: many(roomAssociatedCategories),
    categories: many(roomAssociatedCategories),
  }),
);

export const roomCategoriesI18N = pgTable(
  'room_categories_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => roomCategories.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const roomCategoriesI18NRelations = relations(
  roomCategoriesI18N,
  ({ one }) => ({
    roomsCategory: one(roomCategories, {
      fields: [roomCategoriesI18N.dataId],
      references: [roomCategories.id],
    }),
    locale: one(locales, {
      fields: [roomCategoriesI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const roomAssociatedCategories = pgTable(
  'room_associated_categories',
  {
    roomId: text()
      .notNull()
      .references(() => rooms.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    categoryId: text()
      .notNull()
      .references(() => roomCategories.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      roomCategoryPk: primaryKey({
        columns: [table.roomId, table.categoryId],
      }),
    },
  ],
);

export const roomAssociatedCategoriesRelations = relations(
  roomAssociatedCategories,
  ({ one }) => ({
    room: one(rooms, {
      fields: [roomAssociatedCategories.roomId],
      references: [rooms.id],
    }),
    category: one(roomCategories, {
      fields: [roomAssociatedCategories.categoryId],
      references: [roomCategories.id],
    }),
  }),
);
