import { FieldInfo } from '@/components/FieldInfo';
import { MultiselectField } from '@/components/form-fields/multiselect-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import type { InfrastructureFormSchema } from '@/schemas/infrastructure-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Switch } from '@/ui/switch';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const Identification = () => {
  const tInfrastructures = useTranslations(
    'infrastructures.form.sections.description.infrastructureIdentification',
  );
  const tCommon = useTranslations('common');
  const { control } = useFormContext<InfrastructureFormSchema>();

  const { selectsData } = useControlledListSelectsData(['innovationLab']);

  return (
    <FormSubsection title={tInfrastructures('title')}>
      <MultiselectField
        fieldName="innovationLab"
        label={tInfrastructures('fields.innovationLab.label')}
        options={selectsData.innovationLab?.data ?? []}
        placeholder={tCommon('select')}
      />
      <FormField
        control={control}
        name="highlight"
        render={({ field }) => {
          return (
            <FormItem>
              <FormControl>
                <div className="flex gap-x-2">
                  <Switch
                    checked={field.value}
                    id="highlight"
                    onCheckedChange={field.onChange}
                  />
                  <LabelTooltip
                    htmlFor="highlight"
                    label={tInfrastructures(`fields.${field.name}.label`)}
                  />
                </div>
              </FormControl>
              <FieldInfo>
                <FormMessage />
              </FieldInfo>
            </FormItem>
          );
        }}
      />
    </FormSubsection>
  );
};
