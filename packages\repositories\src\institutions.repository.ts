import { DBSchema } from '@rie/db-schema';
import type { InstitutionInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class InstitutionsRepositoryLive extends Effect.Service<InstitutionsRepositoryLive>()(
  'InstitutionsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      const findAllInstitutions = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.institutions.findMany({
            columns: {
              id: true,
              isActive: true,
              guidId: true,
              typeId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  acronyms: true,
                  otherNames: true,
                },
              },
              type: {
                columns: {
                  id: true,
                  uid: true,
                },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      const findInstitutionById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.institutions.findFirst({
            where: eq(DBSchema.institutions.id, id),
            columns: {
              id: true,
              isActive: true,
              guidId: true,
              typeId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  acronyms: true,
                  otherNames: true,
                },
              },
              type: {
                columns: {
                  id: true,
                  uid: true,
                },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      const createInstitution = (params: { institution: InstitutionInput }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Create the institution
            const [createdInstitution] = yield* tx((client) =>
              client
                .insert(DBSchema.institutions)
                .values(params.institution)
                .returning({
                  id: DBSchema.institutions.id,
                  guidId: DBSchema.institutions.guidId,
                  typeId: DBSchema.institutions.typeId,
                  createdAt: DBSchema.institutions.createdAt,
                  updatedAt: DBSchema.institutions.updatedAt,
                  modifiedBy: DBSchema.institutions.modifiedBy,
                }),
            );

            if (!createdInstitution) {
              return yield* Effect.fail(
                new Error('Failed to create institution'),
              );
            }

            // Create the translations
            const translationsToInsert = params.institution.translations.map(
              (translation) => ({
                dataId: createdInstitution.id,
                ...translation,
              }),
            );
            const createdTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.institutionsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.institutionsI18N.id,
                  locale: DBSchema.institutionsI18N.locale,
                  name: DBSchema.institutionsI18N.name,
                  description: DBSchema.institutionsI18N.description,
                  otherNames: DBSchema.institutionsI18N.otherNames,
                  acronyms: DBSchema.institutionsI18N.acronyms,
                }),
            );

            // Return the institution with its translations directly
            return {
              ...createdInstitution,
              translations: createdTranslations,
            };
          });
        });
      };

      const updateInstitution = (params: {
        institutionId: string;
        institution: InstitutionInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the institution
            const [updatedInstitution] = yield* tx((client) =>
              client
                .update(DBSchema.institutions)
                .set({
                  ...params.institution,
                })
                .where(eq(DBSchema.institutions.id, params.institutionId))
                .returning({
                  id: DBSchema.institutions.id,
                  guidId: DBSchema.institutions.guidId,
                  typeId: DBSchema.institutions.typeId,
                  createdAt: DBSchema.institutions.createdAt,
                  updatedAt: DBSchema.institutions.updatedAt,
                  modifiedBy: DBSchema.institutions.modifiedBy,
                }),
            );

            // Delete existing translations
            yield* tx((client) =>
              client
                .delete(DBSchema.institutionsI18N)
                .where(
                  eq(DBSchema.institutionsI18N.dataId, params.institutionId),
                ),
            );

            // Insert new translations
            const translationsToInsert = params.institution.translations.map(
              (translation) => ({
                dataId: params.institutionId,
                ...translation,
              }),
            );

            const updatedTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.institutionsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.institutionsI18N.id,
                  locale: DBSchema.institutionsI18N.locale,
                  name: DBSchema.institutionsI18N.name,
                  description: DBSchema.institutionsI18N.description,
                  otherNames: DBSchema.institutionsI18N.otherNames,
                  acronyms: DBSchema.institutionsI18N.acronyms,
                }),
            );

            // Return the institution with its translations directly
            return {
              ...updatedInstitution,
              translations: updatedTranslations,
            };
          });
        });
      };

      const deleteInstitution = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.institutions)
            .where(eq(DBSchema.institutions.id, id))
            .returning({ id: DBSchema.institutions.id }),
        );
      });

      return {
        findAllInstitutions,
        findInstitutionById,
        createInstitution,
        updateInstitution,
        deleteInstitution,
      } as const;
    }),
  },
) {}
