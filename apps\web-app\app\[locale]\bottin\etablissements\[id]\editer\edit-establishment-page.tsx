'use client';
import { EstablishmentForm } from '@/app/[locale]/bottin/etablissements/form/establishment-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useUpdateEstablishment } from '@/hooks/bottin/establishments.hook';
import { useGetGenericById } from '@/hooks/bottin/generic-list.hook';
import type { EstablishmentFormSchema } from '@/schemas/bottin/establishment-form-schema';
import type { EstablishmentFormSectionKey } from '@/types/bottin/establishement';
import type { SupportedLocale } from '@/types/locale';
import { InstitutionEditToFormSchema } from '@rie/domain/serializers';
import * as Schema from 'effect/Schema';

type EditEstablishmentPageParams = {
  formSections: Record<EstablishmentFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionEstablishmentPage({
  formSections,
  id,
}: EditEstablishmentPageParams) {
  const {
    data: establishment,
    error,
    isPending,
  } = useGetGenericById<'establishment', 'edit'>({
    controlledListKey: 'establishment',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500"> Erreur : {error.message}</div>;
  }

  const updateEstablishment = useUpdateEstablishment();
  const onSubmit = async (data: EstablishmentFormSchema) => {
    await updateEstablishment.mutateAsync({ id, payload: data });
  };

  // Transform establishment edit data to form schema using serializer
  const formData = Schema.decodeSync(InstitutionEditToFormSchema)(
    establishment,
  ) as EstablishmentFormSchema;

  return (
    <EstablishmentForm
      defaultValues={formData}
      formSections={formSections}
      onSubmit={onSubmit}
    />
  );
}
