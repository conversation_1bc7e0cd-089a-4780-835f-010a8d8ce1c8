import { CampusNotFoundError } from '@rie/domain/errors';
import type { CampusInputSchema } from '@rie/domain/schemas';
import { CampusesRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type CampusInput = Schema.Schema.Type<typeof CampusInputSchema>;

export class CampusesServiceLive extends Effect.Service<CampusesServiceLive>()(
  'CampusesServiceLive',
  {
    dependencies: [CampusesRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const campusesRepository = yield* CampusesRepositoryLive;

      const getAllCampuses = () => {
        return Effect.gen(function* () {
          return yield* campusesRepository.findAllCampuses();
        });
      };

      const getCampusById = (id: string) => {
        return Effect.gen(function* () {
          const campus = yield* campusesRepository.findCampusById(id);
          if (!campus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }
          return campus;
        });
      };

      const createCampus = (campus: CampusInput) => {
        return Effect.gen(function* () {
          const repo = yield* CampusesRepositoryLive;
          return yield* repo.createCampus({ campus });
        });
      };

      const updateCampus = ({
        id,
        campus,
      }: { id: string; campus: CampusInput }) => {
        return Effect.gen(function* () {
          const existingCampus = yield* campusesRepository.findCampusById(id);
          if (!existingCampus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }

          const updatedCampus = yield* campusesRepository.updateCampus({
            campusId: id,
            campus,
          });

          if (!updatedCampus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }

          return updatedCampus;
        });
      };

      const deleteCampus = (id: string) => {
        return Effect.gen(function* () {
          const existingCampus = yield* campusesRepository.findCampusById(id);
          if (!existingCampus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }
          const result = yield* campusesRepository.deleteCampus(id);
          return result.length > 0;
        });
      };

      return {
        getAllCampuses,
        getCampusById,
        createCampus,
        updateCampus,
        deleteCampus,
      } as const;
    }),
  },
) {}
