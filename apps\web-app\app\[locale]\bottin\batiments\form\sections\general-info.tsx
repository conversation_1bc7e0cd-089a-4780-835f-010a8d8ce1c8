import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { CivicAddress } from '@/components/civic-campus-address/civic-address';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CIVIC_ADDRESS_DEFAULT_VALUE } from '@/constants/common';
import { useControlledListSelectsData } from '@/hooks/controlled-list/use-controlled-list-selects-data';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { BuildingFormSchema } from '@/schemas/bottin/building-form-schema';
import type { CivicAddressDTO } from '@/schemas/common-schema';
import { Heading } from '@/ui/heading';
import { Textarea } from '@/ui/textarea';
import { useTranslations } from 'next-intl';
import { useCallback } from 'react';
import { type Path, useFieldArray, useFormContext } from 'react-hook-form';
import { FiPlus, FiTrash2 } from 'react-icons/fi';

export const GeneralInfo = () => {
  const tBuilding = useTranslations(
    'buildings.form.sections.description.generalInfo',
  );
  const tBuildingFields = useTranslations(
    'buildings.form.sections.description.generalInfo.fields',
  );
  const tCommon = useTranslations('common');

  const { control, formState, setValue, watch } =
    useFormContext<BuildingFormSchema>();

  const nameFields = useTranslatedField(control, 'name');
  const aliasFields = useTranslatedField(control, 'alias');
  const nameErrorMessage = getFieldErrorMessage(formState.errors, 'name')
    ? getFieldErrorMessage(formState.errors, 'name')
    : undefined;
  const aliasErrorMessage = getFieldErrorMessage(formState.errors, 'alias')
    ? getFieldErrorMessage(formState.errors, 'alias')
    : undefined;
  const { handleOnSearchTermChange, selectsData } =
    useControlledListSelectsData(['organisation', 'campus']);

  const civicAddressFields = useFieldArray({
    control,
    name: 'civicAddresses',
  });

  const civicAddresses = watch('civicAddresses');

  const handleCivicAddressChange = useCallback(
    (basePath: Path<BuildingFormSchema>, address: CivicAddressDTO | null) => {
      setValue(basePath, address ?? CIVIC_ADDRESS_DEFAULT_VALUE.data);
    },
    [setValue],
  );

  return (
    <FormSubsection title={tBuilding('title')}>
      <FieldWithTranslations
        control={control}
        errorMessage={nameErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="name"
        fields={nameFields.fields}
        label={(locale) =>
          tBuildingFields('buildingName.label', {
            locale: tCommon(locale),
          })
        }
        maxLength={150}
        onAddTranslation={nameFields.handleAddTranslation}
        onRemoveTranslation={nameFields.handleRemoveTranslation}
        required={true}
      />
      <FieldWithTranslations
        control={control}
        errorMessage={aliasErrorMessage}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="alias"
        fields={aliasFields.fields}
        label={(locale) =>
          tBuildingFields('alias.label', {
            locale: tCommon(locale),
          })
        }
        maxLength={150}
        onAddTranslation={aliasFields.handleAddTranslation}
        onRemoveTranslation={aliasFields.handleRemoveTranslation}
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        fetchNextPage={selectsData.campus?.fetchNextPage}
        fieldLabel={tBuildingFields('campus.label')}
        fieldName="campus"
        hasNextPage={Boolean(selectsData.campus?.hasNextPage)}
        isFetching={Boolean(selectsData.campus?.isFetching)}
        isFetchingNextPage={Boolean(selectsData.campus?.isFetchingNextPage)}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.campus?.data ?? []}
        placeholder={tCommon('select')}
        required
      />
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        controlledListKey="organisation"
        fetchNextPage={selectsData.organisation?.fetchNextPage}
        fieldLabel={tBuildingFields('jurisdiction.label')}
        fieldName="jurisdiction"
        hasNextPage={Boolean(selectsData.organisation?.hasNextPage)}
        isFetching={Boolean(selectsData.organisation?.isFetching)}
        isFetchingNextPage={Boolean(
          selectsData.organisation?.isFetchingNextPage,
        )}
        onSearchChange={handleOnSearchTermChange}
        options={selectsData.organisation?.data ?? []}
        placeholder={tCommon('select')}
        required={true}
      />
      <Heading level={4}>{tBuildingFields('address.label')}</Heading>
      <div className="flex flex-col gap-y-4">
        {civicAddressFields.fields.map((field, index) => (
          <div className="flex flex-col" key={field.id}>
            <CivicAddress
              onValueChange={(address) =>
                handleCivicAddressChange(`civicAddresses.${index}`, address)
              }
              selectedAddress={civicAddresses[index]}
              required={false}
            />
            <Button
              className="self-end mt-8"
              onClick={() => civicAddressFields.remove(index)}
              type="button"
              variant="destructive"
            >
              <FiTrash2 className="mr-2 h-4 w-4" /> {tCommon('delete')}
            </Button>
          </div>
        ))}
        <Button
          className="self-end"
          onClick={() =>
            civicAddressFields.append({
              city: '',
              countryCode: '',
              fullAddress: '',
              lat: '',
              lon: '',
              placeId: '',
              postalCode: '',
              state: '',
              streetName: '',
              streetNumber: '',
            })
          }
          type="button"
        >
          <FiPlus className="mr-2 h-4 w-4" />
          {tBuilding('addAddress.label')}
        </Button>
      </div>
    </FormSubsection>
  );
};
