import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import { type I18NColumnMapper, i18nColumnMapper } from '../constants';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class UnitTypeI18nMigrationConverter extends BaseConverter {
  private unitTypeI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    unitTypeIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the unit_type
    const newUnitTypeId = unitTypeIdMappings[mysqlRecord.data_id.toString()];
    if (!newUnitTypeId) {
      throw new Error(
        `No mapping found for unit_type_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.unitTypeI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newUnitTypeId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for type_unite_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'type_unite_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No type_unite_trad INSERT statements found.');
        return;
      }

      // Load unit_type ID mappings
      const unitTypeIdMappings = await this.loadEntityIdMappings('type_unite');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, unitTypeIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      const columns = this.extractColumnNames(insertStatements[0] ?? '').map(
        (column) => i18nColumnMapper[column as I18NColumnMapper] ?? column,
      );

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'unit_types_i18n',
        'Unit Type I18n Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.unitTypeI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'type_unite_trad',
          postgres: 'unit_types_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} unit_types_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
