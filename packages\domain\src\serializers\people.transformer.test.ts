import * as Either from 'effect/Either';
import * as Schema from 'effect/Schema';
import { describe, expect, it } from 'vitest';
import { DbPersonToPersonEdit, dbPeopleToPeople } from './people.serializer';

describe('people.transformer', () => {
  describe('DbPersonToPersonEdit', () => {
    it('should decode PersonSchema to PersonEditSchema', () => {
      const rawPerson = {
        id: '123',
        guidId: 'guid-123',
        uid: 'uid-123',
        firstName: 'John',
        lastName: 'Doe',
        userId: 'user-123',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        modifiedBy: null,
        isActive: true,
        emails: [{ id: 'email-1', address: '<EMAIL>' }],
      };

      const result = Schema.decodeEither(DbPersonToPersonEdit)(rawPerson);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({
          id: '123',
          guidId: 'guid-123',
          uid: 'uid-123',
          firstName: 'John',
          lastName: 'Doe',
          userId: 'user-123',
        });
      }
    });
  });

  describe('dbPeopleToPeople', () => {
    it('should transform array of Person to PersonList[]', () => {
      const dbPeople = [
        {
          id: '123',
          guidId: 'guid-123',
          uid: 'uid-123',
          firstName: 'John',
          lastName: 'Doe',
          userId: 'user-123',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
          modifiedBy: null,
          isActive: true,
          emails: [
            { id: 'email-1', address: '<EMAIL>' },
            { id: 'email-2', address: '<EMAIL>' },
          ],
        },
      ];

      const result = dbPeopleToPeople(dbPeople, 'list');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: '123',
        firstName: 'John',
        lastName: 'Doe',
        email: ['<EMAIL>', '<EMAIL>'],
        lastUpdatedAt: '2023-01-01T00:00:00Z',
        guidId: 'guid-123',
        uid: 'uid-123',
        userId: 'user-123',
      });
    });

    it('should transform array of Person to PersonSelect[]', () => {
      const dbPeople = [
        {
          id: '456',
          guidId: 'guid-456',
          uid: 'uid-456',
          firstName: 'Jane',
          lastName: 'Smith',
          userId: 'user-456',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
          modifiedBy: null,
          isActive: true,
          emails: [{ id: 'email-3', address: '<EMAIL>' }],
        },
      ];

      const result = dbPeopleToPeople(dbPeople, 'select');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        value: '456',
        label: 'Jane Smith',
      });
    });
  });
});
