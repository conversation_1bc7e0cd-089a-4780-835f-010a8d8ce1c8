import type {
  CampusDetailSchema,
  CampusEditSchema,
  CampusInputSchema,
  CampusListSchema,
  CampusSchema,
  CampusSelectSchema,
} from '../schemas/campuses.schema';

import type * as Schema from 'effect/Schema';

export type Campus = Schema.Schema.Type<typeof CampusSchema>;
export type CampusInput = Schema.Schema.Type<typeof CampusInputSchema>;
export type CampusList = Schema.Schema.Type<typeof CampusListSchema>;
export type CampusSelect = Schema.Schema.Type<typeof CampusSelectSchema>;
export type CampusEdit = Schema.Schema.Type<typeof CampusEditSchema>;
export type CampusDetail = Schema.Schema.Type<typeof CampusDetailSchema>;
