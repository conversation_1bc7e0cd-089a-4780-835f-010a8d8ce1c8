import { AddPerson } from '@/app/[locale]/bottin/personnes/ajouter/add-person';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { personFormSections } from '@/constants/bottin/person';
import { getQueryClientOptions } from '@/constants/query-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function NewPersonPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  const formSections = await getFormSections({
    resourceName: 'people',
    sections: personFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  const controlledLists: ControlledListKey[] = [
    'local',
    'building',
    'person',
    'jobType',
  ];

  const t0 = performance.now();

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  );

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "local", "building", "person" and "jobType" took ${t1 - t0} milliseconds.`,
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AddPerson formSections={formSections} />
    </HydrationBoundary>
  );
}
