import {
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import { AccessTreeServiceLive } from '../access-tree.service';
import { UserPermissionsServiceLive } from '../user-permissions.service';
import {
  type Policy,
  type UserPermissionsError,
  type UserPermissionsRequirements,
  all,
  any,
  checkPermission,
  permissionForResource,
  resourceAccess,
} from './policy.service';

/**
 * Domain-specific policy service for equipment management.
 * This service provides composable policies that understand your business rules
 * and leverage your hierarchical permission system.
 */
export class EquipmentPoliciesLive extends Effect.Service<EquipmentPoliciesLive>()(
  'EquipmentPoliciesLive',
  {
    effect: Effect.gen(function* () {
      /**
       * Policy to check if user can read equipment information
       */
      const canReadEquipment = (
        equipmentId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        equipmentId
          ? permissionForResource('equipment', 'read', equipmentId)
          : checkPermission('equipment:read');

      /**
       * Policy to check if user can create equipment
       */
      const canCreateEquipment = checkPermission('equipment:create');

      /**
       * Policy to check if user can update specific equipment
       */
      const canUpdateEquipment = (
        equipmentId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('equipment', 'update', equipmentId);

      /**
       * Policy to check if user can delete specific equipment
       */
      const canDeleteEquipment = (
        equipmentId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        permissionForResource('equipment', 'delete', equipmentId);

      /**
       * Policy to check if user can manage equipment within an infrastructure
       * This combines infrastructure access with equipment management permissions
       */
      const canManageInfrastructureEquipment = (
        infrastructureId: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> =>
        all([
          resourceAccess('infrastructure', infrastructureId),
          checkPermission('equipment:update'),
        ]);

      /**
       * Flexible policy that allows either direct equipment permission or infrastructure management
       */
      const canModifyEquipment = (
        equipmentId: string,
        infrastructureId?: string,
      ): Policy<UserPermissionsError, UserPermissionsRequirements> => {
        const directEquipmentPermission = permissionForResource(
          'equipment',
          'update',
          equipmentId,
        );

        if (infrastructureId) {
          return any([
            directEquipmentPermission,
            canManageInfrastructureEquipment(infrastructureId),
          ] as const);
        }

        return directEquipmentPermission;
      };

      return {
        canReadEquipment,
        canCreateEquipment,
        canUpdateEquipment,
        canDeleteEquipment,
        canModifyEquipment,
      } as const;
    }),
  },
) {}

/**
 * Default layer for EquipmentPoliciesLive
 */
export const EquipmentPoliciesLayer = Layer.mergeAll(
  EquipmentPoliciesLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
);
