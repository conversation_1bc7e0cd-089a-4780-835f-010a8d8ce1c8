import { UnitNotFoundError } from '@rie/domain/errors';
import type { UnitInputSchema } from '@rie/domain/schemas';
import { dbUnitToUnit } from '@rie/domain/serializers';
import type { ResourceViewType } from '@rie/domain/types';
import { UnitsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type UnitInput = Schema.Schema.Type<typeof UnitInputSchema>;

export class UnitsServiceLive extends Effect.Service<UnitsServiceLive>()(
  'UnitsServiceLive',
  {
    dependencies: [UnitsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllUnits = () =>
        Effect.gen(function* () {
          const repo = yield* UnitsRepositoryLive;
          return yield* repo.findAllUnits();
        });

      const getUnitById = (params: { id: string; view: ResourceViewType }) =>
        Effect.gen(function* () {
          const unitsRepository = yield* UnitsRepositoryLive;
          const unit = yield* unitsRepository.findUnitById(params.id);
          if (!unit) {
            return yield* Effect.fail(new UnitNotFoundError({ id: params.id }));
          }
          return dbUnitToUnit(unit, params.view);
        });

      const createUnit = (unit: UnitInput) =>
        Effect.gen(function* () {
          const repo = yield* UnitsRepositoryLive;
          return yield* repo.createUnit({ unit });
        });

      const updateUnit = ({ id, unit }: { unit: UnitInput; id: string }) =>
        Effect.gen(function* () {
          const repo = yield* UnitsRepositoryLive;
          const existingUnit = yield* repo.findUnitById(id);
          if (!existingUnit) {
            return yield* Effect.fail(new UnitNotFoundError({ id }));
          }
          return yield* repo.updateUnit({ unitId: id, unit });
        });

      const deleteUnit = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* UnitsRepositoryLive;
          const existingUnit = yield* repo.findUnitById(id);
          if (!existingUnit) {
            return yield* Effect.fail(new UnitNotFoundError({ id }));
          }
          const result = yield* repo.deleteUnit(id);
          return result.length > 0;
        });

      return {
        getAllUnits,
        getUnitById,
        createUnit,
        updateUnit,
        deleteUnit,
      } as const;
    }),
  },
) {}
