'use client';
import { BuildingForm } from '@/app/[locale]/bottin/batiments/form/building-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useUpdateBuilding } from '@/hooks/buildings/buildings.hook';
import type { BuildingFormSectionKey } from '@/types/building';
import type { BuildingFormSchemaType } from '@rie/domain/types';

interface EditBuildingProps {
  id: string;
  initialData: BuildingFormSchemaType;
  formSections: Record<BuildingFormSectionKey, string>;
}

export const EditionBuildingPage = ({
  id,
  initialData,
  formSections,
}: EditBuildingProps) => {
  const { mutate, status } = useUpdateBuilding(); // TODO: Maybe add status and feedback to the user

  const handleOnSubmit = async (data: BuildingFormSchemaType) => {
    // No conversion needed as BuildingInputSchema = BuildingFormSchema
    await mutate({ payload: data, id });
  };

  if (status === 'pending') {
    return <LoadingResource />;
  }

  return (
    <BuildingForm
      defaultValues={initialData}
      formSections={formSections}
      onSubmit={handleOnSubmit}
    />
  );
};
