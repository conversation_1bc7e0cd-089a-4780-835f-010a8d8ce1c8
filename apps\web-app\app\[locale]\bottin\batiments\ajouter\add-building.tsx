'use client';

import { BuildingForm } from '@/app/[locale]/bottin/batiments/form/building-form';
import { buildingFormDefaultValues } from '@/constants/bottin/building';
import { useCreateBuilding } from '@/hooks/buildings/buildings.hook';
import type { SupportedLocale } from '@/types/locale';
import type { BuildingFormSchemaType } from '@rie/domain/types';

type AddBuildingProps = {
  locale: SupportedLocale;
  formSections: Record<string, string>;
};

export const AddBuilding = ({ locale, formSections }: AddBuildingProps) => {
  const { mutate } = useCreateBuilding();

  const handleOnSubmit = async (data: BuildingFormSchemaType) => {
    // No conversion needed as BuildingInputSchema = BuildingFormSchema
    await mutate(data);
  };

  return (
    <BuildingForm
      defaultValues={buildingFormDefaultValues(locale)}
      formSections={formSections}
      onSubmit={handleOnSubmit}
    />
  );
};
