import { Schema } from 'effect';
import {
  TrimmedString,
  createTranslatableFieldMaxLengthSchema,
} from './base.schema';

export const equipmentId = Schema.String.pipe(Schema.brand('EquipmentId'));
export type EquipmentId = typeof equipmentId.Type;

export const infrastructureId = Schema.String.pipe(
  Schema.brand('InfrastructureId'),
);
export type InfrastructureId = typeof infrastructureId.Type;
export const serviceOfferId = Schema.String.pipe(
  Schema.brand('ServiceOfferId'),
);
export type ServiceOfferId = typeof serviceOfferId.Type;
export const buildingId = Schema.String.pipe(Schema.brand('BuildingId'));
export type BuildingId = typeof buildingId.Type;
export const roomId = Schema.String.pipe(Schema.brand('RoomId'));
export type RoomId = typeof roomId.Type;

export const CampusAddressSchema = Schema.Struct({
  building: buildingId,
  room: roomId,
});

export const CivicAddressSchema = Schema.Struct({
  city: Schema.String.pipe(Schema.trimmed(), Schema.nonEmptyString()),
  countryCode: Schema.String.pipe(Schema.trimmed(), Schema.nonEmptyString()),
  lat: Schema.NullOr(Schema.Trimmed).annotations({
    jsonSchema: {
      type: ['string', 'null'],
      description: 'Latitude coordinate',
    },
  }),
  lon: Schema.NullOr(Schema.Trimmed).annotations({
    jsonSchema: {
      type: ['string', 'null'],
      description: 'Longitude coordinate',
    },
  }),
  placeId: Schema.String.pipe(Schema.nonEmptyString()),
  postalCode: Schema.String.pipe(Schema.nonEmptyString()),
  state: Schema.String.pipe(Schema.trimmed(), Schema.nonEmptyString()),
  street1: Schema.String.pipe(Schema.trimmed(), Schema.nonEmptyString()),
  street2: Schema.NullOr(Schema.Trimmed).annotations({
    jsonSchema: {
      type: ['string', 'null'],
      description: 'Secondary street address',
    },
  }),
});

// Base schema for service offer address
const AddressSchema = Schema.Union(CampusAddressSchema, CivicAddressSchema);
export const AddressSchemaWithType = Schema.Union(
  CampusAddressSchema.pipe(Schema.attachPropertySignature('kind', 'campus')),
  CivicAddressSchema.pipe(Schema.attachPropertySignature('kind', 'civic')),
);

export const ServiceOfferBaseSchema = Schema.Struct({
  isForClinicalResearch: Schema.Boolean,
  highlightService: Schema.Boolean,
  createdBy: Schema.String,
  createdAt: Schema.String,
  updatedAt: Schema.String,
});

export const ServiceOfferRawSchema = Schema.Struct({
  ...ServiceOfferBaseSchema.fields,
  id: Schema.NonEmptyString,
  translations: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.NullOr(Schema.String.pipe(Schema.trimmed())),
      description: Schema.NullOr(Schema.String.pipe(Schema.trimmed())),
      serviceConditions: Schema.NullOr(Schema.String.pipe(Schema.trimmed())),
    }),
  ),
});

// Service Offer for API responses (with resolved translations)
const ServiceOfferTransformedFieldsSchema = Schema.Struct({
  name: Schema.String,
  description: Schema.String.pipe(Schema.trimmed()),
  serviceConditions: Schema.String.pipe(Schema.trimmed()),
  equipmentIds: Schema.Array(equipmentId),
  address: AddressSchemaWithType,
});

export const ServiceOfferResponseSchema = Schema.Struct({
  id: serviceOfferId,
  ...ServiceOfferBaseSchema.fields,
  ...ServiceOfferTransformedFieldsSchema.fields,
});

const ServiceOfferCampusAddressSchema = Schema.Struct({
  kind: Schema.Literal('campus'),
  campusAddressId: Schema.String,
});

const ServiceOfferCivicAddressSchema = Schema.Struct({
  kind: Schema.Literal('civic'),
  civicAddressId: Schema.String,
});

const AddressCreateInputSchema = Schema.Union(
  ServiceOfferCampusAddressSchema,
  ServiceOfferCivicAddressSchema,
);

export const ServiceOfferCreateInputSchema = Schema.extend(
  ServiceOfferBaseSchema,
  AddressCreateInputSchema,
);

// Service Offer for creation and update (with i18n fields)
export const CreateServiceOfferSchema = Schema.Struct({
  ...ServiceOfferBaseSchema.fields,
  name: createTranslatableFieldMaxLengthSchema({
    field: { maxLength: 150, maxLengthErrorMessage: 'Name ' },
    required: true,
    missingTranslationMessage: 'At least one translation',
  }),
  description: createTranslatableFieldMaxLengthSchema({
    field: { maxLength: 1500, maxLengthErrorMessage: 'Name ' },
    required: false,
  }),
  serviceConditions: createTranslatableFieldMaxLengthSchema({
    field: {
      maxLength: 1500,
      maxLengthErrorMessage: 'Service conditions cannot  ',
    },
    required: false,
  }),
  equipmentIds: Schema.Array(TrimmedString.pipe(Schema.nonEmptyString())),
  address: AddressSchema,
});

// Types
export type ServiceOfferResponseDTO = Schema.Schema.Type<
  typeof ServiceOfferResponseSchema
>;
export type ServiceOfferRawDTO = Schema.Schema.Type<
  typeof ServiceOfferRawSchema
>;
export type ServiceOfferCreateInputDTO = Schema.Schema.Type<
  typeof ServiceOfferCreateInputSchema
>;
export type CreateServiceOfferDTO = Schema.Schema.Type<
  typeof CreateServiceOfferSchema
>;
export type UpdateServiceOfferDTO = CreateServiceOfferDTO;
export type ServiceOfferAddressDTO = Schema.Schema.Type<typeof AddressSchema>;
