import { DBSchema } from '@rie/db-schema';
import type { FundingProjectInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class FundingProjectsRepositoryLive extends Effect.Service<FundingProjectsRepositoryLive>()(
  'FundingProjectsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const db = yield* PgDatabaseLive;

      // — Fetch all projects
      const findAllFundingProjects = db.makeQuery((exec) =>
        exec((client) =>
          client.query.fundingProjects.findMany({
            columns: {
              id: true,
              holderId: true,
              typeId: true,
              fciId: true,
              synchroId: true,
              obtainingYear: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                },
              },
            },
          }),
        ),
      );

      // — Fetch one
      const findFundingProjectById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.fundingProjects.findFirst({
            where: eq(DBSchema.fundingProjects.id, id),
            columns: {
              id: true,
              holderId: true,
              typeId: true,
              fciId: true,
              synchroId: true,
              obtainingYear: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                },
              },
            },
          }),
        ),
      );

      const createFundingProject = (params: {
        project: FundingProjectInput;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            const [createdProject] = yield* tx((client) =>
              client
                .insert(DBSchema.fundingProjects)
                .values(params.project)
                .returning({
                  id: DBSchema.fundingProjects.id,
                  holderId: DBSchema.fundingProjects.holderId,
                  typeId: DBSchema.fundingProjects.typeId,
                  fciId: DBSchema.fundingProjects.fciId,
                  synchroId: DBSchema.fundingProjects.synchroId,
                  obtainingYear: DBSchema.fundingProjects.obtainingYear,
                  endDate: DBSchema.fundingProjects.endDate,
                  createdAt: DBSchema.fundingProjects.createdAt,
                  updatedAt: DBSchema.fundingProjects.updatedAt,
                  modifiedBy: DBSchema.fundingProjects.modifiedBy,
                }),
            );

            if (!createdProject) {
              return yield* Effect.fail(
                new Error('Failed to create funding project'),
              );
            }

            const translationsToInsert = params.project.translations.map(
              (translation) => ({
                dataId: createdProject.id,
                ...translation,
              }),
            );

            const createdTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.fundingProjectsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.fundingProjectsI18N.id,
                  locale: DBSchema.fundingProjectsI18N.locale,
                  name: DBSchema.fundingProjectsI18N.name,
                  description: DBSchema.fundingProjectsI18N.description,
                }),
            );

            // Return the project with its translations directly
            return {
              ...createdProject,
              translations: createdTranslations,
            };
          });
        });
      };

      const updateFundingProject = (params: {
        projectId: string;
        project: FundingProjectInput;
      }) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the project
            const [updatedProject] = yield* tx((client) =>
              client
                .update(DBSchema.fundingProjects)
                .set(params.project)
                .where(eq(DBSchema.fundingProjects.id, params.projectId))
                .returning({
                  id: DBSchema.fundingProjects.id,
                  holderId: DBSchema.fundingProjects.holderId,
                  typeId: DBSchema.fundingProjects.typeId,
                  fciId: DBSchema.fundingProjects.fciId,
                  synchroId: DBSchema.fundingProjects.synchroId,
                  obtainingYear: DBSchema.fundingProjects.obtainingYear,
                  endDate: DBSchema.fundingProjects.endDate,
                  createdAt: DBSchema.fundingProjects.createdAt,
                  updatedAt: DBSchema.fundingProjects.updatedAt,
                  modifiedBy: DBSchema.fundingProjects.modifiedBy,
                }),
            );

            const translationsToInsert = params.project.translations.map(
              (translation) => ({
                dataId: params.projectId,
                ...translation,
              }),
            );

            const updatedTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.fundingProjectsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.fundingProjectsI18N.id,
                  locale: DBSchema.fundingProjectsI18N.locale,
                  name: DBSchema.fundingProjectsI18N.name,
                  description: DBSchema.fundingProjectsI18N.description,
                }),
            );
            // Return the project with its translations directly
            return {
              ...updatedProject,
              translations: updatedTranslations,
            };
          });
        });
      };

      const deleteFundingProject = (id: string) => {
        return db.transaction((tx) => {
          return Effect.gen(function* () {
            // First delete all translations for this project
            yield* tx((client) =>
              client
                .delete(DBSchema.fundingProjectsI18N)
                .where(eq(DBSchema.fundingProjectsI18N.dataId, id)),
            );

            return yield* tx((client) =>
              client
                .delete(DBSchema.fundingProjects)
                .where(eq(DBSchema.fundingProjects.id, id))
                .returning({ id: DBSchema.fundingProjects.id }),
            );
          });
        });
      };

      return {
        findAllFundingProjects,
        findFundingProjectById,
        createFundingProject,
        updateFundingProject,
        deleteFundingProject,
      } as const;
    }),
  },
) {}
