import {
  PermissionAlreadyExistsError,
  PermissionNotFoundError,
} from '@rie/domain/errors';
import type { PermissionInputSchema } from '@rie/domain/schemas';
import {
  dbPermissionToPermission,
  dbPermissionsToPermissions,
} from '@rie/domain/serializers';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { PermissionsRepositoryLive } from '@rie/repositories';
import { Effect } from 'effect';
import type * as Schema from 'effect/Schema';

type PermissionInput = Schema.Schema.Type<typeof PermissionInputSchema>;

interface UpdatePermissionParams extends PermissionInput {
  id: string;
}

export class PermissionsServiceLive extends Effect.Service<PermissionsServiceLive>()(
  'PermissionsServiceLive',
  {
    dependencies: [PermissionsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllPermissions = ({ view }: CollectionViewParamType) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;
          const permissions = yield* permissionsRepository.findAllPermissions();
          const t1 = performance.now();

          console.log(
            `Call to getAllPermissions took ${t1 - t0} milliseconds.`,
          );

          return dbPermissionsToPermissions(permissions, view);
        });
      };

      /**
       * Get a permission by ID
       */
      const getPermissionById = (params: {
        id: string;
        view: ResourceViewType;
      }) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;
          const permission = yield* permissionsRepository.findPermissionById(
            params.id,
          );

          if (!permission) {
            return yield* Effect.fail(
              new PermissionNotFoundError({ id: params.id }),
            );
          }

          const t1 = performance.now();
          console.log(
            `Call to getPermissionById took ${t1 - t0} milliseconds.`,
          );

          return dbPermissionToPermission(permission, params.view);
        });
      };

      const createPermission = (input: PermissionInput) => {
        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;

          // Check if permission already exists
          const existingPermission =
            yield* permissionsRepository.checkPermissionExists({
              domain: input.domain,
              action: input.action,
            });

          if (existingPermission) {
            return yield* Effect.fail(
              new PermissionAlreadyExistsError({
                domain: input.domain,
                action: input.action,
              }),
            );
          }

          const [createdPermission] =
            yield* permissionsRepository.createPermission(input);

          return createdPermission;
        });
      };

      const updatePermission = (params: UpdatePermissionParams) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;

          // Check if permission exists
          const permission = yield* permissionsRepository.findPermissionById(
            params.id,
          );
          if (!permission) {
            return yield* Effect.fail(
              new PermissionNotFoundError({ id: params.id }),
            );
          }

          // Check if another permission with the same domain/action already exists (excluding this one)
          const existingPermission =
            yield* permissionsRepository.checkPermissionExists({
              action: params.action,
              domain: params.domain,
            });

          if (existingPermission && existingPermission.id !== params.id) {
            return yield* Effect.fail(
              new PermissionAlreadyExistsError({
                action: params.action,
                domain: params.domain,
              }),
            );
          }

          const [updatedPermission] =
            yield* permissionsRepository.updatePermission({
              id: params.id,
              action: params.action,
              domain: params.domain,
            });

          const t1 = performance.now();
          console.log(`Call to updatePermission took ${t1 - t0} milliseconds.`);

          return updatedPermission;
        });
      };

      const deletePermission = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;

          const existingPermission =
            yield* permissionsRepository.findPermissionById(id);

          if (!existingPermission) {
            return yield* Effect.fail(new PermissionNotFoundError({ id }));
          }

          const result = yield* permissionsRepository.deletePermission(id);
          const t1 = performance.now();

          console.log(`Call to deletePermission took ${t1 - t0} milliseconds.`);

          return result.length > 0;
        });
      };

      return {
        getAllPermissions,
        getPermissionById,
        createPermission,
        updatePermission,
        deletePermission,
      } as const;
    }),
  },
) {}
