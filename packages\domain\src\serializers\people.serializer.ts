import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  PersonEditSchema,
  PersonListSchema,
  PersonSchema,
  PersonSelectSchema,
} from '../schemas';
import type {
  CollectionViewType,
  Person,
  PersonList,
  PersonSelect,
  ResourceViewType,
} from '../types';

// Schema transformer for converting database person to list view
export const DbPersonToPersonList = Schema.transformOrFail(
  PersonSchema,
  PersonListSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            lastName: raw.lastName,
            firstName: raw.firstName,
            email: raw.emails.map((email) => email.address),
            lastUpdatedAt: raw.updatedAt,
            guidId: raw.guidId,
            uid: raw.uid,
            userId: raw.userId,
            isActive: raw.isActive,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse person for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database person to select view
export const DbPersonToPersonSelect = Schema.transformOrFail(
  PersonSchema,
  PersonSelectSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            value: raw.id,
            label: `${raw.firstName} ${raw.lastName}`,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse person for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database person to edit format
export const DbPersonToPersonEdit = Schema.transformOrFail(
  PersonSchema,
  PersonEditSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            guidId: raw.guidId,
            uid: raw.uid,
            firstName: raw.firstName,
            lastName: raw.lastName,
            userId: raw.userId,
            isActive: raw.isActive,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse person for edit format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of Person to PersonSelect[]
export const dbPeopleToPersonSelect = (dbPeople: Person[]): PersonSelect[] => {
  return dbPeople.map((dbPerson) => {
    return {
      value: dbPerson.id,
      label: `${dbPerson.firstName} ${dbPerson.lastName}`,
    };
  });
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbPeopleToPeople = (
  dbPeople: Person[],
  view: CollectionViewType,
): PersonList[] | PersonSelect[] => {
  return view === 'select'
    ? dbPeople.map((person) =>
      Schema.decodeUnknownSync(DbPersonToPersonSelect)(person),
    )
    : dbPeople.map((person) =>
      Schema.decodeUnknownSync(DbPersonToPersonList)(person),
    );
};

// New serializer function for Person with view parameter
export const dbPersonToPerson = (dbPerson: Person, view: ResourceViewType) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbPersonToPersonEdit)(dbPerson)
    : Schema.decodeUnknownSync(DbPersonToPersonList)(dbPerson);
};

// Schema transformer for converting PersonEdit to form schema (Zod-compatible)
export const PersonEditToFormSchema = Schema.transformOrFail(
  PersonEditSchema,
  Schema.Struct({
    id: Schema.optional(Schema.String),
    phones: Schema.Array(
      Schema.Struct({
        phone: Schema.optional(Schema.String),
      }),
    ),
    affiliatedField: Schema.Struct({
      affiliationSection: Schema.Struct({
        jobTitle: Schema.Array(
          Schema.Struct({
            locale: Schema.String,
            value: Schema.String,
          }),
        ),
        jobType: Schema.Struct({
          label: Schema.String,
          value: Schema.String,
        }),
        affiliation: Schema.Struct({
          label: Schema.String,
          value: Schema.String,
        }),
        affiliationType: Schema.String,
      }),
    }),
    emails: Schema.Array(
      Schema.Struct({
        email: Schema.optional(Schema.String),
      }),
    ),
    addresses: Schema.Array(Schema.Unknown),
  }),
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            phones: [{ phone: '' }], // TODO: Get person phones
            affiliatedField: {
              affiliationSection: {
                jobTitle: [{ locale: 'fr', value: '' }], // TODO: Get job title
                jobType: { label: '', value: '' }, // TODO: Get job type
                affiliation: { label: '', value: '' }, // TODO: Get affiliation
                affiliationType: 'etablissement', // Default type
              },
            },
            emails: [{ email: '' }], // TODO: Get person emails
            addresses: [], // TODO: Get person addresses
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to convert person edit to form schema',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);
