import type {
  DbPermissionAction,
  DbPermissionDomain,
  DbPermissionInput,
} from '@rie/db-schema/entity-types';
import { permissions } from '@rie/db-schema/schemas';
import { PgDatabaseLive } from '@rie/postgres-db';
import { and, eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

/**
 * Update a permission
 */
interface UpdatePermissionParams extends DbPermissionInput {
  id: string;
}

export class PermissionsRepositoryLive extends Effect.Service<PermissionsRepositoryLive>()(
  'PermissionsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      /**
       * Find all permissions
       */
      const findAllPermissions = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.permissions.findMany({
            columns: {
              id: true,
              domain: true,
              action: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        );
      });

      /**
       * Find a permission by ID
       */
      const findPermissionById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.permissions.findFirst({
            where: eq(permissions.id, id),
            columns: {
              id: true,
              domain: true,
              action: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        );
      });

      /**
       * Check if a permission with the given domain and action already exists
       */
      const checkPermissionExists = dbClient.makeQuery(
        (
          execute,
          data: { domain: DbPermissionDomain; action: DbPermissionAction },
        ) => {
          return execute((client) =>
            client.query.permissions.findFirst({
              where: and(
                eq(permissions.domain, data.domain),
                eq(permissions.action, data.action),
              ),
              columns: {
                id: true,
              },
            }),
          );
        },
      );

      /**
       * Create a new permission
       */
      const createPermission = ({ domain, action }: DbPermissionInput) => {
        return dbClient.makeQuery((execute) => {
          return execute((client) =>
            client.insert(permissions).values({ domain, action }).returning({
              id: permissions.id,
              domain: permissions.domain,
              action: permissions.action,
              createdAt: permissions.createdAt,
              updatedAt: permissions.updatedAt,
            }),
          );
        })();
      };

      /**
       * Update a permission
       */
      const updatePermission = ({
        id,
        domain,
        action,
      }: UpdatePermissionParams) => {
        return dbClient.makeQuery((execute) => {
          return execute((client) =>
            client
              .update(permissions)
              .set({
                domain,
                action,
              })
              .where(eq(permissions.id, id))
              .returning({
                id: permissions.id,
                domain: permissions.domain,
                action: permissions.action,
                createdAt: permissions.createdAt,
                updatedAt: permissions.updatedAt,
              }),
          );
        })();
      };

      /**
       * Delete a permission
       * This will cascade delete all related data due to foreign key constraints
       */
      const deletePermission = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(permissions)
            .where(eq(permissions.id, id))
            .returning({ id: permissions.id }),
        );
      });

      return {
        findAllPermissions,
        findPermissionById,
        checkPermissionExists,
        createPermission,
        updatePermission,
        deletePermission,
      } as const;
    }),
  },
) {}
